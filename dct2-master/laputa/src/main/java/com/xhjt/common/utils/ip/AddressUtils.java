package com.xhjt.common.utils.ip;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.http.HttpUtils;
import com.xhjt.framework.config.ProjectConfig;

/**
 * 获取地址类
 * 
 * <AUTHOR>
 */
public class AddressUtils
{
    private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);

    public static final String IP_URL = "http://ip.taobao.com/service/getIpInfo.php";

    public static String getRealAddressByIP(String ip)
    {
        String address = "XX XX";
        // 内网不查询
        if (IpUtils.internalIp(ip))
        {
            return "内网IP";
        }
        if (ProjectConfig.isAddressEnabled())
        {
            String rspStr = HttpUtils.sendPost(IP_URL, "ip=" + ip);
            if (StringUtils.isEmpty(rspStr))
            {
                log.error("获取地理位置异常 {}", ip);
                return address;
            }
            JSONObject obj = JSONObject.parseObject(rspStr);
            JSONObject data = obj.getObject("data", JSONObject.class);
            // 获取城市信息失败，需要判空
            if (data != null) {
                String region = data.getString("region");
                String city = data.getString("city");
                address = region + " " + city;
            }
        }
        return address;
    }
}
