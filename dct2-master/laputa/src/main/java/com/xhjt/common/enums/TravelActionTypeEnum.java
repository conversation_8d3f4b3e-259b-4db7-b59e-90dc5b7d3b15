package com.xhjt.common.enums;

/**
 * 行程动作节点--动作类型
 *
 * <AUTHOR>
 */
public enum TravelActionTypeEnum {
    /**
     * 进范围(首次)
     */
    INTO_THE_SCOPE(1, "进范围(首次)"),
    STOPPED_IN_SCOPE(2, "站内停船"),
    START_UP_IN_SCOPE(3, "站内启动"),
    OUT_THE_SCOPE(4, "出范围(首次)"),
    STOPPED_OUT_SCOPE(5, "意外停船"),
    START_UP_OUT_SCOPE(6, "意外停船启动");

    private final Integer value;
    private final String alias;

    TravelActionTypeEnum(Integer value, String alias) {
        this.value = value;
        this.alias = alias;
    }

    public Integer getValue() {
        return value;
    }

    public String getAlias() {
        return alias;
    }
}
