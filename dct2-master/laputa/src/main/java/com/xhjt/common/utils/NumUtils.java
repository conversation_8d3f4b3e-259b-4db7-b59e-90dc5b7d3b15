package com.xhjt.common.utils;

import com.google.common.collect.Lists;

import java.nio.ByteBuffer;
import java.util.List;

/**
 * 数值转换
 *
 * <AUTHOR>
 */
public class NumUtils {

    private static ByteBuffer buffer = ByteBuffer.allocate(8);

    /**
     * int转byte数组
     *
     * @param i
     * @return
     */
    public static byte[] int2ByteArray(int i) {
        byte[] result = new byte[4];
        //由高位到低位
        result[0] = (byte) ((i >> 24) & 0xFF);
        result[1] = (byte) ((i >> 16) & 0xFF);
        result[2] = (byte) ((i >> 8) & 0xFF);
        result[3] = (byte) (i & 0xFF);
        return result;
    }

    /**
     * list转byte数组
     *
     * @param list
     * @return
     */
    public static byte[] list2ByteArray(List<Integer> list) {
        byte[] result = new byte[list.size() * 4];

        int offset = 0;
        for (int i = 0; i < list.size(); i++) {
            byte[] bytes = int2ByteArray(list.get(i));
            // 合并到结果集
            mergeBytes(result, offset, bytes);
            offset += 4;
        }

        return result;
    }

    /**
     * byte[]转int
     *
     * @param bytes
     * @return
     */
    public static int byteArray2Int(byte[] bytes) {
        int value = 0;
        //由高位到低位
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            //往高位游
            value += (bytes[i] & 0x000000FF) << shift;
        }

        return value;
    }

    /**
     * byte数组转list（仅对int类型有效）
     * @param bytes
     * @return
     */
    public static List<Integer> byteArray2List(byte[] bytes) {
        List<Integer> list = Lists.newArrayList();

        int count = bytes.length / 4;
        for (int i = 0; i < count; i++) {
            byte[] b = new byte[4];
            System.arraycopy(bytes, 4 * i, b, 0, 4);
            list.add(byteArray2Int(b));
        }

        return list;
    }

    /**
     * byte 数组与 long 的相互转换
     */
    public static byte[] longToBytes(long x) {
        buffer.putLong(0, x);
        return buffer.array();
    }

    /**
     * byte 数组与 long 的相互转换
     */
    public static long bytesToLong(byte[] bs) throws Exception {
        int bytes = bs.length;
        if (bytes > 1) {
            if ((bytes % 2) != 0 || bytes > 8) {
                throw new Exception("not support");
            }
        }
        switch (bytes) {
            case 0:
                return 0;
            case 1:
                return (long) ((bs[0] & 0xff));
            case 2:
                return (long) ((bs[0] & 0xff) << 8 | (bs[1] & 0xff));
            case 4:
                return (long) ((bs[0] & 0xffL) << 24 | (bs[1] & 0xffL) << 16 | (bs[2] & 0xffL) << 8 | (bs[3] & 0xffL));
            case 8:
                return (long) ((bs[0] & 0xffL) << 56 | (bs[1] & 0xffL) << 48 | (bs[2] & 0xffL) << 40 | (bs[3] & 0xffL) << 32 |
                        (bs[4] & 0xffL) << 24 | (bs[5] & 0xffL) << 16 | (bs[6] & 0xffL) << 8 | (bs[7] & 0xffL));
            default:
                throw new Exception("not support");
        }
    }

    /**
     * 把byte数组合并到总数组
     *
     * @param total  总数组
     * @param offset 下标
     * @param target 需要合并的byte数组
     * @return
     */
    public static int mergeBytes(byte[] total, int offset, byte[] target) {
        for (int i = 0; i < target.length; i++) {
            total[offset + i] = target[i];
        }
        return offset + target.length;
    }

    /**
     * 把byte合并到byte数组
     *
     * @param total  总数组
     * @param offset 下标
     * @param target 需要合并的byte
     * @return
     */
    public static int mergeByte(byte[] total, int offset, byte target) {
        total[offset] = target;
        return offset + 1;
    }

    /**
     * 从byte数组中获取一定长度的byte数组
     *
     * @param total  总数组
     * @param offset 下标
     * @param len    获取的长度
     * @return
     */
    public static byte[] splitBytes(byte[] total, int offset, int len) {
        byte[] data = new byte[len];

        for (int i = 0; i < len; i++) {
            data[i] = total[offset + i];
        }

        return data;
    }

    /**
     * 从byte数组中获取一定长度的byte
     *
     * @param total  总数组
     * @param offset 下标
     * @return
     */
    public static byte splitByte(byte[] total, int offset) {
        return total[offset];
    }
}
