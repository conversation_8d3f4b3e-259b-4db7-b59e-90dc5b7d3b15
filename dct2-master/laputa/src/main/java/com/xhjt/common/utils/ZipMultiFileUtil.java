package com.xhjt.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Created by chenmingyong on 2021/11/23.
 */
public class ZipMultiFileUtil {
    public final static Logger logger = LoggerFactory.getLogger(ZipMultiFileUtil.class);
    public static int zipFiles(List<File> srcFiless, File zipFile) {

        int row =0;
        BufferedInputStream bis = null;
        // 创建 FileInputStream 对象
        FileInputStream fileInputStream = null;
        // 实例化 FileOutputStream 对象
        FileOutputStream fileOutputStream = null;
        // 实例化 ZipOutputStream 对象
        ZipOutputStream zipOutputStream = null;
        // 创建 ZipEntry 对象
        ZipEntry zipEntry = null;
        try {

            if (srcFiless.size() != 0) {
                logger.info(srcFiless.size()+"------所有图片得数量22222222");
                // 判断压缩后的文件存在不，不存在则创建
                if (!zipFile.exists()) {
                    zipFile.createNewFile();
                } else {
                    zipFile.delete();
                    zipFile.createNewFile();
                }
                fileOutputStream = new FileOutputStream(zipFile);
                zipOutputStream = new ZipOutputStream(fileOutputStream);
                // 创建 ZipEntry 对象
//                ZipEntry zipEntry = null;

                forInsertFile(srcFiless,bis,fileInputStream,fileOutputStream,zipOutputStream,zipEntry);
//                for (int i = 0; i < srcFiles.length; i++) {
//
//                    srcFiles.
//                }
                row = 1;
                logger.info(row+"------10101010101");

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {

            try {
//                //关闭流
//                TimeUnit.SECONDS.sleep(10);
                if (null != bis) bis.close();
                zipOutputStream.closeEntry();
                if (null != zipOutputStream) zipOutputStream.close();
                if (null != fileOutputStream) fileOutputStream.close();
                if (null != fileInputStream) fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }
        return row;
    }

    private static void forInsertFile(List<File> srcFiless,BufferedInputStream bis, FileInputStream fileInputStream, FileOutputStream fileOutputStream, ZipOutputStream zipOutputStream,ZipEntry zipEntry) throws IOException {
        // 遍历源文件数组
        for(File f:srcFiless){
            // 将源文件数组中的当前文件读入 FileInputStream 流中
            fileInputStream = new FileInputStream(f);
            // 实例化 ZipEntry 对象，源文件数组中的当前文件
             zipEntry = new ZipEntry(f.getName());
            zipOutputStream.putNextEntry(zipEntry);
            // 该变量记录每次真正读的字节个数
            int len;
            // 定义每次读取的字节数组
            byte[] buffer = new byte[1024];
            while ((len = fileInputStream.read(buffer)) > 0) {
                zipOutputStream.write(buffer, 0, len);
            }
         }
    }


    /**
     * 将存放在sourceFilePath目录下的源文件，打包成fileName名称的zip文件，并存放到zipFilePath路径下
     *
     * @param sourceFilePath :待压缩的文件路径
     * @param zipFilePath    :压缩后存放路径
     * @param fileName       :压缩后文件的名称
     * @return
     */
    public static boolean fileToZip(String sourceFilePath, String zipFilePath, String fileName) {
        boolean flag = false;
        File sourceFile = new File(sourceFilePath);
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        ZipOutputStream zos = null;

        if (sourceFile.exists() == false) {
            logger.info("待压缩的文件目录：" + sourceFilePath + "不存在");
            sourceFile.mkdir(); // 新建目录
        }
        try {
            File zipFile = new File(zipFilePath + "/" + fileName);
            if (zipFile.exists()) {
                logger.info(zipFilePath + "目录下存在名字为:" + fileName + ".zip" + "打包文件.");
            } else {
                File[] sourceFiles = sourceFile.listFiles();
                if (null == sourceFiles || sourceFiles.length < 1) {
                    logger.info("待压缩的文件目录：" + sourceFilePath + "里面不存在文件，无需压缩.");
                } else {
                    fos = new FileOutputStream(zipFile);
                    zos = new ZipOutputStream(new BufferedOutputStream(fos));
                    byte[] bufs = new byte[1024 * 10];
                    for (int i = 0; i < sourceFiles.length; i++) {
                        //创建ZIP实体，并添加进压缩包
                        ZipEntry zipEntry = new ZipEntry(sourceFiles[i].getName());
                        zos.putNextEntry(zipEntry);
                        //读取待压缩的文件并写进压缩包里
                        fis = new FileInputStream(sourceFiles[i]);
                        bis = new BufferedInputStream(fis, 1024 * 10);
                        int read = 0;
                        while ((read = bis.read(bufs, 0, 1024 * 10)) != -1) {
                            zos.write(bufs, 0, read);
                        }
                    }
                    flag = true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            //关闭流
            try {
                if (null != bis) bis.close();
                if (null != zos) zos.close();
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }
        return flag;
    }

    public static void main(String[] args) {
        //创建父级目录 用于存放分区（分区中存放多个分类）
        String fatherPath = "E:\\xhjt\\20211025";
        File fatherFile = new File(fatherPath);
        File fatherFile2 = new File("E:\\xhjt\\20211024");
        try {
            if (!fatherFile.exists()) {
                fatherFile.mkdir();
            } else {
                fatherFile.delete();
                fatherFile.mkdir();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //创建list 存放图片
        File[] fileLists = fatherFile.listFiles();
        File[] fileLists2 = fatherFile2.listFiles();
        List<File> fileList = new ArrayList<>();
        for (int i = 0; i < fileLists.length; i++) {
            fileList.add(fileLists[i]);
        }
        for (int i = 0; i < fileLists2.length; i++) {
            fileList.add(fileLists2[i]);
        }
        //遍历存储图片地址
        String url = "E:/xhjt" + "/" + 1 + ".zip";
        File zipFile = new File(url);
        // 调用压缩方法
        ZipMultiFileUtil.zipFiles(fileList, zipFile);


    }
}
