package com.xhjt.project.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xhjt.common.constant.HttpStatus;
import com.xhjt.common.constant.UserConstants;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.domain.SysDictData;
import com.xhjt.project.system.domain.SysModule;
import com.xhjt.project.system.service.ISysDeptService;
import com.xhjt.project.system.service.ISysDictDataService;
import com.xhjt.project.system.service.ISysModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.server.HandshakeHandler;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模板管理
 */
@RestController
@RequestMapping("/system/module")
public class SysModuleController extends BaseController {
    @Autowired
    private ISysModuleService moduleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysDictDataService dictDataService;

    @PreAuthorize("@ss.hasPermi('system:module:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysModule sysModule) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        sysModule.setDeptId(deptId);
        startPage();
        List<HashMap> list = moduleService.getModuleShow(sysModule);
        for (HashMap map : list){
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String s = format.format(map.get("createTime"));
            map.put("createTime",s);
            SysDictData dictData = new SysDictData();
            dictData.setDictType("module_type");
            List<SysDictData>  dictList = dictDataService.selectDictDataList(dictData);
            for(SysDictData sysDictData : dictList){
                if(Integer.parseInt(sysDictData.getDictValue()) == Integer.parseInt(map.get("type")+"")){
                    map.put("name",sysDictData.getDictLabel());
                }
            }
        }
        //分页
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 获取模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:module:query')")
    @GetMapping("/list/{type}")
    public TableDataInfo listByType(@PathVariable Integer type) {
        SysModule sysModule = new SysModule();
        sysModule.setType(type);
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        sysModule.setDeptId(deptId);
        startPage();
        List<HashMap> list = moduleService.getModuleShow(sysModule);
        for (HashMap map : list){
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String s = format.format(map.get("createTime"));
            map.put("createTime",s);
            SysDictData dictData = new SysDictData();
            dictData.setDictType("module_type");
            List<SysDictData>  dictList = dictDataService.selectDictDataList(dictData);
            for(SysDictData sysDictData : dictList){
                if(Integer.parseInt(sysDictData.getDictValue()) == Integer.parseInt(map.get("type")+"")){
                    map.put("name",sysDictData.getDictLabel());
                }
            }
        }
        //分页
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 新增模板时获取表单
     */
    @PreAuthorize("@ss.hasPermi('system:module:query')")
    @RequestMapping("/addList/{type}")
    public AjaxResult addForm(@PathVariable  int type) {
        SysModule sysModule = new SysModule();
        sysModule.setType(type);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<SysModule> list = moduleService.selectModuleListByType(sysModule);
        HashMap<String,Object> map = new HashMap<>();
        map.put("data",list);
        map.put("comId",sysDept.getParentId());
        return AjaxResult.success(map);
    }

    /**
     * 修改模板时获取表单
     */
    @PreAuthorize("@ss.hasPermi('system:module:query')")
    @RequestMapping("/updateForm/{type}/{comId}")
    public AjaxResult updateForm(@PathVariable  int type,@PathVariable  Long comId) {
        SysModule sysModule = new SysModule();
        sysModule.setType(type);

        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        String comIds = comId + ","+"100";
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByEdit(sysModule);
        HashMap<String,Object> map = new HashMap<>();
        map.put("data",list);
        map.put("comId",sysDept.getParentId());
        return AjaxResult.success(map);
    }

    /**
     * 新增模板确定
     */
    @PreAuthorize("@ss.hasPermi('system:module:add')")
    @Log(title = "新增模板", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@Validated @RequestBody SysModule moduleInput) {
        List<SysModule> moduleList = moduleInput.getModuleList();
        //判断新增的是否重复
        List<String> stringList = moduleList.stream().map(SysModule::getLabel)
                .collect(Collectors.toList());
        List<String> stringList2 = moduleList.stream().map(SysModule::getProperty)
                .collect(Collectors.toList());
        long count = stringList.stream().distinct().count();
        long count2 = stringList2.stream().distinct().count();
        if (stringList.size() != count) {
            return AjaxResult.error("列名称重复");
        } else if(stringList2.size() != count2){
            return AjaxResult.error("列编码重复");
        }
        int type = moduleInput.getType();
        Long comId = moduleInput.getComId();
        for (SysModule sysModule : moduleList) {
            sysModule.setType(type);
            String comIds = comId + ","+"100";
            sysModule.setComIds(comIds);
            if (UserConstants.NOT_UNIQUE.equals(moduleService.checkModuleLabelUnique(sysModule))) {
                return AjaxResult.error("列名称已存在");
            } else if (UserConstants.NOT_UNIQUE.equals(moduleService.checkModulePropertyUnique(sysModule))) {
                return AjaxResult.error("列编码已存在");
            }
        }
        int row =  moduleService.insertModule(moduleInput);
        return toAjax(row);
    }

    /**
     * 修改模板
     */
    @PreAuthorize("@ss.hasPermi('system:module:edit')")
    @Log(title = "修改模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysModule moduleInput) {
        List<SysModule> moduleList = moduleInput.getModuleList();
        //判断修改的是否重复
        List<String> stringList = moduleList.stream().map(SysModule::getLabel)
                .collect(Collectors.toList());
        List<String> stringList2 = moduleList.stream().map(SysModule::getProperty)
                .collect(Collectors.toList());
        long count = stringList.stream().distinct().count();
        long count2 = stringList2.stream().distinct().count();
        if (stringList.size() != count) {
            return AjaxResult.error("列名称重复");
        } else if(stringList2.size() != count2){
            return AjaxResult.error("列编码重复");
        }
        int type = moduleInput.getType();
        Long comId = moduleInput.getComId();
        for (SysModule sysModule : moduleList) {
            sysModule.setType(type);
            String comIds = comId + ","+"100";
            sysModule.setComIds(comIds);
            if (UserConstants.NOT_UNIQUE.equals(moduleService.checkModuleLabelUnique(sysModule))) {
                return AjaxResult.error("列名称已存在");
            }else if (UserConstants.NOT_UNIQUE.equals(moduleService.checkModulePropertyUnique(sysModule))) {
                return AjaxResult.error("列编码已存在");
            }
        }
        int row = moduleService.updateModule(moduleInput);
        return toAjax(row);
    }

    /**
     * 删除模板
     */
    @PreAuthorize("@ss.hasPermi('system:module:remove')")
    @Log(title = "删除模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        int row = 0;
        if(!id.equals("")){
            SysModule sysModule = new SysModule();
            sysModule.setId(id);
            row = moduleService.deleteModuleById(sysModule);
        }
        return toAjax(row);
    }

    @GetMapping("/export")
    public AjaxResult export() {
        SysModule sysModule = new SysModule();
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        sysModule.setDeptId(deptId);
        List<HashMap> list = moduleService.getModuleShow(sysModule);
        for (HashMap map : list){
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String s = format.format(map.get("createTime"));
            map.put("createTime",s);
            SysDictData dictData = new SysDictData();
            dictData.setDictType("module_type");
            List<SysDictData>  dictList = dictDataService.selectDictDataList(dictData);
            for(SysDictData sysDictData : dictList){
                if(Integer.parseInt(sysDictData.getDictValue()) == Integer.parseInt(map.get("type")+"")){
                    map.put("name",sysDictData.getDictLabel());
                }
            }
        }
        List<SysModule> resStation = new ArrayList<>();
        for(HashMap hashMap : list){
            SysModule sysModule1 = JSON.parseObject(JSON.toJSONString(hashMap), SysModule.class);
            String time = hashMap.get("createTime")+"";
            sysModule1.setExportCreateTime(time);
            resStation.add(sysModule1);
        }

        ExcelUtil<SysModule> util = new ExcelUtil<SysModule>(SysModule.class);
        return util.exportExcel(resStation, "模板管理数据");
    }

    @PreAuthorize("@ss.hasPermi('system:module:list')")
    @GetMapping("/getModuleName/{comId}")
    public AjaxResult getModuleName(@PathVariable Long comId) {
        SysModule sysModule = new SysModule();
        sysModule.setComId(comId);
        startPage();
        List<HashMap> list = moduleService.getModuleShow(sysModule);
        SysDictData dictData = new SysDictData();
        dictData.setDictType("module_type");
        List<SysDictData>  dictList = dictDataService.selectDictDataList(dictData);
        for (HashMap map : list){
            if(!dictList.isEmpty() && dictList != null){
                for(SysDictData sysDictData : dictList){
                    if(Integer.parseInt(sysDictData.getDictValue()) == Integer.parseInt(map.get("type")+"")){
                        dictList.remove(sysDictData);
                        break;
                    }
                }
            }
        }
        return AjaxResult.success(dictList);
    }
    public String getComIds(Long comId, String comIds){
        while(comId != 0){
            SysDept sysDept1 = deptService.selectDeptById(comId);
            comId = sysDept1.getParentId();
            comIds = comIds + "," + comId.toString();
        }
        return comIds;
    }
}
