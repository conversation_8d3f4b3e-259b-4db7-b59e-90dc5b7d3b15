package com.xhjt.project.data.controller;

import com.xhjt.common.constant.HttpStatus;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.hbase.*;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.data.service.*;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备数据 信息操作处理
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/data/device")
public class DeviceDataController extends BaseController {

    @Autowired
    private DeviceDataService deviceDataService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private AttitudeDataService attitudeDataService;
    @Autowired
    private AwsDataService awsDataService;
    @Autowired
    private Co2DataService co2DataService;
    @Autowired
    private CompassDataService compassDataService;
    @Autowired
    private Ea600DataService ea600DataService;
    @Autowired
    private GpsDataService gpsDataService;
    @Autowired
    private LogDataService logDataService;
    @Autowired
    private RacduDataService racduDataService;
    @Autowired
    private Sbe21DataService sbe21DataService;
    @Autowired
    private WindDataService windDataService;
    @Autowired
    private GpsInDataService gpsInDataService;
    @Autowired
    private CompassInDataService compassInDataService;

    /**
     * 获取数据列表
     *
     * @param startData
     * @param endDate
     * @return
     */
    @RequestMapping("/list/{startData}/{endDate}/{deviceId}/{interval}/{currentPage}/{pageSize}/{sort}")
    @PreAuthorize("@ss.hasPermi('data:device:list')")
    public TableDataInfo selectList(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long deviceId,
                                    @PathVariable Integer interval, @PathVariable int currentPage,
                                    @PathVariable int pageSize, @PathVariable String sort) {

        DeviceEntity device = deviceService.selectDeviceById(deviceId);
        if (device == null) {
            return new TableDataInfo(Lists.newArrayList(), 0, HttpStatus.SUCCESS);
        }

        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            return new TableDataInfo(Lists.newArrayList(), 0, HttpStatus.SUCCESS);
        }

        String tableName = hBaseDaoUtil.getTableName(device.getSn(), deviceTypeEnum.getAlias(), device.getCode(), interval);
        if (!hBaseDaoUtil.tableExists(tableName)) {
            return new TableDataInfo(Lists.newArrayList(), 0, HttpStatus.SUCCESS);
        }

        if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            return attitudeDataService.queryAttitudePage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            return awsDataService.queryAwsPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.GO8050.getValue().equals(device.getType())) {
            return co2DataService.queryCo2Page(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.COMPASS.getValue().equals(device.getType())) {
            return compassDataService.queryCompassPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.EA600.getValue().equals(device.getType())) {
            return ea600DataService.queryEa600Page(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            return gpsDataService.queryGpsPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.LOG.getValue().equals(device.getType())) {
            return logDataService.queryLogPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.RA_CDU1.getValue().equals(device.getType()) || DeviceTypeEnum.RA_CDU2.getValue().equals(device.getType())) {
            return racduDataService.queryRacduPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.SBE21.getValue().equals(device.getType())) {
            return sbe21DataService.querySbe21Page(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.WIND.getValue().equals(device.getType())) {
            return windDataService.queryWindPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.GPS_IN.getValue().equals(device.getType())) {
            return gpsInDataService.queryGpsInPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }
        if (DeviceTypeEnum.COMPASS_IN.getValue().equals(device.getType())) {
            return compassInDataService.queryCompassInPage(startData, endDate, tableName, currentPage, pageSize, sort);
        }

        return new TableDataInfo(Lists.newArrayList(), 0, HttpStatus.SUCCESS);
    }

    /**
     * 导出数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @PreAuthorize("@ss.hasPermi('data:device:export')")
    @GetMapping("/export/{startData}/{endDate}/{deviceId}/{interval}")
    public AjaxResult export(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long deviceId, @PathVariable Integer interval) {
        DeviceEntity device = deviceService.selectDeviceById(deviceId);

        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            return AjaxResult.error("设备类型不存在");
        }
        String tableName = hBaseDaoUtil.getTableName(device.getSn(), deviceTypeEnum.getAlias(), device.getCode(), interval);
        if (!hBaseDaoUtil.tableExists(tableName)) {
            return AjaxResult.error("表不存在");
        }

        if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            List<AttitudeHbaseVo> list = attitudeDataService.selectAttitudeList(startData, endDate, tableName);
            ExcelUtil<AttitudeHbaseVo> util = new ExcelUtil<AttitudeHbaseVo>(AttitudeHbaseVo.class);
            return util.exportExcel(list, "ATTITUDE数据");
        }
        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            List<AwsHbaseVo> list = awsDataService.selectAwsList(startData, endDate, tableName);
            ExcelUtil<AwsHbaseVo> util = new ExcelUtil<AwsHbaseVo>(AwsHbaseVo.class);
            return util.exportExcel(list, "AWS数据");
        }
        if (DeviceTypeEnum.GO8050.getValue().equals(device.getType())) {
            List<Co2HbaseVo> list = co2DataService.selectCo2List(startData, endDate, tableName);
            ExcelUtil<Co2HbaseVo> util = new ExcelUtil<Co2HbaseVo>(Co2HbaseVo.class);
            return util.exportExcel(list, "CO2数据");
        }
        if (DeviceTypeEnum.COMPASS.getValue().equals(device.getType())) {
            List<CompassHbaseVo> list = compassDataService.selectCompassList(startData, endDate, tableName);
            ExcelUtil<CompassHbaseVo> util = new ExcelUtil<CompassHbaseVo>(CompassHbaseVo.class);
            return util.exportExcel(list, "电罗经数据");
        }
        if (DeviceTypeEnum.EA600.getValue().equals(device.getType())) {
            List<Ea600HbaseVo> list = ea600DataService.selectEa600List(startData, endDate, tableName);
            ExcelUtil<Ea600HbaseVo> util = new ExcelUtil<Ea600HbaseVo>(Ea600HbaseVo.class);
            return util.exportExcel(list, "EA600数据");
        }
        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            List<GpsHbaseVo> list = gpsDataService.selectGpsList(startData, endDate, tableName);
            ExcelUtil<GpsHbaseVo> util = new ExcelUtil<GpsHbaseVo>(GpsHbaseVo.class);
            return util.exportExcel(list, "GPS数据");
        }
        if (DeviceTypeEnum.LOG.getValue().equals(device.getType())) {
            List<LogHbaseVo> list = logDataService.selectLogList(startData, endDate, tableName);
            ExcelUtil<LogHbaseVo> util = new ExcelUtil<LogHbaseVo>(LogHbaseVo.class);
            return util.exportExcel(list, "LOG数据");
        }
        if (DeviceTypeEnum.RA_CDU1.getValue().equals(device.getType()) || DeviceTypeEnum.RA_CDU2.getValue().equals(device.getType())) {
            List<RacduHbaseVo> list = racduDataService.selectRacduList(startData, endDate, tableName);
            ExcelUtil<RacduHbaseVo> util = new ExcelUtil<RacduHbaseVo>(RacduHbaseVo.class);
            return util.exportExcel(list, "RACDU数据");
        }
        if (DeviceTypeEnum.SBE21.getValue().equals(device.getType())) {
            List<Sbe21HbaseVo> list = sbe21DataService.selectSbe21List(startData, endDate, tableName);
            ExcelUtil<Sbe21HbaseVo> util = new ExcelUtil<Sbe21HbaseVo>(Sbe21HbaseVo.class);
            return util.exportExcel(list, "SBE21数据");
        }
        if (DeviceTypeEnum.WIND.getValue().equals(device.getType())) {
            List<WindHbaseVo> list = windDataService.selectWindList(startData, endDate, tableName);
            ExcelUtil<WindHbaseVo> util = new ExcelUtil<WindHbaseVo>(WindHbaseVo.class);
            return util.exportExcel(list, "Wind数据");
        }
        if (DeviceTypeEnum.GPS_IN.getValue().equals(device.getType())) {
            List<GpsInHbaseVo> list = gpsInDataService.selectGpsInList(startData, endDate, tableName);
            ExcelUtil<GpsInHbaseVo> util = new ExcelUtil<GpsInHbaseVo>(GpsInHbaseVo.class);
            return util.exportExcel(list, "内置GPS数据");
        }
        if (DeviceTypeEnum.COMPASS_IN.getValue().equals(device.getType())) {
            List<CompassInHbaseVo> list = compassInDataService.selectCompassInList(startData, endDate, tableName);
            ExcelUtil<CompassInHbaseVo> util = new ExcelUtil<CompassInHbaseVo>(CompassInHbaseVo.class);
            return util.exportExcel(list, "内置COMPASS数据");
        }
        return AjaxResult.success("没有该类型设备");
    }


    /**
     * 获取表格表头
     */
    @PreAuthorize("@ss.hasPermi('data:device:list')")
    @GetMapping("/getTableHead/{deviceId}")
    public AjaxResult queryBySn(@PathVariable Long deviceId) {
        return AjaxResult.success(deviceDataService.getTableHead(deviceId));
    }
}
