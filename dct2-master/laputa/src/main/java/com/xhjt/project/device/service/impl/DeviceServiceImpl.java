package com.xhjt.project.device.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.xhjt.common.constant.UserConstants;
import com.xhjt.common.constants.RedisKeyConstants;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.JsonUtil;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.data.service.DeviceDataService;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.DeviceStatus;
import com.xhjt.project.device.domain.TransferDeviceVo;
import com.xhjt.project.device.mapper.DeviceMapper;
import com.xhjt.project.device.service.DeviceAttributeService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.netty.service.SyncService;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.ShipService;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class DeviceServiceImpl implements DeviceService {
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private ShipService shipService;

    @Resource
    private ValueOperations<String, String> valueOperations;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SyncService syncService;
    @Autowired
    private DeviceAttributeService deviceAttributeService;
    @Autowired
    JdbcTemplate jdbcTemplate;
     /**
     * 查询设备信息
     *
     * @param deviceId 设备ID
     * @return 设备信息
     */
    @Override
    public DeviceEntity selectDeviceById(Long deviceId) {
        DeviceEntity device = new DeviceEntity();
        device.setId(deviceId);
        return deviceMapper.selectDevice(device);
    }

    /**
     * 查询设备列表
     *
     * @param device 设备信息
     * @return 设备集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<DeviceEntity> selectDeviceList(DeviceEntity device) {
        return deviceMapper.selectDeviceList(device);
    }


    @Override
    public List<DeviceEntity> queryListBySn(String sn) {
        DeviceEntity device = new DeviceEntity();
        device.setSn(sn);
        return deviceMapper.selectDeviceList(device);
    }

    /**
     * 查询设备列表
     *
     * @return 设备集合
     */
    @Override
    public List<DeviceEntity> selectAllDevice() {
        return deviceMapper.selectDeviceList(null);
    }

    /**
     * 根据 code查询设备信息
     *
     * @param code
     * @return 设备信息
     */
    @Override
    public DeviceEntity selectByCodeAndSn(String sn, String code) {
        DeviceEntity device = new DeviceEntity();
        device.setSn(sn);
        device.setCode(code);
        return deviceMapper.selectDevice(device);
    }


    /**
     * 新增设备
     *
     * @param device 设备信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addDevice(DeviceEntity device) {
        DeviceEntity sourceDevice = selectByCodeAndSn(device.getSn(), device.getCode());
        if (sourceDevice != null) {
            return 0;
        }

        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            return 0;
        }

        ShipEntity shipEntity = shipService.selectShipBySn(device.getSn());
        device.setDeptId(shipEntity.getDeptId());

//        device.setEnable(0);

        int count = deviceMapper.addDevice(device);

        // 设备创建hbase表
        String tableName;
        int[] array = {0, 1, 5, 15};
        for (int interval : array) {
            tableName = hBaseDaoUtil.getTableName(device.getSn(), deviceTypeEnum.getAlias(), device.getCode(), interval);

            //先做删除，再添加
            hBaseDaoUtil.dropTable(tableName);

            hBaseDaoUtil.createTable(tableName, Sets.newHashSet("i"), HBaseDaoUtil.DAY_SPLIT_KEYS);
        }
        renewEnableCodeList();
        return count;
    }

    /**
     * 修改设备
     *
     * @param device 设备信息
     * @return 结果
     */
    @Override
    public int updateDevice(DeviceEntity device) {
        DeviceEntity deviceEntity = selectDeviceById(device.getId());
        if (deviceEntity == null) {
            throw new CustomException("数据不存在");
        }

        boolean renameBoolean = false;
        DeviceEntity oldDevice = new DeviceEntity();
        if (!deviceEntity.getCode().equals(device.getCode()) || !deviceEntity.getType().equals(device.getType())) {
            renameBoolean = true;
            BeanUtils.copyProperties(deviceEntity, oldDevice);
        }

        deviceEntity.setName(device.getName());
        deviceEntity.setType(device.getType());
        deviceEntity.setCode(device.getCode());
        deviceEntity.setEnable(device.getEnable());
        deviceEntity.setConnectStatus(device.getConnectStatus());
        deviceEntity.setCompartment(device.getCompartment());
        deviceEntity.setTransferStatus(device.getTransferStatus());
        int count = deviceMapper.updateDevice(deviceEntity);

        // 更新设备缓存信息
        renewDevice4Redis();

        // 类型或者编码修改需要更新hbase表名
        if (renameBoolean) {
            String oldName;
            String newName;
            int[] array = {0, 1, 5, 15};
            for (int interval : array) {
                oldName = hBaseDaoUtil.getTableName(oldDevice.getSn(), DeviceTypeEnum.getByValue(oldDevice.getType()).getAlias(), oldDevice.getCode(), interval);
                newName = hBaseDaoUtil.getTableName(deviceEntity.getSn(), DeviceTypeEnum.getByValue(deviceEntity.getType()).getAlias(), deviceEntity.getCode(), interval);
                hBaseDaoUtil.tableRename(oldName, newName);
            }
        }

        return count;
    }

    /**
     * 删除设备信息
     *
     * @param deviceId 参数ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeviceById(Long deviceId) {
        DeviceEntity deviceEntity = selectDeviceById(deviceId);
        if (deviceEntity == null) {
            return 0;
        }

        int count = deviceMapper.deleteDeviceById(deviceId);

        // 设备删除hbase表
        String tableName;
        int[] array = {0, 1, 5, 15};
        for (int interval : array) {
            tableName = hBaseDaoUtil.getTableName(deviceEntity.getSn(), DeviceTypeEnum.getByValue(deviceEntity.getType()).getAlias(), deviceEntity.getCode(), interval);
            if (!hBaseDaoUtil.tableExists(tableName)) {
                continue;
            }
            hBaseDaoUtil.dropTable(tableName);
        }

        return count;
    }

    /**
     * 批量删除参数信息
     *
     * @param deviceIds 需要删除的参数ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeviceByIds(Long[] deviceIds) {
        int count = 0;
        for (Long id : deviceIds) {
            count += deleteDeviceById(id);
        }

        return count;
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param device 设备信息
     * @return 结果
     */
    @Override
    public String checkCodeUnique(DeviceEntity device) {
        Long deviceId = StringUtils.isNull(device.getId()) ? -1L : device.getId();

        DeviceEntity deviceEntity = selectByCodeAndSn(device.getSn(), device.getCode());

        if (StringUtils.isNotNull(deviceEntity) && deviceEntity.getId().longValue() != deviceId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }


    /**
     * 启用
     *
     * @param id 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public DeviceEntity enable(Long id) {
        DeviceEntity deviceEntity = selectDeviceById(id);
        if (deviceEntity == null) {
            return null;
        }
        if (deviceEntity.getEnable() == 1) {
            return deviceEntity;
        }
        deviceEntity.setEnable(1);
        updateDevice(deviceEntity);

        // 更新缓存数据
        renewDevice4Redis();
        // 同步到船端
        sync2Shore(deviceEntity);

        return deviceEntity;
    }

    /**
     * 修改设备
     *
     * @param id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public DeviceEntity disable(Long id) {
        DeviceEntity deviceEntity = selectDeviceById(id);
        if (deviceEntity == null) {
            return null;
        }
        if (deviceEntity.getEnable() == 0) {
            return deviceEntity;
        }
        deviceEntity.setEnable(0);
        updateDevice(deviceEntity);

        // 更新缓存数据
        renewDevice4Redis();
        // 同步到船端
        sync2Shore(deviceEntity);

        return deviceEntity;
    }

    /**
     * 更新redis中设备
     */
    private void renewDevice4Redis() {
        ValueOperations<String, DeviceEntity> opsForValue = redisTemplate.opsForValue();
        List<DeviceEntity> list = selectAllDevice();

        List<String> codeList = Lists.newArrayList();
        for (DeviceEntity device : list) {
            opsForValue.set("ALL_DEVICE-" + device.getSn() + "_" + device.getCode(), device);
            if (device.getEnable() == 0) {
                continue;
            }
            codeList.add(device.getSn() + "_" + device.getCode());
        }

        if (codeList.size() == 0) {
            codeList.add("TEMP");
        }

        valueOperations.set(RedisParameter.ALL_ENABLE_DEVICE_CODE, JsonUtil.obj2String(codeList));
    }

    @Override
    public void handleBySync(SyncEntity syncEntity, String sn) {
        TransferDeviceVo transferDeviceVo = JSON.parseObject(syncEntity.getJsonObject(), TransferDeviceVo.class);
        DeviceEntity deviceTran = transferDeviceVo.getDeviceEntity();

        if (syncEntity.getAction() == 1) {
            deviceTran.setSn(sn);
            deviceTran.setCreateBy("sync");
            addDevice(deviceTran);
        } else if (syncEntity.getAction() == 2) {
            DeviceEntity deviceEntity = selectByCodeAndSn(sn, deviceTran.getCode());
            if (deviceEntity == null) {
                return;
            }
            deviceEntity.setType(deviceTran.getType());
            deviceEntity.setName(deviceTran.getName());
            deviceEntity.setTransferStatus(deviceTran.getTransferStatus());
            deviceEntity.setCompartment(deviceTran.getCompartment());
            deviceEntity.setBaudRate(deviceTran.getBaudRate());
            deviceEntity.setDataBits(deviceTran.getDataBits());
            deviceEntity.setStopBits(deviceTran.getStopBits());
            deviceEntity.setParity(deviceTran.getParity());
            deviceEntity.setSerialPort(deviceTran.getSerialPort());
            deviceEntity.setMac(deviceTran.getMac());
            deviceEntity.setEnable(deviceTran.getEnable());
            deviceEntity.setConnectStatus(deviceTran.getConnectStatus());
            deviceEntity.setUpdateBy("sync");

            updateNewDevice(deviceEntity);
        } else if (syncEntity.getAction() == 3) {
            DeviceEntity deviceEntity = selectByCodeAndSn(sn, deviceTran.getCode());

            if (deviceEntity != null) {
                deleteDeviceById(deviceEntity.getId());
            }
        }
    }

    /**
     * 同步使用
     * @param device
     */
    private int updateNewDevice(DeviceEntity device) {
        DeviceEntity deviceEntity = selectDeviceById(device.getId());
        if (deviceEntity == null) {
            throw new CustomException("数据不存在");
        }

        boolean renameBoolean = false;
        DeviceEntity oldDevice = new DeviceEntity();
        if (!deviceEntity.getCode().equals(device.getCode()) || !deviceEntity.getType().equals(device.getType())) {
            renameBoolean = true;
            BeanUtils.copyProperties(deviceEntity, oldDevice);
        }

        deviceEntity.setName(device.getName());
        deviceEntity.setType(device.getType());
        deviceEntity.setCode(device.getCode());
        deviceEntity.setTransferStatus(device.getTransferStatus());
        deviceEntity.setCompartment(device.getCompartment());
        deviceEntity.setBaudRate(device.getBaudRate());
        deviceEntity.setDataBits(device.getDataBits());
        deviceEntity.setStopBits(device.getStopBits());
        deviceEntity.setParity(device.getParity());
        deviceEntity.setMac(device.getMac());
        deviceEntity.setEnable(device.getEnable());
        deviceEntity.setConnectStatus(device.getConnectStatus());
        deviceEntity.setSerialPort(device.getSerialPort());
        deviceEntity.setUpdateBy("sync");

        int count = deviceMapper.updateDevice(deviceEntity);

        // 更新设备缓存信息
        renewDevice4Redis();

        // 类型或者编码修改需要更新hbase表名
        if (renameBoolean) {
            String oldName;
            String newName;
            int[] array = {0, 1, 5, 15};
            for (int interval : array) {
                oldName = hBaseDaoUtil.getTableName(oldDevice.getSn(), DeviceTypeEnum.getByValue(oldDevice.getType()).getAlias(), oldDevice.getCode(), interval);
                newName = hBaseDaoUtil.getTableName(deviceEntity.getSn(), DeviceTypeEnum.getByValue(deviceEntity.getType()).getAlias(), deviceEntity.getCode(), interval);
                hBaseDaoUtil.tableRename(oldName, newName);
            }
        }
        renewEnableCodeList();
        return count;
    }

    /**
     * 同步到船端
     *
     * @param deviceEntity
     */
    public void sync2Shore(DeviceEntity deviceEntity) {
        SyncEntity syncEntity = new SyncEntity();
        syncEntity.setJsonObject(JSONObject.toJSONString(deviceEntity));
        syncEntity.setAction(2);
        syncEntity.setModule("device");

        KafkaMessage kafkaMessage = new KafkaMessage(deviceEntity.getCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(deviceEntity.getSn());
        syncService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 根据IP和Port查询设备信息
     *
     * @return 设备信息
     */
    public DeviceEntity selectByMac(String mac) {
        DeviceEntity device = new DeviceEntity();
        device.setMac(mac);
        device.setConnectType(0);//采集终端
        return deviceMapper.selectDevice(device);
    }

    /**
     * 传输状态修改-同时同步到船端
     * @param id
     * @param status
     * @return
     */
    @Override
    public DeviceEntity changeTrStatus(Long id, Integer status) {
        DeviceEntity deviceEntity = selectDeviceById(id);
        if (deviceEntity == null) {
            return null;
        }
        if (status==1){
            deviceEntity.setEnable(status);
        }
        deviceEntity.setTransferStatus(status);
        updateDevice(deviceEntity);

        // 更新缓存数据
        renewDevice4Redis();
        // 同步到船端
        sync2Shore(deviceEntity);
        return deviceEntity;
    }

    /**
     * 5分钟船端设备状态定时同步到岸端
     * @param deviceStatus
     * @param sn
     */
    @Override
    public void handleStatusBySync(DeviceStatus deviceStatus, String sn) {
        DeviceEntity deviceEntity = selectByCodeAndSn(sn, deviceStatus.getCode());
        if (deviceEntity == null) {
            return;
        }
        deviceEntity.setConnectStatus(deviceStatus.getConnectStatus());
        deviceEntity.setTransferStatus(deviceStatus.getTransferStatus());
        deviceEntity.setEnable(deviceStatus.getEnable());
        deviceEntity.setUpdateBy("sync");
        int count = deviceMapper.updateDevice(deviceEntity);

        // 更新设备缓存信息
        renewDevice4Redis();

    }

    /**
     * 更新redis中设备编码集合
     *
     * @throws Exception
     */
    public List<String> renewEnableCodeList() {

        String sql = "SELECT CONCAT(sn,'_',code) FROM device WHERE enable = 1";
        List<String> codeList = jdbcTemplate.queryForList(sql, String.class);

        if (codeList.size() == 0) {
            codeList.add("TEMP");
        }


        valueOperations.set(RedisKeyConstants.ALL_ENABLE_DEVICE_CODE, JsonUtil.obj2String(codeList));

        return codeList;
    }

}
