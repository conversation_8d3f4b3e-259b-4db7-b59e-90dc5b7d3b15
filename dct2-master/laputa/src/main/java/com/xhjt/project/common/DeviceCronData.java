package com.xhjt.project.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ScheduledExecutorService;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
@Component
public class DeviceCronData implements Runnable {

    protected Logger logger = LoggerFactory.getLogger(DeviceCronData.class);

    private ScheduledExecutorService scheduled;

    private SynchroEntity synchro;

    private String sn;

    private int i = 0;

    public DeviceCronData() {

    }

    public DeviceCronData(SynchroEntity synchro, String sn, ScheduledExecutorService scheduled) {
        this.synchro = synchro;
        this.sn = sn;
        this.scheduled = scheduled;
    }

    @Override
    public void run() {

    }
}
