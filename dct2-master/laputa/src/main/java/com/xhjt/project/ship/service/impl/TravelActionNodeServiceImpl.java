package com.xhjt.project.ship.service.impl;

import com.xhjt.project.ship.domain.TravelActionNodeEntity;
import com.xhjt.project.ship.mapper.TravelActionNodeMapper;
import com.xhjt.project.ship.service.TravelActionNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 行程动作节点管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class TravelActionNodeServiceImpl implements TravelActionNodeService {

    @Autowired
    private TravelActionNodeMapper travelActionNodeMapper;

    /**
     * 查询航次信息
     *
     * @param id 航次ID
     * @return 航次信息
     */
    @Override
    public TravelActionNodeEntity selectTravelActionNodeById(Long id) {
        TravelActionNodeEntity travelActionNode = new TravelActionNodeEntity();
        travelActionNode.setId(id);
        return travelActionNodeMapper.selectTravelActionNode(travelActionNode);
    }

    /**
     * 查询航次列表
     *
     * @param travelActionNode 航次信息
     * @return 航次集合
     */
    @Override
    public List<TravelActionNodeEntity> selectTravelActionNodeListByDept(TravelActionNodeEntity travelActionNode) {
        return travelActionNodeMapper.selectTravelActionNodeList(travelActionNode);
    }

    @Override
    public List<TravelActionNodeEntity> selectTravelActionNodeList(TravelActionNodeEntity travelActionNode) {
        return travelActionNodeMapper.selectTravelActionNodeList(travelActionNode);
    }

    /**
     * 新增航次
     *
     * @param travelActionNode 航次信息
     * @return 结果
     */
    @Override
    public int addTravelActionNode(TravelActionNodeEntity travelActionNode) {
        return travelActionNodeMapper.addTravelActionNode(travelActionNode);
    }

    /**
     * 修改航次
     *
     * @param travelActionNode 航次信息
     * @return 结果
     */
    @Override
    public int updateTravelActionNode(TravelActionNodeEntity travelActionNode) {
        return travelActionNodeMapper.updateTravelActionNode(travelActionNode);
    }

    /**
     * 删除航次信息
     *
     * @param travelActionNodeId 参数ID
     * @return 结果
     */
    @Override
    public int deleteTravelActionNodeById(Long travelActionNodeId) {
        return travelActionNodeMapper.deleteTravelActionNodeById(travelActionNodeId);
    }

    /**
     * 批量删除参数信息
     *
     * @param travelActionNodeIds 需要删除的参数ID
     * @return 结果
     */
    @Override
    public int deleteTravelActionNodeByIds(Long[] travelActionNodeIds) {
        return travelActionNodeMapper.deleteTravelActionNodeByIds(travelActionNodeIds);
    }

}
