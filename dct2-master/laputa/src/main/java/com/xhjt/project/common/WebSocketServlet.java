package com.xhjt.project.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: rr1
 * @create: 2020-05-12 12:34
 **/
//@ServerEndpoint(value = "/websocket/{page}/{sn}")
@Component
public class WebSocketServlet {

    private Logger logger = LoggerFactory.getLogger(WebSocketServlet.class);

    private ScheduledExecutorService scheduled = null;
    /**
     * 存放session以及对应的页面
     */
    private static ConcurrentHashMap<Session, String> map = new ConcurrentHashMap<Session, String>();
    /**
     * 判断是否启动线程
     */
    private static boolean isRun = true;
    private Session session;

    /**
     * @ClassName: onOpen
     * @Description: 开启连接的操作
     */
    @OnOpen
    public void onOpen(@PathParam(value = "page") String page, Session session,@PathParam(value = "sn") String sn) throws Exception {
        this.session = session;
        logger.info("WebSocket接收到请求111。。---session:--{}", session.getId());
        map.put(session, page);
        if (isRun) {
            scheduled = Executors.newScheduledThreadPool(1);
            logger.info("开始发送数据。。。。。。。。");
            isRun = false;

            CronSendData cron = new CronSendData(map,sn);
            scheduled.scheduleAtFixedRate(cron, 0, 1, TimeUnit.SECONDS);

        }

    }

    /**
     * @ClassName: onClose
     * @Description: 连接关闭的操作
     */
    @OnClose
    public void onClose() {
        logger.info("WebSocket结束请求333。。session:{}", session.getId());
        map.remove(this.session);
        if (map.size() <= 0) {
            logger.info("无连接,停止发送数据。。。。。。。。");
            isRun = true;
            if (scheduled != null && !scheduled.isShutdown()) {
                scheduled.shutdown();
                scheduled = null;
            }

        }

    }

    /**
     * 给服务器发送消息
     *
     * @param msg 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String msg, Session session) {
//        System.out.println("执行刷新");

    }

    /**
     * @ClassName: OnError
     * @Description: 出错的操作
     */
    @OnError
    public void onError(Session session, Throwable error) {
        error.printStackTrace();
    }

    /**
     * 获取最新的session集合
     * @return
     */
    public ConcurrentHashMap<Session, String> getSessionMap(){
        return map;
    }

}