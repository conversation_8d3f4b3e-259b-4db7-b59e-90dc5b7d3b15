package com.xhjt.project.common;

import com.xhjt.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * websocket服务
 */
@ServerEndpoint(value = "/wsServer/{sid}/{sn}/{code}/{type}")
@Component
public class WebSocketServer {

    private static Logger logger = LoggerFactory.getLogger(WebSocketServer.class);
    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<WebSocketServer>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 接收sid
     */
    private String sid = "";

    /**
     * 设备编码
     */
    private String code;

    /**
     * sn
     */
    private String sn;

    /**
     * 模板类型
     */
    private Integer type;

    public static boolean flag = false;


    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "sid") String sid, @PathParam(value = "code") String code, @PathParam(value = "sn") String sn, @PathParam(value = "type") Integer type) {
        this.session = session;
        //加入set中
        webSocketSet.add(this);
        //在线数加1
        addOnlineCount();
        logger.info("websocket有新窗口开始监听:" + sid + ",当前在线人数为" + getOnlineCount());
        this.sid = sid;
        this.code = code;
        this.type = type;
        flag = true;
        logger.info("websocket有新窗口开始监听:" + flag);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        //从set中删除
        webSocketSet.remove(this);
        //在线数减1
        subOnlineCount();
        logger.info("websocket有一连接关闭！当前在线人数为" + getOnlineCount());
        flag = false;
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        logger.info("收到来自窗口" + sid + "的信息:" + message);
        //群发消息
        for (WebSocketServer item : webSocketSet) {
            item.sendMessage(message);
        }
    }

    /**
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        logger.error("发生错误");
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    private void sendMessage(String message) {
        try {
            this.session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            logger.error("websocket发送出错，---{}", e);
        }
    }

    /**
     * 群发自定义消息
     */
    private static void sendInfo(String message, @PathParam("sid") String sid) {
        webSocketSet.stream().filter(ws -> ws.sid.equals(sid)).forEach(ws -> ws.sendMessage(message));
    }

    /**
     * 发送实时数据
     *
     * @param message
     * @param code
     */
    public static void sendRealTimeData(String message, String code,String sn) {
        if (StringUtils.isBlank(code) && sn == null) {
            return;
        }
        List<String> codeList = getAllWebsocketCode();
        if (codeList == null || !codeList.contains(code)) {
            return;
        }

        webSocketSet.stream().filter(ws -> ws.code.equals(code)).forEach(ws -> sendInfo(message, ws.sid));
    }

    private static List<String> getAllWebsocketCode() {
        if (webSocketSet.size() == 0) {
            return null;
        }
        return webSocketSet.stream().map(ws -> ws.code).collect(Collectors.toList());
    }

    private static synchronized int getOnlineCount() {
        return onlineCount;
    }

    private static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
    }

    private static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
    }
}
