package com.xhjt.project.data.service;

import com.xhjt.common.constant.HttpStatus;
import com.xhjt.dctcore.commoncore.domain.hbase.Co2HbaseVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.web.page.TableDataInfo;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * co2数据信息 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class Co2DataService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    public List<Co2HbaseVo> selectCo2List(Long startTime, Long endTime, String tableName) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new Co2HbaseVo(), tableName, rowList);
    }

    public TableDataInfo queryCo2Page(Long startTime, Long endTime, String tableName, int currentPage, int pageSize, String sort) {
        List<Co2HbaseVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new Co2HbaseVo(), tableName, list, rowList, currentPage, pageSize, sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }
}
