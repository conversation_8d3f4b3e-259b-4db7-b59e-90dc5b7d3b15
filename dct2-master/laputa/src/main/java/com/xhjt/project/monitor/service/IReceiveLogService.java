package com.xhjt.project.monitor.service;

import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.project.monitor.domain.ReceiveLog;

import java.util.List;
import java.util.Map;

/**
 * 接收数据记录 服务层
 *
 * <AUTHOR>
 */
public interface IReceiveLogService {

    /**
     * 记录
     *
     * @param transferPackage
     */
    public void record(TransferPackage transferPackage);

    /**
     * 新增
     */
    public void insert(ReceiveLog receiveLog);

    /**
     * 查询集合
     */
    public List<ReceiveLog> selectList(ReceiveLog receiveLog);

    public List<ReceiveLog> selectReceiveLogList();

    /**
     * 批量删除
     */
    public int deleteByIds(Long[] ids);

    /**
     * 查询详细
     */
    public ReceiveLog selectById(Long id);

    /**
     * 根据查询集合
     */
    public List<ReceiveLog> selectListByRecordTime(Long startTime, Long endTime, String sn);

    /**
     * 查询秒级数据
     */
    public List<Map<Long, Integer>> selectListForMinutes(String sn);

    /**
     * 监控机舱数据
     */
    public List<Map<Long, Integer>> queryEngineRoomForMinutes();

}
