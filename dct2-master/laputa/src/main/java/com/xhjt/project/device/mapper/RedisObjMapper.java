package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.RedisObjVo;

/**
 * redis 数据层
 *
 * <AUTHOR>
 */
public interface RedisObjMapper {

 /**
  * 新增redis
  *
  * @param redisObj redis信息
  * @return 结果
  */
 public int save(RedisObjVo redisObj);

 /**
  * 修改redis
  *
  * @param redisObj redis信息
  * @return 结果
  */
 public int update(RedisObjVo redisObj);

 /**
  * 查询redis
  *
  * @param redisObj redis信息
  * @return redis信息
  */
 public RedisObjVo selectRedisObj(RedisObjVo redisObj);
}