package com.xhjt.project.device.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.DeviceStatus;
import com.xhjt.project.device.domain.Transfer2DeviceVo;
import com.xhjt.project.device.domain.Transfer2SheetaEntity;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.TransferAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/transferAttribute")
public class TransferAttributeController extends BaseController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Resource
    private ValueOperations<String, Object> valueOperations;
    /**
     * 获取传输属性列表
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceEntity deviceEntity) {
        startPage();
        List<DeviceEntity> deviceList = deviceService.selectDeviceList(deviceEntity);
        List<TransferAttributeEntity> taList = transferAttributeService.selectTransferAttributeList(new TransferAttributeEntity());
        Map<String, String> map = taList.stream()
                .collect(groupingBy(entity -> entity.getSn() + "_" + entity.getDeviceCode(),
                        mapping(TransferAttributeEntity::getLabel, Collectors.joining(","))));

        deviceList.forEach(device -> {
            //从redis中取出来，加入最新数据的时间
            String objectKey = RedisParameter.LATEST_DATE + device.getSn() + "_" + device.getCode();
            if (valueOperations.get(objectKey) != null){
                Long dataTime = (Long) valueOperations.get(objectKey);//最新一条数据
//                if (dataTime == null){
//                    //一开始可能还没有则给个当前的时间
//                    String dt = DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
//                    device.setLastTime(dt);
//                }else{
                    String dt = DateUtils.parseTimeToDate(dataTime,"yyyy-MM-dd HH:mm:ss");
                    device.setLastTime(dt);
//                }
            }else {
                String dt = DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
                device.setLastTime(dt);
            }

            if (map.get(device.getSn() + "_" + device.getCode()) != null) {
                device.setAttributes(map.get(device.getSn() + "_" + device.getCode()));
            }
        });

        return getDataTable(deviceList);
    }

    @Log(title = "传输属性", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('transfer:attribute:export')")
    @GetMapping("/export")
    public AjaxResult export(TransferAttributeEntity transferAttribute) {
        List<TransferAttributeEntity> list = transferAttributeService.selectTransferAttributeList(transferAttribute);
        ExcelUtil<TransferAttributeEntity> util = new ExcelUtil<TransferAttributeEntity>(TransferAttributeEntity.class);
        return util.exportExcel(list, "传输数据");
    }

    @PreAuthorize("@ss.hasPermi('transfer:attribute:list')")
    @GetMapping("/listByDeviceCode")
    public AjaxResult listByDeviceCode(TransferAttributeEntity transferAttribute) {
        List<TransferAttributeEntity> list = transferAttributeService.selectTransferAttributeList(transferAttribute);
        return AjaxResult.success(list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:query')")
    @GetMapping(value = "/{transferAttributeId}")
    public AjaxResult getInfo(@PathVariable Long transferAttributeId) {
        return AjaxResult.success(transferAttributeService.selectTransferAttributeById(transferAttributeId));
    }

    /**
     * 新增传输属性
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:add')")
    @Log(title = "传输属性新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody TransferAttributeEntity transferAttribute) {
        transferAttribute.setCreateBy(SecurityUtils.getUsername());
        return toAjax(transferAttributeService.addTransferAttribute(transferAttribute));
    }

    /**
     * 修改传输属性
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:edit')")
    @Log(title = "传输属性修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody TransferAttributeEntity transferAttribute) {
        transferAttribute.setUpdateBy(SecurityUtils.getUsername());
        List<Transfer2SheetaEntity> transferAttributeEntities =  transferAttributeService.updateTransferAttribute(transferAttribute);

        if (transferAttributeEntities!=null && transferAttributeEntities.size() > 0) {
            //这边需要把cost , compartment更新到设备信息表DeviceEntity
            DeviceEntity deviceEntity = deviceService.selectByCodeAndSn(transferAttribute.getSn(),transferAttribute.getDeviceCode());
            deviceEntity.setCompartment(transferAttribute.getCompartment());
            int row = deviceService.updateDevice(deviceEntity);
            if (row>0){
                //同时同步设备以及对应属性数据到船端
                Transfer2DeviceVo transferDeviceEntity = new Transfer2DeviceVo();
                DeviceStatus deviceStatus = new DeviceStatus();
                deviceStatus.setCompartment(transferAttribute.getCompartment());
                deviceStatus.setCode(deviceEntity.getCode());
                transferDeviceEntity.setDeviceStatus(deviceStatus);
                transferDeviceEntity.setTransferAttributeEntities(transferAttributeEntities);
                transferAttributeService.syncNewShore(transferDeviceEntity, 2,transferAttribute.getSn());
                return toAjax(row);
//            transferAttributeService.sync2Shore(transferAttribute);
            }
        }
        transferAttributeService.renewAttributes(transferAttribute.getSn(), transferAttribute.getDeviceCode());

        return toAjax(0);
    }

    /**
     * 删除传输属性
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:remove')")
    @Log(title = "传输属性删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{transferAttributeIds}")
    public AjaxResult remove(@PathVariable Long[] transferAttributeIds) {
        return toAjax(transferAttributeService.deleteTransferAttributeByIds(transferAttributeIds));
    }

    /**
     * 定时任务-存储状态修改
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:edit')")
    @PostMapping("/changeStatus/{code}/{status}/{sn}")
    public AjaxResult changeStatus(@PathVariable String code, @PathVariable Integer status,@PathVariable String sn) {
        DeviceEntity deviceEntity = deviceService.selectByCodeAndSn(sn,code);
        if (status==0){
            DeviceEntity device = deviceService.disable(deviceEntity.getId());
            if (device!=null){
                return toAjax(1);
            }else {
                return toAjax(0);
            }
        }
        DeviceEntity device = deviceService.enable(deviceEntity.getId());
        if (device!=null){
            return toAjax(1);
        }else {
            return toAjax(0);
        }
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('transfer:attribute:edit')")
    @PostMapping("/changeTrStatus/{code}/{status}/{sn}")
    public AjaxResult changeTrStatus(@PathVariable String code, @PathVariable Integer status,@PathVariable String sn) {
        DeviceEntity deviceEntity = deviceService.selectByCodeAndSn(sn,code);
        DeviceEntity device = deviceService.changeTrStatus(deviceEntity.getId(),status);
        if (device!=null){
            return toAjax(1);
        }else {
            return toAjax(0);
        }
    }
}
