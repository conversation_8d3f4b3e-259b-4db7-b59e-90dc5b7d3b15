package com.xhjt.project.netty.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.RedisObjVo;
import com.xhjt.project.device.domain.SyncAllEntity;
import com.xhjt.project.device.domain.TransferDeviceVo;
import com.xhjt.project.device.mapper.RedisObjMapper;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.ShipTerminalManageService;
import com.xhjt.project.device.service.TransferAttributeService;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * class
 *
 * <AUTHOR>
 */
@Service
public class SyncService {
    public final static Logger logger = LoggerFactory.getLogger(SyncService.class);

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private SnapshotChannelService snapshotChannelService;
    @Autowired
    private SnapshotTransferService snapshotTransferService;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Autowired
    private AcquisitionMiddlewareService acquisitionMiddlewareService;
    @Autowired
    private ShipTerminalManageService shipTerminalManageService;
    @Autowired
    private RedisObjMapper redisObjMapper;
    public void handleMessage(TransferPackage transferPackage) {
        try {
            logger.info("同步请求--555555555555---{}",transferPackage.getMessage());
            SyncEntity syncEntity = JSON.parseObject(transferPackage.getMessage(), SyncEntity.class);
            logger.info("同步请求--sn:{}---DeviceCode:{}----{}", transferPackage.getSn(), transferPackage.getDeviceCode(), transferPackage.getMessage());

            if ("transferDevice".equals(syncEntity.getModule())) {
                deviceService.handleBySync(syncEntity, transferPackage.getSn());

                //同时修改设备传输属性
                TransferDeviceVo transferDeviceVo = JSON.parseObject(syncEntity.getJsonObject(), TransferDeviceVo.class);
                if(syncEntity.getAction() == 3){
                    transferAttributeService.deleteTransferAttributeByCode(transferPackage.getSn(), transferPackage.getDeviceCode());
                }else{
                    transferAttributeService.updateBySync(transferDeviceVo.getTransferAttributeEntities(), transferPackage.getSn(), transferPackage.getDeviceCode());
                    transferAttributeService.renewAttributes(transferPackage.getSn(), transferPackage.getDeviceCode());
                }


            } else if ("transferSnapshot".equals(syncEntity.getModule())) {
                snapshotTransferService.handleBySync(syncEntity, transferPackage.getSn());
                snapshotChannelService.handleBySync(syncEntity, transferPackage.getSn());
                SnapshotUtil.addAllInfo();
            } else if ("acquisitionMiddleware".equals(syncEntity.getModule())) {
                //同步接收过来的采集终端数据-这边代码已完成待测试
                logger.info("采集终端{}---------同步过来了", transferPackage.getSn());
                acquisitionMiddlewareService.handleBySync(syncEntity, transferPackage.getSn());
            } else if ("syncAll".equals(syncEntity.getModule())) {
                logger.info("定时同步开始---------");
                //船端定时同步接收过来的所有数据，需要解析对应各个数据结构-单独处理
                SyncAllEntity syncAllEntity = JSON.parseObject(syncEntity.getJsonObject(), SyncAllEntity.class);
                if (syncAllEntity != null) {
                    //设备状态相关保存
                    if (syncAllEntity.getDeviceStatuses() != null && syncAllEntity.getDeviceStatuses().size() > 0) {
                        syncAllEntity.getDeviceStatuses().stream().forEach(deviceStatus -> {
                            deviceService.handleStatusBySync(deviceStatus, transferPackage.getSn());
                            logger.info("定时同步结束------{}---",deviceStatus.toString());
                        });
                    }
                    //快照通道状态相关保存
                    if (syncAllEntity.getChannelStatuses() != null && syncAllEntity.getChannelStatuses().size() > 0) {
                        syncAllEntity.getChannelStatuses().stream().forEach(channelStatus -> {
                            snapshotChannelService.handleStatusBySync(channelStatus, transferPackage.getSn());
                            logger.info("定时同步结束------{}---",channelStatus.toString());
                            SnapshotUtil.addAllInfo();
                        });
                    }

                }
            }else if ("syncManage".equals(syncEntity.getModule())) {
                //同步接收过来的船舶终端管理信息数据-这边代码已完成待测试
                logger.info("船舶终端管理信息{}---------同步过来了", transferPackage.getSn());
                shipTerminalManageService.handleBySync(syncEntity, transferPackage.getSn());
            }
        } catch (Exception e) {
            logger.error("数据异常---{}", e);
        }
    }


    /**
     * 发送数据到kafka
     *
     * @param message
     */
    public void sendSync2Kafka(KafkaMessage message) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send("SHORE_SYNC_DATA_TOPIC", JSONObject.toJSONString(message));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", "SHORE_SYNC_DATA_TOPIC", ex.getMessage()));
    }

    public RedisObjVo selectRedisObj(RedisObjVo selectObj) {
        logger.info("redis获取数据库备用数据:---sn---{}",selectObj.getSn());
        RedisObjVo redisObjVo = redisObjMapper.selectRedisObj(selectObj);
        return redisObjVo;
    }
}
