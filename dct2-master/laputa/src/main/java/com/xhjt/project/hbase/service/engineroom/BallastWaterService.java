package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.BallastWater;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.BallastWaterVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * BallastWater 操作类
 */
@Service
@Transactional(readOnly = true)
public class BallastWaterService {

    private Logger logger = LoggerFactory.getLogger(BallastWaterService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 保存ballastWater
     *
     * @param ballastWater
     */
    @Transactional(rollbackFor = Exception.class)
    public BallastWaterVo save(String sn, BallastWater ballastWater, Long timeStamp) {
        BallastWaterVo ballastWaterVo = new BallastWaterVo();

        ballastWaterVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        ballastWaterVo.setLevel(ballastWater.getLevel());
        ballastWaterVo.setLevelP6(ballastWater.getLevelP6());
        ballastWaterVo.setLevelS6(ballastWater.getLevelS6());
        ballastWaterVo.setLevelP4(ballastWater.getLevelP4());
        ballastWaterVo.setLevelP5(ballastWater.getLevelP5());
        ballastWaterVo.setLevelS5(ballastWater.getLevelS5());
        ballastWaterVo.setLevelS4(ballastWater.getLevelS4());
        ballastWaterVo.setDraftFore(ballastWater.getDraftFore());
        ballastWaterVo.setDraftP(ballastWater.getDraftP());
        ballastWaterVo.setDraftAfter(ballastWater.getDraftAfter());
        ballastWaterVo.setDraftS(ballastWater.getDraftS());
        ballastWaterVo.setLevelP3(ballastWater.getLevelP3());
        ballastWaterVo.setLevelP2(ballastWater.getLevelP2());
        ballastWaterVo.setLevelS3(ballastWater.getLevelS3());
        ballastWaterVo.setLevelS2(ballastWater.getLevelS2());

        logger.info(" ballastWater开始保存到hbase，---{}", JSONObject.toJSONString(ballastWaterVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "ballast_water"), ballastWaterVo);

        return ballastWaterVo;
    }
}
