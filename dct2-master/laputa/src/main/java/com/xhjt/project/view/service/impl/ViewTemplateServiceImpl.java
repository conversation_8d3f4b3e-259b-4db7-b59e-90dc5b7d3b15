package com.xhjt.project.view.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.view.domain.ViewTemplateEntity;
import com.xhjt.project.view.mapper.ViewTemplateMapper;
import com.xhjt.project.view.service.ViewTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 模版管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ViewTemplateServiceImpl implements ViewTemplateService {
    @Autowired
    private ViewTemplateMapper viewTemplateMapper;

    /**
     * 查询模版信息
     *
     * @param templateId 模版ID
     * @return 模版信息
     */
    @Override
    public ViewTemplateEntity selectTemplateById(Long templateId) {
        ViewTemplateEntity template = new ViewTemplateEntity();
        template.setId(templateId);
        ViewTemplateEntity viewTemplateEntity = viewTemplateMapper.selectTemplate(template);
        if (StringUtils.isNotBlank(viewTemplateEntity.getModuleJson())) {
            viewTemplateEntity.setModuleList(JSON.parseArray(viewTemplateEntity.getModuleJson(), ViewTemplateEntity.ModuleVo.class));
        }

        return viewTemplateEntity;
    }

    /**
     * 查询模版列表
     *
     * @param template 模版信息
     * @return 模版集合
     */
    @Override
    public List<ViewTemplateEntity> selectTemplateList(ViewTemplateEntity template) {
        List<ViewTemplateEntity> list = viewTemplateMapper.selectTemplateList(template);
        list.forEach(entity -> {
            if (StringUtils.isNotBlank(entity.getModuleJson())) {
                entity.setModuleList(JSON.parseArray(entity.getModuleJson(), ViewTemplateEntity.ModuleVo.class));
            }
        });

        return list;
    }

    /**
     * 新增模版
     *
     * @param template 模版信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addTemplate(ViewTemplateEntity template) {
        template.setModuleJson(JSONObject.toJSONString(template.getModuleList()));
        template.setStatus(1);
        return viewTemplateMapper.addTemplate(template);
    }

    /**
     * 修改模版
     *
     * @param template 模版信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTemplate(ViewTemplateEntity template) {
        template.setModuleJson(JSONObject.toJSONString(template.getModuleList()));
        return viewTemplateMapper.updateTemplate(template);
    }

    /**
     * 启用
     *
     * @param id 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ViewTemplateEntity enable(Long id) {
        ViewTemplateEntity template = selectTemplateById(id);
        if (template == null) {
            return null;
        }
        if (template.getStatus() == 1) {
            return template;
        }
        template.setStatus(1);
        updateTemplate(template);

        return template;
    }

    /**
     * 修改设备
     *
     * @param id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ViewTemplateEntity disable(Long id) {
        ViewTemplateEntity template = selectTemplateById(id);
        if (template == null) {
            return null;
        }
        if (template.getStatus() == 0) {
            return template;
        }
        template.setStatus(0);
        updateTemplate(template);

        return template;
    }

    /**
     * 删除模版信息
     *
     * @param templateId 参数ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteTemplateById(Long templateId) {
        return viewTemplateMapper.deleteTemplateById(templateId);
    }

    /**
     * 批量删除参数信息
     *
     * @param templateIds 需要删除的参数ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteTemplateByIds(Long[] templateIds) {
        return viewTemplateMapper.deleteTemplateByIds(templateIds);
    }

}
