package com.xhjt.project.snapshot.mapper;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.project.snapshot.domain.SnapshotVideoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 传输管理
 *
 * <AUTHOR>
 */
public interface SnapshotLogMapper {

    /**
     * 查询传输管理列表
     *
     * @param snapshotLog
     * @return 传输管理集合
     */
    public List<SnapshotLogEntity> selectSnapshotLogList(SnapshotLogEntity snapshotLog);

    /**
     * 查询传输管理信息
     *
     * @param snapshotLog
     * @return 传输管理信息
     */
    public SnapshotLogEntity selectSnapshotLog(SnapshotLogEntity snapshotLog);

    /**
     * 添加
     *
     * @param snapshotLog
     * @return 结果
     */
    public int addSnapshotLog(SnapshotLogEntity snapshotLog);

    /**
     * 修改传输管理
     *
     * @param snapshotLog
     * @return 结果
     */
    public int updateSnapshotLog(SnapshotLogEntity snapshotLog);

    /**
     * 删除传输管理
     *
     * @param id
     * @return 结果
     */
    public int deleteSnapshotLogById(Long id);

    List<SnapshotLogEntity> selectSnapshotVideo(SnapshotVideoEntity snapshotVideoEntity);
}
