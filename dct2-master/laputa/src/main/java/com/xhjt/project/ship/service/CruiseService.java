package com.xhjt.project.ship.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.project.ship.domain.CruiseEntity;

import java.util.List;

/**
 * 航次管理 服务层
 *
 * <AUTHOR>
 */
public interface CruiseService {
    /**
     * 查询航次信息
     */
    List<CruiseEntity> selectNewCruiseList(CruiseEntity cruise);

    /**
     * 查询航次全部信息
     */
    List<CruiseEntity> selectAllNewCruise(CruiseEntity cruise);

    /**
     * 根据航次ID查询航次信息
     */
    CruiseEntity selectNewCruiseById(Long cruiseId);

    /**
     * 根据航次名称查询航次信息
     */
    CruiseEntity selectCruiseByCruiseName(CruiseEntity cruise);

    /**
     * 根据条件查询航次信息
     */
    List<CruiseEntity> selectCruiseNewByFactor(CruiseEntity cruise);

    /**
     * 根据sn条件查询航次信息
     */
    List<CruiseEntity> selectNewCruiseBySn(String sn);

    /**
     * 查询当前航次信息
     */
    CruiseEntity queryCurrentCruiseBySn(String sn);

    /**
     * 新增航次
     */
    int addCruise(JSONObject cruise);

    int updateNewCruise(CruiseEntity cruise);

    int deleteNewCruiseById(Long cruiseId);
}
