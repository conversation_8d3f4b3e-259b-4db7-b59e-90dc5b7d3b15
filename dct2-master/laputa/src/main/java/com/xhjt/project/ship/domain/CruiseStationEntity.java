package com.xhjt.project.ship.domain;

import com.xhjt.dctcore.commoncore.annotation.Excel;
import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 航次站位信息
 *
 * <AUTHOR>
 */
public class CruiseStationEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "ID")
    private Long id;

    /**
     * 航次ID
     */
    private Long cruiseId;

    /**
     * 关联的船只
     */
    private String sn;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 站位编号
     */
    private String stationCode;

    /**
     * 站位名称
     */
    private String stationName;

    /**
     * moduleJson
     */
    private String moduleJson;


    @Excel(name = "航次编号")
    private String cruiseCode;

    @Excel(name = "船只名称")
    private String shipName;

    /**
     * 航次名称
     */
    @Excel(name = "航次名称")
    private String cruiseName;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String latitude;

    /**
     * 计划到站时间
     */
    @Excel(name = "计划到站时间")
    private Long planArrivalTime;

    /**
     * 计划离站时间
     */
    @Excel(name = "计划离站时间")
    private Long planDepartureTime;

    /**
     * 实际到站时间
     */
    @Excel(name = "实际到站时间")
    private Long arrivalTime;

    /**
     * 实际离站时间
     */
    @Excel(name = "实际离站时间")
    private Long departureTime;

    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCruiseId() {
        return cruiseId;
    }

    public void setCruiseId(Long cruiseId) {
        this.cruiseId = cruiseId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getModuleJson() {
        return moduleJson;
    }

    public void setModuleJson(String moduleJson) {
        this.moduleJson = moduleJson;
    }

    public String getCruiseCode() {
        return cruiseCode;
    }

    public void setCruiseCode(String cruiseCode) {
        this.cruiseCode = cruiseCode;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getCruiseName() {
        return cruiseName;
    }

    public void setCruiseName(String cruiseName) {
        this.cruiseName = cruiseName;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Long getPlanArrivalTime() {
        return planArrivalTime;
    }

    public void setPlanArrivalTime(Long planArrivalTime) {
        this.planArrivalTime = planArrivalTime;
    }

    public Long getPlanDepartureTime() {
        return planDepartureTime;
    }

    public void setPlanDepartureTime(Long planDepartureTime) {
        this.planDepartureTime = planDepartureTime;
    }

    public Long getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(Long arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public Long getDepartureTime() {
        return departureTime;
    }

    public void setDepartureTime(Long departureTime) {
        this.departureTime = departureTime;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
