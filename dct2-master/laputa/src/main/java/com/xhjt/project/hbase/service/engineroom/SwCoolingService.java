package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.SwCoolingVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * 海水冷却系统数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class SwCoolingService {
    private Logger logger = LoggerFactory.getLogger(SwCoolingService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * @param swCoolingVo
     */
    @Transactional(rollbackFor = Exception.class)
    public SwCoolingVo save(String sn, SwCoolingVo swCoolingVo, Long timeStamp) {
        swCoolingVo.setId(hBaseDaoUtil.getRowKey(timeStamp));
        logger.info("swCoolingVo开始保存到hbase，---{}", JSONObject.toJSONString(swCoolingVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "sw_cool"), swCoolingVo);
        return swCoolingVo;
    }
}
