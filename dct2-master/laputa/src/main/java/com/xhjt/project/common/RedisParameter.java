package com.xhjt.project.common;

/**
 * @description: 所有参数整合
 * @author: rr
 * @create: 2020-09-07 16:54
 **/

public class RedisParameter {
    /*
     * @description: UDP消息接收参数
     */

    /**
     * 记录不同船，pazu的ip和port,key中后面带上sn号
     */
    public static String SHIP_PAZU_IP = "SHIP_PAZU_IP_";
    public static String SHIP_PAZU_PORT = "SHIP_PAZU_PORT_";

    /**
     * 按数据类型存储，拆包的未合并之前临时存储
     */
    public static String SHORE_RECEIVE_CODE_TEMPORARY = "SHORE_RECEIVE_CODE_TEMPORARY_";

    /**
     * 存储接收到的数据
     */
    public static String SHORE_RECEIVE_DATA = "SHORE_RECEIVE_DATA-";

    /**
     * 岸上丢失数据集合
     */
    public static String SHORE_LOSE_DATA_SET = "SHORE_LOSE_DATA_SET";

    /**
     * 拆包数据，最后存储时间
     */
    public static String SHORE_UNPACKING_NEWEST_TIME = "SHORE_UNPACKING_NEWEST_TIME_";
    /**
     * 拆包数据，最后补包时间
     */
    public static String SHORE_UNPACKING_REPAIR_TIME = "UNPACKING_REPAIR_TIME_";
    /**
     * 图片最新数据
     */
    public static String SHORE_UNPACKING_NEWEST_DATA = "SHORE_UNPACKING_NEWEST_DATA_";

    /**
     * 最大接收编号
     */
    public static String SHORE_MAX_RECEIVE_NUM = "SHORE_MAX_RECEIVE_NUM";

    /**
     * 最大校验编号
     */
    public static String SHORE_MAX_CHECK_NUM = "SHORE_MAX_CHECK_NUM";

    /**
     * 最大接收编号与最大校验编号的差
     */
    public static String SHORE_CHECK_INTERVAL_NUM = "SHORE_CHECK_INTERVAL_NUM";

    /*
     * @description:UDP丢包补数据服务
     */
    /**
     * 岸上丢失数据集合--已发送过一次(记录起来，等N分钟后再发)
     */
    public static String SHORE_LOSE_DATA_NUM_HASH = "SHORE_LOSE_DATA_NUM_HASH";

    /**
     * redis数据最新时间
     */
    public static String LATEST_DATE = "LATEST_DATE_";

    /**
     * redis 最新数据
     */
    public static String LATEST_DATA = "LATEST_DATA_";

    /**
     * 单位时间内接收到的数据的数量，丢失重传的数量
     */
    public static String DATA_AMOUNT = "DATA_AMOUNT-";

    /**
     * 数据接收情况检查時間
     */
    public static String DATA_CHECK_TIME = "_DATA_CHECK_TIME";



//---------------------------------------------数据接收日志----------------------------------------------------------------
    /**
     * 一秒钟接收数据总长度
     */
    public static String RECEIVE_TOTAL_LENGTH_S = "RECEIVE_TOTAL_LENGTH_S-";
    /**
     * 一秒钟接收数据总条数
     */
    public static String RECEIVE_LINES_S = "RECEIVE_LINES_S-";
    /**
     * 一秒钟接收补数据总长度
     */
    public static String RECEIVE_REPAIR_LENGTH_S = "RECEIVE_REPAIR_LENGTH_S-";
    /**
     * 一秒钟接收图片数据长度
     */
    public static String RECEIVE_PIC_LENGTH_S = "RECEIVE_PIC_LENGTH_S-";

    /**
     * 一分钟接收数据总长度
     */
    public static String RECEIVE_TOTAL_LENGTH_M = "RECEIVE_TOTAL_LENGTH_M-";
    /**
     * 一分钟接收数据总条数
     */
    public static String RECEIVE_LINES_M = "RECEIVE_LINES_M-";
    /**
     * 一分钟接收补数据总长度
     */
    public static String RECEIVE_REPAIR_LENGTH_M = "RECEIVE_REPAIR_LENGTH_M-";
    /**
     * 一分钟接收图片数据长度
     */
    public static String RECEIVE_PIC_LENGTH_M = "RECEIVE_PIC_LENGTH_M-";
    /**
     * 一分钟接收数据长度,是否已保存
     */
    public static String RECEIVE_DATA_LENGTH_SAVE = "RECEIVE_DATA_LENGTH_SAVE-";

    /**
     * 最新接收图片的时间
     */
    public static String LATEST_PICTURE_DATE = "LATEST_PICTURE_DATE-";

    /**
     * 传输监控的船只名称
     */
    public static String  TRANSMIT_DATA_SHIP_NAME = "TRANSMIT_DATA_SHIP_NAME-";

//---------------------------------------------数据接收日志----------------------------------------------------------------



//---------------------------------------------台风----------------------------------------------------------------

    /**
     * 台风列表
     */
    public static String TYPHOON_LIST = "TYPHOON_LIST-";
    /**
     * 台风信息列表
     */
    public static String TYPHOON_INFO = "TYPHOON_INFO-";

//---------------------------------------------台风----------------------------------------------------------------


//---------------------------------------------机舱数据----------------------------------------------------------------

    /**
     * 机舱数据结构
     */
    public static String ENGINE_ROOM_CONFIG = "ENGINE_ROOM_CONFIG-";

    /**
     * 机舱数据
     */
    public static String ENGINE_ROOM_DATA = "ENGINE_ROOM_DATA-";

    /**
     * 机舱数据接收时间
     */
    public static String ENGINE_ROOM_DATE = "ENGINE_ROOM_DATE";

    /**
     * 机舱数据条数
     */
    public static String ENGINE_ROOM_DATA_SIZE = "ENGINE_ROOM_DATA_SIZE-";

    /**
     * 机舱数据查询临时链表--倒序
     */
    public static String ENGINE_ROOM_TEMP_ORDER = "ENGINE_ROOM_TEMP_ORDER-";

//---------------------------------------------机舱数据----------------------------------------------------------------


    //-----------------------------------------------------------------

    /**
     * 所有启用的船只sn号
     */
    public static String ALL_ENABLE_SHIP_SN = "ALL_ENABLE_SHIP_SN";
    /**
     * 所有启用的设备
     */
    public static String ALL_ENABLE_DEVICE_CODE = "ALL_ENABLE_DEVICE_CODE";
}
