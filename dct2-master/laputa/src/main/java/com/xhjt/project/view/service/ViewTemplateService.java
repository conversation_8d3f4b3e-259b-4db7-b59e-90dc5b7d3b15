package com.xhjt.project.view.service;

import com.xhjt.project.view.domain.ViewTemplateEntity;

import java.util.List;

/**
 * 模版管理 服务层
 *
 * <AUTHOR>
 */
public interface ViewTemplateService {
    /**
     * 查询模版信息
     *
     * @param templateId 模版ID
     * @return 模版信息
     */
    public ViewTemplateEntity selectTemplateById(Long templateId);

    /**
     * 查询模版列表
     *
     * @param template 模版信息
     * @return 模版集合
     */
    public List<ViewTemplateEntity> selectTemplateList(ViewTemplateEntity template);

    /**
     * 新增模版
     *
     * @param template 模版信息
     * @return 结果
     */
    public int addTemplate(ViewTemplateEntity template);

    /**
     * 修改模版
     *
     * @param template 模版信息
     * @return 结果
     */
    public int updateTemplate(ViewTemplateEntity template);

    /**
     * 启用
     *
     * @param id
     * @return
     */
    public ViewTemplateEntity enable(Long id);

    /**
     * 停用
     *
     * @param id
     * @return
     */
    public ViewTemplateEntity disable(Long id);

    /**
     * 删除模版信息
     *
     * @param templateId 参数ID
     * @return 结果
     */
    public int deleteTemplateById(Long templateId);

    /**
     * 批量删除参数信息
     *
     * @param templateIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteTemplateByIds(Long[] templateIds);

}
