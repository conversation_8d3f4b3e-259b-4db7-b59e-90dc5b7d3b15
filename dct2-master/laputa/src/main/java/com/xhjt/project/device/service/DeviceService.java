package com.xhjt.project.device.service;

import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.project.data.service.DeviceDataService;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.DeviceStatus;

import java.util.List;

/**
 * 设备管理 服务层
 *
 * <AUTHOR>
 */
public interface DeviceService {
    /**
     * 查询设备信息
     *
     * @param deviceId 设备ID
     * @return 设备信息
     */
    public DeviceEntity selectDeviceById(Long deviceId);

    /**
     * 查询设备列表
     *
     * @param device 设备信息
     * @return 设备集合
     */
    public List<DeviceEntity> selectDeviceList(DeviceEntity device);

    /**
     * 查询设备列表
     *
     * @param sn
     * @return 设备集合
     */
    public List<DeviceEntity> queryListBySn(String sn);

    /**
     * 查询设备列表
     *
     * @return 设备集合
     */
    public List<DeviceEntity> selectAllDevice();

    /**
     * 根据IP和Port查询设备信息
     *
     * @param code
     * @return 设备信息
     */
    public DeviceEntity selectByCodeAndSn(String sn, String code);

    /**
     * 新增设备
     *
     * @param device 设备信息
     * @return 结果
     */
    public int addDevice(DeviceEntity device);

    /**
     * 修改设备
     *
     * @param device 设备信息
     * @return 结果
     */
    public int updateDevice(DeviceEntity device);

    /**
     * 删除设备信息
     *
     * @param deviceId 参数ID
     * @return 结果
     */
    public int deleteDeviceById(Long deviceId);

    /**
     * 批量删除参数信息
     *
     * @param deviceIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] deviceIds);

    /**
     * 校验code是否唯一
     *
     * @param device 参数信息
     * @return 结果
     */
    public String checkCodeUnique(DeviceEntity device);

    /**
     * 启用
     *
     * @param id
     * @return
     */
    public DeviceEntity enable(Long id);

    /**
     * 停用
     *
     * @param id
     * @return
     */
    public DeviceEntity disable(Long id);

    public void handleBySync(SyncEntity syncEntity,String sn);

    public DeviceEntity selectByMac(String mac);

    /**
     * 传输状态修改
     * @param id
     * @param status
     * @return
     */
   public DeviceEntity changeTrStatus(Long id, Integer status);

    public void handleStatusBySync(DeviceStatus deviceStatus, String sn);

}
