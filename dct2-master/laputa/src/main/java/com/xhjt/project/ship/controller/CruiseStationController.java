package com.xhjt.project.ship.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xhjt.common.constant.HttpStatus;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.ServletUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.ip.IpUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.service.CruiseService;
import com.xhjt.project.ship.service.CruiseStationService;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.domain.SysModule;
import com.xhjt.project.system.service.ISysDeptService;
import com.xhjt.project.system.service.ISysModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 航次站位信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cruiseStation")
public class CruiseStationController extends BaseController {

    @Autowired
    private CruiseStationService cruiseStationService;

    @Autowired
    private ShipService shipService;

    @Autowired
    private ISysModuleService moduleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysOperLogService iSysOperLogService;

    @Autowired
    private CruiseService cruiseService;

    /**
     * 获取航次信息动态列
     */
    @GetMapping("/getColumn")
    public AjaxResult getColumn() {

        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(2);
        sysModule.setTableShow(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);

        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o:moduleList) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("id",o.getId().toString());
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("characterType",o.getCharacterType()+"");
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });

        return AjaxResult.success(res);
    }


    /**
     * 获取新增表单
     */
    @GetMapping(value = "/addForm")
    public AjaxResult getAddForm(){
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(2);
        sysModule.setFormShow(1);
        sysModule.setIsRequired(0);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o: list) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("isRequired",o.getIsRequired());
            map.put("value",null);
            map.put("characterType",o.getCharacterType());
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }

    /**
     * 获取新增表单-必填
     */
    @GetMapping(value = "/addForm2")
    public AjaxResult getAddForm2(){
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(2);
        sysModule.setFormShow(1);
        sysModule.setIsRequired(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o: list) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("isRequired",o.getIsRequired());
            map.put("value",null);
            map.put("characterType",o.getCharacterType());
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }
    /**
     * 获取航次站位信息列表
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(CruiseStationEntity cruiseStation) {
        SysModule sysModule1 = new SysModule();
        sysModule1.setType(2);
        sysModule1.setTableShow(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule1.setComIds(comIds);
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule1);

        List<String> properties = new ArrayList<>();//属性，用于判断
        for (SysModule sysModule:moduleList) {
            properties.add(sysModule.getProperty());
        }
        cruiseStation.setDeptId(deptId);
        List<CruiseStationEntity> list =  cruiseStationService.selectNewCruiseStationList(cruiseStation);
        startPage();
        List<HashMap> resultList = new ArrayList<>();//最终返回
        for (CruiseStationEntity s:list) {
            HashMap<String,Object> map= new HashMap<>();
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(properties.contains(entry.getKey())){//如果属于动态列中
                        map.put("id",s.getId());
                        Object value = entry.getValue();
                        SysModule sysModule = new SysModule();
                        sysModule.setProperty(entry.getKey());
                        sysModule.setType(2);
                        SysModule selectModule = moduleService.selectModuleListByProperty(sysModule);
                        if (!entry.getValue().equals("") && entry.getValue() != null){
                            if(selectModule.getCharacterType() == 1){// 1.时间
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                value = sdf.format(new Date(Long.parseLong(value+"")));
                            } else if(selectModule.getCharacterType() == 3) {// 3.日期
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                value = sdf.format(new Date(Long.parseLong(value+"")));
                            }
                        }
                        map.put(entry.getKey(),value);//存入map
                        map.put(entry.getKey(),entry.getValue());//存入map
                        map.put(entry.getKey(),value);//存入map
                    }
                }
                resultList.add(map);
            }
        }
        //分页
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(resultList);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    @Log(title = "航次管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:export')")
    @GetMapping("/export")
    public AjaxResult export(CruiseStationEntity cruiseStation) {
        SysModule sysModule1 = new SysModule();
        sysModule1.setType(2);
        sysModule1.setTableShow(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule1.setComIds(comIds);
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule1);

        List<String> properties = new ArrayList<>();//属性，用于判断
        for (SysModule sysModule:moduleList) {
            properties.add(sysModule.getProperty());
        }
        cruiseStation.setDeptId(deptId);
        List<CruiseStationEntity> list =  cruiseStationService.selectNewCruiseStationList(cruiseStation);
        startPage();
        List<HashMap> resultList = new ArrayList<>();//最终返回
        for (CruiseStationEntity s:list) {
            HashMap<String,Object> map= new HashMap<>();
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(properties.contains(entry.getKey())){//如果属于动态列中
                        map.put("id",s.getId());
                        Object value = entry.getValue();
                        if(entry.getKey().contains("Time")){
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            value = sdf.format(new Date(Long.parseLong(value+"")));

                        }
                        map.put(entry.getKey(),value);//存入map
                    }
                }
                resultList.add(map);
            }
        }
        List<CruiseStationEntity> resStation = new ArrayList<>();
        for(HashMap hashMap : resultList){
            CruiseStationEntity cruiseStationEntity = JSON.parseObject(JSON.toJSONString(hashMap), CruiseStationEntity.class);
            resStation.add(cruiseStationEntity);
        }

        ExcelUtil<CruiseStationEntity> util = new ExcelUtil<CruiseStationEntity>(CruiseStationEntity.class);
        return util.exportExcel(resStation, "站位数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(cruiseStationService.selectNewCruiseStationById(id));
    }

    /**
     * 新增航次站位信息
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:add')")
    @Log(title = "航次站位新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody JSONObject cruiseStation) {
        CruiseStationEntity cruiseStation1 = new CruiseStationEntity();
        String stationCode = "";
        List<HashMap> editRequiredForm = (List<HashMap>) cruiseStation.get("editRequiredForm");
        for(HashMap hashMap : editRequiredForm){
            if(hashMap.get("property").equals("code")){
                stationCode = hashMap.get("value")+"";
                break;
            }
        }

        cruiseStation1.setStationCode(stationCode);
//        List<CruiseStationEntity> list = cruiseStationService.selectCruiseStationNewByFactor(cruiseStation1);
//        if (list.size() > 0) {
//            return AjaxResult.error("站位编号已存在");
//        }

        int row = cruiseStationService.addNewCruise(cruiseStation);
        return toAjax(row);
    }

    /**
     * 修改航次站位信息
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody JSONObject cruiseStationEntity) {
        //获取数据库信息 ->selectCruise 如果id不对，显示数据不存在
        CruiseStationEntity selectCruise = cruiseStationService.selectNewCruiseStationById(Long.parseLong(cruiseStationEntity.get("stationId").toString())) ;
        if (selectCruise == null) {
            throw new CustomException("数据不存在");
        }
        //获取表单信息
        CruiseStationEntity cruiseStation = new CruiseStationEntity();
        List<HashMap> list = (List) cruiseStationEntity.get("editNoRequiredForm");
        List<HashMap> list2 = (List) cruiseStationEntity.get("editRequiredForm");
        for(int i = 0;i < list2.size();i++){
            list.add(list2.get(i));
        }
        List<HashMap> res = new ArrayList<>();
        for(int i = 0;i < list.size();i++){
            HashMap<String,Object> map = new HashMap();
            if(list.get(i).get("value")!=null && !list.get(i).get("value").equals("null")){
                map.put("property",list.get(i).get("property")+"");
                map.put("value",list.get(i).get("value"));
                res.add(map);
            }
        }

        Map map = res.stream().collect(Collectors.toMap(p->p.get("property"), p->p.get("value")));//moduleJSon(Map)
        String updateBy = SecurityUtils.getUsername();
        map.put("deptId",SecurityUtils.getLoginUser().getUser().getDeptId());
        map.put("updateBy",updateBy);
        Date date = new Date();
        map.put("updateTime",date);
        cruiseStation.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());

        String s = JSONObject.toJSONString(map);
        cruiseStation.setModuleJson(s);
        cruiseStation.setSn(selectCruise.getSn());
//        cruiseStation.setCruiseId(Long.parseLong(cruiseStationEntity.get("cruiseId")+""));

        //两者对比moduleJson -> form如果没有select的（比如cereateBy、cereateTime）
        String formModuleJson = cruiseStation.getModuleJson();
        JSONObject formJsonObject = JSONObject.parseObject(formModuleJson);
        String selectModuleJson = selectCruise.getModuleJson();
        JSONObject selectJsonObject = JSONObject.parseObject(selectModuleJson);
        for (Map.Entry<String, Object> selectEntry: selectJsonObject.entrySet()) {
            if(!formJsonObject.containsKey(selectEntry.getKey()) && selectEntry.getKey()!=null && StringUtils.isNotBlank(selectEntry.getValue()+"")){
                formJsonObject.put(selectEntry.getKey(),selectEntry.getValue());
            }
        }

        cruiseStation.setModuleJson(formJsonObject+"");
        cruiseStation.setId(Long.parseLong(cruiseStationEntity.get("stationId")+""));
        int row = cruiseStationService.updateNewCruiseStation(cruiseStation);
//        if (row > 0) {
//            //同步修改操作
//            ShipEntity ship = shipService.selectShipBySn(cruiseStation.getSn());
//            SynchroEntity synchro = new SynchroEntity();
//            synchro.setObject(cruiseStation);
//            synchro.setAction("update");
//            synchro.setModule("cruise");
//            synchroService.synchroDataRequest(synchro, ship.getSn());
//        }

        //打印日志
        String log = "";
        for (Map.Entry<String, Object> entry: formJsonObject.entrySet()) {
            for (Map.Entry<String, Object> entry2: selectJsonObject.entrySet()) {
                if(entry.getKey().equals(entry2.getKey()) && !entry.getValue().equals(entry2.getValue()) && !entry.getKey().equals("updateTime")){
                    log = log + "{" + entry.getKey() + "的[" + entry2.getValue() + "]改为[" + entry.getValue() +" ]}";
                }
            }
        }
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setTitle("站位修改");
        sysOperLog.setMethod("修改");
        sysOperLog.setBusinessType(2);
        sysOperLog.setRequestMethod("PUT");
        sysOperLog.setOperatorType(1);
        sysOperLog.setOperUrl("/cruiseStation");
        sysOperLog.setOperLocation("内网IP");
        sysOperLog.setOperName(SecurityUtils.getUsername());
        sysOperLog.setOperParam(log);
        sysOperLog.setJsonResult("{\"msg\":\"操作成功\",\"code\":200}");
        sysOperLog.setStatus(0);
        sysOperLog.setOperTime(new Date());
        sysOperLog.setOperIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        iSysOperLogService.insertOperlog(sysOperLog);

        return toAjax(row);
    }

    /**
     * 修改-获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:query')")
    @GetMapping(value = "/NoRequired/{id}")
    public AjaxResult getInfo1(@PathVariable Long id) {
        CruiseStationEntity cruiseStation = cruiseStationService.selectNewCruiseStationById(id);
        JSONObject jsonObject = JSONObject.parseObject(cruiseStation.getModuleJson());//将moduleJson String类型转为json
        SysModule sysModule = new SysModule();
        sysModule.setType(2);
        sysModule.setIsRequired(0);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        while(comId != 0){
            SysDept sysDept1 = deptService.selectDeptById(comId);
            comId = sysDept1.getParentId();
            comIds = comIds + "," + comId.toString();
        }
        sysModule.setComIds(comIds);
        sysModule.setComId(sysDept.getDeptId());
        Map map1 = moduleService.getModuleShowByParam(sysModule);
        if(map1 == null){
            sysModule.setComId(sysDept.getParentId());
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        if(map1 == null){
            sysModule.setComId(Long.parseLong(100+""));
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        String property = map1.get("property")+"";
        String[] propertyArr = property.split(",");
        for(int i = 0;i < propertyArr.length;i++){
            if(!jsonObject.containsKey(propertyArr[i])){
                jsonObject.put(propertyArr[i],null);
            }
        }
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String,Object>> list = new ArrayList<>();

        for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
            for(SysModule module: moduleList){
                if(entry.getKey().equals(module.getProperty()) && module.getIsRequired() == 0 && module.getFormShow() == 1){
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("property",entry.getKey());
                    map.put("value",entry.getValue());
                    map.put("label",module.getLabel());
                    map.put("isRequired",module.getIsRequired());
                    map.put("characterType",module.getCharacterType());
                    map.put("sortNum",module.getSortNum());
                    list.add(map);
                }
            }
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 修改-获取详细信息-必填
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:query')")
    @GetMapping(value = "/Required/{id}")
    public AjaxResult getInfo2(@PathVariable Long id) {
        CruiseStationEntity cruiseStation = cruiseStationService.selectNewCruiseStationById(id);
        JSONObject jsonObject = JSONObject.parseObject(cruiseStation.getModuleJson());//将moduleJson String类型转为json
        SysModule sysModule = new SysModule();
        sysModule.setType(2);
        sysModule.setIsRequired(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        sysModule.setComId(sysDept.getDeptId());
        Map map1 = moduleService.getModuleShowByParam(sysModule);
        if(map1 == null){
            sysModule.setComId(sysDept.getParentId());
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        if(map1 == null){
            sysModule.setComId(Long.parseLong(100+""));
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        String property = map1.get("property")+"";
        String[] propertyArr = property.split(",");
        for(int i = 0;i < propertyArr.length;i++){
            if(!jsonObject.containsKey(propertyArr[i])){
                jsonObject.put(propertyArr[i],null);
            }
        }
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String,Object>> list = new ArrayList<>();

        for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
            for(SysModule module: moduleList){
                if(entry.getKey().equals(module.getProperty()) && module.getIsRequired() == 1 && module.getFormShow() == 1){
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("property",entry.getKey());
                    map.put("value",entry.getValue());
                    map.put("label",module.getLabel());
                    map.put("isRequired",module.getIsRequired());
                    map.put("characterType",module.getCharacterType());
                    map.put("sortNum",module.getSortNum());
                    list.add(map);
                }
            }
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 删除站位信息
     */
    @PreAuthorize("@ss.hasPermi('cruiseStation:info:remove')")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int row = 0;

        //打印日志
        String log = "";
        for (Long id : ids) {
            log = log + "[" + cruiseStationService.selectNewCruiseStationById(id).getStationName() + "]";
        }
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setTitle("站位删除");
        sysOperLog.setMethod("删除");
        sysOperLog.setBusinessType(3);
        sysOperLog.setRequestMethod("DELETE");
        sysOperLog.setOperatorType(1);
        sysOperLog.setOperUrl("/cruiseStation");
        sysOperLog.setOperName(SecurityUtils.getUsername());
        sysOperLog.setOperLocation("内网IP");
        sysOperLog.setOperParam(log);
        sysOperLog.setJsonResult("{\"msg\":\"操作成功\",\"code\":200}");
        sysOperLog.setStatus(0);
        sysOperLog.setOperTime(new Date());
        sysOperLog.setOperIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        iSysOperLogService.insertOperlog(sysOperLog);

        //同步删除操作
        for (Long id : ids) {
            row = cruiseStationService.deleteNewCruiseStationById(id);
        }

        return toAjax(row);
    }

    public String getComIds(Long comId, String comIds){
        while(comId != 0){
            SysDept sysDept1 = deptService.selectDeptById(comId);
            comId = sysDept1.getParentId();
            comIds = comIds + "," + comId.toString();
        }
        return comIds;
    }

}
