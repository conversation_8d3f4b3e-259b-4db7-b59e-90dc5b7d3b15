package com.xhjt.project.ship.controller;

import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.ship.domain.TravelActionNodeEntity;
import com.xhjt.project.ship.service.TravelActionNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行程动作节点信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/travelActionNode")
public class TravelActionNodeController extends BaseController {

    @Autowired
    private TravelActionNodeService travelActionNodeService;

    /**
     * 获取行程动作节点信息列表
     */
    @PreAuthorize("@ss.hasPermi('travelActionNode:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(TravelActionNodeEntity travelActionNode) {
        startPage();
        List<TravelActionNodeEntity> list = travelActionNodeService.selectTravelActionNodeList(travelActionNode);
        return getDataTable(list);
    }

    @Log(title = "行程动作节点管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('travelActionNode:info:export')")
    @GetMapping("/export")
    public AjaxResult export(TravelActionNodeEntity travelActionNode) {
        List<TravelActionNodeEntity> list = travelActionNodeService.selectTravelActionNodeList(travelActionNode);
        ExcelUtil<TravelActionNodeEntity> util = new ExcelUtil<TravelActionNodeEntity>(TravelActionNodeEntity.class);
        return util.exportExcel(list, "行程动作节点数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('travelActionNode:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(travelActionNodeService.selectTravelActionNodeById(id));
    }

    /**
     * 删除航次信息
     */
    @PreAuthorize("@ss.hasPermi('travelActionNode:info:remove')")
    @Log(title = "行程动作节点删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int row = 0;
        //同步删除操作
        for (Long id : ids) {
            TravelActionNodeEntity travelActionNodeEntity = travelActionNodeService.selectTravelActionNodeById(id);
            row = travelActionNodeService.deleteTravelActionNodeById(id);
        }
        return toAjax(row);

    }

}
