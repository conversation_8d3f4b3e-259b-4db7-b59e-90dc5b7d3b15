package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.TankLevelVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * 油舱液位数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class TankLevelService {

    private Logger logger = LoggerFactory.getLogger(TankLevelService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * @param tankLevelVo
     */
    @Transactional(rollbackFor = Exception.class)
    public TankLevelVo save(String sn, TankLevelVo tankLevelVo, Long timeStamp) {
        tankLevelVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        logger.info(" tankLevelVo开始保存到hbase，---{}", JSONObject.toJSONString(tankLevelVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "tank_level"), tankLevelVo);

        return tankLevelVo;
    }
}
