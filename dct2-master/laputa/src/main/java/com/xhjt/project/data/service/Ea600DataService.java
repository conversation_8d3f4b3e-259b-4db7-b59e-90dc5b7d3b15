package com.xhjt.project.data.service;

import com.xhjt.common.constant.HttpStatus;
import com.xhjt.dctcore.commoncore.domain.hbase.Ea600HbaseVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.web.page.TableDataInfo;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * ea600数据信息 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class Ea600DataService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    public List<Ea600HbaseVo> selectEa600List(Long startTime, Long endTime, String tableName) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new Ea600HbaseVo(), tableName, rowList);
    }

    public TableDataInfo queryEa600Page(Long startTime, Long endTime, String tableName, int currentPage, int pageSize, String sort) {
        List<Ea600HbaseVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new Ea600HbaseVo(), tableName, list, rowList, currentPage, pageSize, sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }
}
