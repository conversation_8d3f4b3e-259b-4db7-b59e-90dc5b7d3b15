package com.xhjt.project.common;

import com.xhjt.common.constant.HttpStatus;
import com.xhjt.framework.web.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * class
 *
 * <AUTHOR>
 */
@Service
public class CommonService<T> {


    /**
     * 给list列表分页
     *
     * @param sourceList
     * @param currentPage
     * @param pageSize
     * @return
     */
    public TableDataInfo pagingList(List<T> sourceList, int currentPage, int pageSize) {
        TableDataInfo rspData = new TableDataInfo();
        int firstIndex = 0;
        List<T> list = new ArrayList<>();
        if (currentPage == 1) {
            firstIndex = 0;
        } else {
            firstIndex = (currentPage - 1) * pageSize;
        }
        for (int i = firstIndex; i < firstIndex + pageSize; i++) {
            if (sourceList.size() < i + 1) {
                break;
            }
            list.add(sourceList.get(i));
        }
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setTotal(sourceList.size());
        rspData.setRows(list);
        return rspData;
    }
}
