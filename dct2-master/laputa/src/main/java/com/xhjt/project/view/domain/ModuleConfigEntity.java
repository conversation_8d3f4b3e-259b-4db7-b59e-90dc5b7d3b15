package com.xhjt.project.view.domain;


import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 模块配置
 *
 * <AUTHOR>
 */
public class ModuleConfigEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 方案ID
     */
    private Long schemeId;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 展示模式（实时数据、折线图、饼图）
     */
    private Integer mode;

    /**
     * 属性
     */
    private String attribute;

    /**
     * 粒度
     */
    private Integer grading;

    /**
     * 时间范围，往前多少小时
     */
    private Integer timeFrame;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getSchemeId() {
        return schemeId;
    }

    public void setSchemeId(Long schemeId) {
        this.schemeId = schemeId;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }

    public Integer getGrading() {
        return grading;
    }

    public void setGrading(Integer grading) {
        this.grading = grading;
    }

    public Integer getTimeFrame() {
        return timeFrame;
    }

    public void setTimeFrame(Integer timeFrame) {
        this.timeFrame = timeFrame;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }
}
