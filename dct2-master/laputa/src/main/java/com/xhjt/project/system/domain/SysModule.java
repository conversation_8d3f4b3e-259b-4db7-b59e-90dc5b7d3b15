package com.xhjt.project.system.domain;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.annotation.Excel;
import com.xhjt.dctcore.commoncore.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

public class SysModule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    private Long id;

    @Excel(name = "公司名称")
    private String comName;

    @Excel(name = "模板名称")
    private String name;

    /**
     * 列名
     */
    @Excel(name = "列名")
    private String label;

    /**
     * 模板编码
     */
    private String property;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    @Excel(name = "创建时间")
    private String exportCreateTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 模板类型 0:船只 1：航次 2：站位
     */
    private Integer type;

    /**
     * 字段存储方式 0:固定 1:可变
     */
    private Integer fieldType;

    /**
     * 列表是否显示 0:不显示 1：显示
     */
    private Integer tableShow;

    /**
     * 表单是否显示 0:不显示 1：显示
     */
    private Integer formShow;

    /**
     * 字符类型 0：字符串 1：时间 2：数值
     */
    private Integer characterType;

    /**
     *  排列顺序
     */
    private Integer sortNum;

    /**
     * 是否必填 0：否 1：是
     */
    private Integer isRequired;

    /**
     * 公司ID
     */
    private Long comId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 公司ID集合
     */
    private String comIds;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 模板
     */
    private List<SysModule> moduleList;

    public String getComName() {
        return comName;
    }

    public void setComName(String comName) {
        this.comName = comName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getDeptId() {
        return deptId;
    }

    public String getExportCreateTime() {
        return exportCreateTime;
    }

    public void setExportCreateTime(String exportCreateTime) {
        this.exportCreateTime = exportCreateTime;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getComIds() {
        return comIds;
    }

    public void setComIds(String comIds) {
        this.comIds = comIds;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getFieldType() {
        return fieldType;
    }

    public void setFieldType(Integer fieldType) {
        this.fieldType = fieldType;
    }

    public Integer getTableShow() {
        return tableShow;
    }

    public void setTableShow(Integer tableShow) {
        this.tableShow = tableShow;
    }

    public Integer getFormShow() {
        return formShow;
    }

    public void setFormShow(Integer formShow) {
        this.formShow = formShow;
    }

    public Integer getCharacterType() {
        return characterType;
    }

    public void setCharacterType(Integer characterType) {
        this.characterType = characterType;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public Integer getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public Long getComId() {
        return comId;
    }

    public void setComId(Long comId) {
        this.comId = comId;
    }

    public List<SysModule> getModuleList() {
        return moduleList;
    }

    public void setModuleList(List<SysModule> moduleList) {
        this.moduleList = moduleList;
    }
}
