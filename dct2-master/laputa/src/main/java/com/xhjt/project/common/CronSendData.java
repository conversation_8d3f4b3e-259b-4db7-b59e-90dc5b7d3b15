package com.xhjt.project.common;

import com.alibaba.fastjson.JSONArray;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.project.monitor.domain.ReceiveLog;
import com.xhjt.project.monitor.service.IReceiveLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.websocket.Session;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 定时任务
 */
@Component
public class CronSendData implements Runnable {

    private Logger logger = LoggerFactory.getLogger(CronSendData.class);

    private ConcurrentHashMap<Session, String> map;

    private String sn;

    private Long recordTime = 0L;
    private Long compareDate = 0L;

    public CronSendData() {
    }

    public CronSendData(ConcurrentHashMap<Session, String> map, String sn) {
        this.map = map;
        this.sn = sn;
    }

    @Override
    public void run() {

        RedisTemplate redisTemplate = SpringUtils.getBean("redisTemplate");
        IReceiveLogService receiveLogService = SpringUtils.getBean(IReceiveLogService.class);
        ValueOperations<String, Integer> operation = redisTemplate.opsForValue();
        SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();
        ValueOperations<String, Long> operations = redisTemplate.opsForValue();
        try {
            for (Map.Entry<Session, String> entry : map.entrySet()) {
                switch (entry.getValue()) {
                    case "minute":
                        List<ReceiveLog> list = receiveLogService.selectReceiveLogList();
                        ReceiveLog receive = list.get(0);
                        if (receive.getRecordTime() > recordTime) {
                            receive.setLoseDataLength(opsForSet.size(RedisParameter.SHORE_LOSE_DATA_SET));
                            entry.getKey().getAsyncRemote().sendText(JSONArray.toJSONString(receive));
                            recordTime = receive.getRecordTime();
                        }
                        break;

                    case "second":

                        Long second = System.currentTimeMillis() / 1000 - 1;
                        ReceiveLog receiveLog = new ReceiveLog();
                        receiveLog.setTotalLength(operation.get(RedisParameter.RECEIVE_TOTAL_LENGTH_S + sn + "-" + second));
                        receiveLog.setTotalLines(operation.get(RedisParameter.RECEIVE_LINES_S + sn + "-" + second));
                        receiveLog.setRepairLength(operation.get(RedisParameter.RECEIVE_REPAIR_LENGTH_S + sn + "-" + second));
                        receiveLog.setPicLength(operation.get(RedisParameter.RECEIVE_PIC_LENGTH_S + sn + "-" + second));
                        receiveLog.setRecordTime(second * 1000);
                        receiveLog.setLoseDataLength(opsForSet.size(RedisParameter.SHORE_LOSE_DATA_SET));
                        entry.getKey().getBasicRemote().sendText(JSONArray.toJSONString(receiveLog));
                        break;
                    case "engineRoom":
                        logger.info("监控机舱数据---------");
                        Long engineroomDate = operations.get(RedisParameter.ENGINE_ROOM_DATE);
                        if (engineroomDate == null) {
                            return;
                        }
                        logger.info("监控机舱数据---------{}", engineroomDate);
                        if (engineroomDate > compareDate) {
                            Map<Long, Integer> map = new HashMap<>();
                            map.put(engineroomDate, operation.get(RedisParameter.ENGINE_ROOM_DATA_SIZE + "aa0001-" + engineroomDate));
                            compareDate = engineroomDate;
                            logger.info("监控机舱数据---------{}", operation.get(RedisParameter.ENGINE_ROOM_DATA_SIZE + "aa0001-" + engineroomDate));
                        }
                        logger.info("监控机舱数据---------{}", map.toString());
                        entry.getKey().getBasicRemote().sendText(JSONArray.toJSONString(map));
                        break;
                    default:
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
