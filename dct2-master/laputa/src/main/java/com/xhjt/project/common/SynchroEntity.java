package com.xhjt.project.common;


import com.alibaba.fastjson.JSON;
import com.xhjt.project.ship.domain.CruiseEntity;

/**
 * 同步数据信息
 *
 * <AUTHOR>
 */
public class SynchroEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 同步的数据对象
     */
    private Object object;

    /**
     *  动作：增删改
     */
    private String action;

    /**
     * 模块
     */
    private String module;

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

}
