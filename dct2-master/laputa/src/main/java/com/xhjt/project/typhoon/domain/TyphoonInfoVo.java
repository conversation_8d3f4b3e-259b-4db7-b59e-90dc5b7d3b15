package com.xhjt.project.typhoon.domain;

import java.util.List;

/**
 * class
 *
 * <AUTHOR>
 */
public class TyphoonInfoVo {

    /**
     * 台风编号
     */
    private String tfid;

    /**
     * 中文名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String enname;

    /**
     * 是否活跃
     */
    private String isactive;

    /**
     * 开始时间
     */
    private String starttime;

    /**
     * 结束时间
     */
    private String endtime;

    /**
     * 警告级别
     */
    private String warnlevel;

    /**
     * 中心纬度
     */
    private String centerlat;

    /**
     * 中心经度
     */
    private String centerlng;

    /**
     * 登陆陆地
     */
    private List<TyphoonLand> land;

    /**
     * 点位
     */
    private List<TyphoonPoint> points;

    public String getTfid() {
        return tfid;
    }

    public void setTfid(String tfid) {
        this.tfid = tfid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnname() {
        return enname;
    }

    public void setEnname(String enname) {
        this.enname = enname;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getWarnlevel() {
        return warnlevel;
    }

    public void setWarnlevel(String warnlevel) {
        this.warnlevel = warnlevel;
    }

    public String getCenterlat() {
        return centerlat;
    }

    public void setCenterlat(String centerlat) {
        this.centerlat = centerlat;
    }

    public String getCenterlng() {
        return centerlng;
    }

    public void setCenterlng(String centerlng) {
        this.centerlng = centerlng;
    }

    public List<TyphoonLand> getLand() {
        return land;
    }

    public void setLand(List<TyphoonLand> land) {
        this.land = land;
    }

    public List<TyphoonPoint> getPoints() {
        return points;
    }

    public void setPoints(List<TyphoonPoint> points) {
        this.points = points;
    }
}
