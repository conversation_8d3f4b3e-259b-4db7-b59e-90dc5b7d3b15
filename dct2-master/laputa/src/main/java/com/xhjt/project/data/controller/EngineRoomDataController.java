package com.xhjt.project.data.controller;

import com.alibaba.fastjson.JSON;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.*;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.data.service.EngineRoomDataService;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.service.CruiseStationService;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 机舱数据 信息操作处理
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/data/engineRoom")
public class EngineRoomDataController extends BaseController {

    @Autowired
    private EngineRoomDataService engineRoomDataService;
    @Autowired
    private CruiseStationService cruiseStationService;


    /**
     * 获取油舱液位数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @PreAuthorize("@ss.hasPermi('data:engineRoom:list')")
    @RequestMapping("/tankLevel/{startData}/{endDate}/{shipId}/{currentPage}/{pageSize}/{sort}")
    public TableDataInfo selectTankLevelList(@PathVariable Long startData, @PathVariable Long endDate,
                                             @PathVariable Long shipId, @PathVariable int currentPage,
                                             @PathVariable int pageSize, @PathVariable String sort) {
        return engineRoomDataService.queryTankLevelPage(startData, endDate, shipId, currentPage, pageSize, sort);
    }

    /**
     * 导出油舱液位数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/tankLevel/{startData}/{endDate}/{shipId}")
    public AjaxResult exportTankLevel(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) {
        List<TankLevelVo> list = engineRoomDataService.selectTankLevelList(startData, endDate, shipId);
        logger.info("油舱液位数据--333---，{}", list.size());

        ExcelUtil<TankLevelVo> util = new ExcelUtil<TankLevelVo>(TankLevelVo.class);
        return util.exportExcel(list, "油舱液位数据");
    }


    /**
     * 获取主推侧推数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @PreAuthorize("@ss.hasPermi('data:engineRoom:list')")
    @RequestMapping("/mainSideThrust/{startData}/{endDate}/{shipId}/{currentPage}/{pageSize}/{sort}")
    public TableDataInfo selectMainSideThrustList(@PathVariable Long startData, @PathVariable Long endDate,
                                                  @PathVariable Long shipId, @PathVariable int currentPage,
                                                  @PathVariable int pageSize, @PathVariable String sort) {
        return engineRoomDataService.queryMainSideThrustPage(startData, endDate, shipId, currentPage, pageSize, sort);
    }

    /**
     * 导出主推侧推数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/mainSideThrust/{startData}/{endDate}/{shipId}")
    public AjaxResult exportMainSideThrust(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) {
        List<MainSideThrustVo> list = engineRoomDataService.selectMainSideThrustList(startData, endDate, shipId);
        ExcelUtil<MainSideThrustVo> util = new ExcelUtil<MainSideThrustVo>(MainSideThrustVo.class);
        return util.exportExcel(list, "主推侧推数据");
    }


    /**
     * 获取站位距离数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @PreAuthorize("@ss.hasPermi('data:engineRoom:list')")
    @RequestMapping("/stationDistance/{startData}/{endDate}/{shipId}/{currentPage}/{pageSize}/{sort}")
    public TableDataInfo selectStationDistanceList(@PathVariable Long startData,
                                                   @PathVariable Long endDate,
                                                   @PathVariable Long shipId,
                                                   @PathVariable int currentPage,
                                                   @PathVariable int pageSize,
                                                   @PathVariable String sort) {
        return engineRoomDataService.queryStationDistancePage(startData, endDate, shipId, currentPage, pageSize, sort);
    }

    /**
     * 导出站位距离数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/stationDistance/{startData}/{endDate}/{shipId}/{cruiseId}")
    public AjaxResult exportStationDistance(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId, @PathVariable Long cruiseId) {
        List<StationDistanceVo> stationDistanceVoList = engineRoomDataService.selectStationDistanceList(startData, endDate, shipId);

        List<List<String>> allRows = Lists.newArrayList();

        // head
        List<String> headRow = Lists.newArrayList();
        List<String> stationCodeList = Lists.newArrayList();
        headRow.add("时间");
        headRow.add("纬度");
        headRow.add("经度");
        headRow.add("船速");
        headRow.add("主推4马达转速");
        headRow.add("主推5马达转速");
        CruiseStationEntity cruiseStation = new CruiseStationEntity();
        cruiseStation.setCruiseId(cruiseId);
        List<CruiseStationEntity> stationList = cruiseStationService.selectNewCruiseStationList(cruiseStation);
        for (CruiseStationEntity stationEntity : stationList) {
            headRow.add(stationEntity.getStationCode());
            stationCodeList.add(stationEntity.getStationCode());
        }
        allRows.add(headRow);

        // data
        List<String> dataRow;
        for (StationDistanceVo distanceVo : stationDistanceVoList) {
            dataRow = Lists.newArrayList();
            dataRow.add(distanceVo.getBjTime());
            dataRow.add(distanceVo.getLatitude());
            dataRow.add(distanceVo.getLongitude());
            dataRow.add(distanceVo.getGroundRate());
            dataRow.add(distanceVo.getMt4MtrSpeed());
            dataRow.add(distanceVo.getMt5MtrSpeed());

            if (StringUtils.isBlank(distanceVo.getDistanceMap())) {
                continue;
            }
            Map<String, String> map = JSON.parseObject(distanceVo.getDistanceMap(), Map.class);
            for (String code : stationCodeList) {
                dataRow.add(StringUtils.isBlank(map.get(code)) ? "" : map.get(code));
            }
            allRows.add(dataRow);
        }

        ExcelUtil<StationDistanceVo> util = new ExcelUtil<StationDistanceVo>(StationDistanceVo.class);
        return util.exportExcel4List(allRows, "站位距离数据");
    }

    /**
     * 获取机舱数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @PreAuthorize("@ss.hasPermi('data:engineRoom:list')")
    @RequestMapping("/{engineRoomName}/{startData}/{endDate}/{shipId}/{currentPage}/{pageSize}/{sort}")
    public TableDataInfo selectBallastWaterList(@PathVariable String engineRoomName, @PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId,
                                                @PathVariable int currentPage, @PathVariable int pageSize, @PathVariable String sort) throws Exception {
        return engineRoomDataService.queryEngineRoomDataPage(engineRoomName, startData, endDate, shipId, currentPage, pageSize, sort);
    }

    /**
     * 导出压载水数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/ballastWater/{startData}/{endDate}/{shipId}")
    public AjaxResult exportBallastWater(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectBallastWaterList(startData, endDate, shipId);
        ExcelUtil<BallastWaterVo> util = new ExcelUtil<BallastWaterVo>(BallastWaterVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "压载水数据");
    }

    /**
     * 导出锅炉给水数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/boilerFeedWater/{startData}/{endDate}/{shipId}")
    public AjaxResult exportBoilerFeedWater(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectBoilerFeedWaterList(startData, endDate, shipId);
        ExcelUtil<BoilerFeedWaterVo> util = new ExcelUtil<BoilerFeedWaterVo>(BoilerFeedWaterVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "锅炉给水数据");
    }

    /**
     * 导出压缩空气数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/compressedAir/{startData}/{endDate}/{shipId}")
    public AjaxResult exportCompressedAir(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectCompressedAirVoList(startData, endDate, shipId);
        ExcelUtil<CompressedAirVo> util = new ExcelUtil<CompressedAirVo>(CompressedAirVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "压缩空气数据");
    }

    /**
     * 导出应急发电机数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/emergencyGenerator/{startData}/{endDate}/{shipId}")
    public AjaxResult exportEmergencyGenerator(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectEmergencyGeneratorList(startData, endDate, shipId);
        ExcelUtil<EmergencyGeneratorVo> util = new ExcelUtil<EmergencyGeneratorVo>(EmergencyGeneratorVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "应急发电机数据");
    }

    /**
     * 导出造水机数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/fwGenerator/{startData}/{endDate}/{shipId}")
    public AjaxResult exportFwGenerator(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectFwGeneratorVoList(startData, endDate, shipId);
        ExcelUtil<FwGeneratorVo> util = new ExcelUtil<FwGeneratorVo>(FwGeneratorVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "造水机数据");
    }

    /**
     * 导出燃油系统数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/fuelOil/{startData}/{endDate}/{shipId}")
    public AjaxResult exportFuelOil(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectFuelOilVoList(startData, endDate, shipId);
        ExcelUtil<FuelOilVo> util = new ExcelUtil<FuelOilVo>(FuelOilVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "燃油系统数据");
    }

    /**
     * 导出侧推系统数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/tunnelThruster/{startData}/{endDate}/{shipId}")
    public AjaxResult exportTunnelThruster(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectTunnelThrusterVoList(startData, endDate, shipId);
        ExcelUtil<TunnelThrusterVo> util = new ExcelUtil<TunnelThrusterVo>(TunnelThrusterVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "侧推系统数据");
    }

    /**
     * 导出海水冷却系统数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/swCooling/{startData}/{endDate}/{shipId}")
    public AjaxResult exportSwCooling(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectSwCoolingVoList(startData, endDate, shipId);
        ExcelUtil<SwCoolingVo> util = new ExcelUtil<SwCoolingVo>(SwCoolingVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "海水冷却系统数据");
    }

    /**
     * 导出电力管理数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/powerManage/{startData}/{endDate}/{shipId}")
    public AjaxResult exportPowerManage(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectPowerManageVoList(startData, endDate, shipId);
        ExcelUtil<PowerManageVo> util = new ExcelUtil<PowerManageVo>(PowerManageVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "电力管理系统数据");
    }

    /**
     * 导出主推轴承数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/propulsion/{startData}/{endDate}/{shipId}")
    public AjaxResult exportPropulsion(@PathVariable Long startData, @PathVariable Long endDate, @PathVariable Long shipId) throws Exception {
        List list = engineRoomDataService.selectPropulsionVoList(startData, endDate, shipId);
        ExcelUtil<PropulsionVo> util = new ExcelUtil<PropulsionVo>(PropulsionVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "主推轴承数据");
    }

    /**
     * 导出发电机数据
     *
     * @param startData
     * @param endDate
     * @return
     */
    @GetMapping("/export/dieselGenerator/{startData}/{endDate}/{shipId}/{tableName}")
    public AjaxResult exportDieselGenerator(@PathVariable Long startData, @PathVariable Long endDate,
                                            @PathVariable Long shipId, @PathVariable String tableName) throws Exception {
        List list = engineRoomDataService.selectDieselGeneratorVoList(startData, endDate, shipId, tableName);
        ExcelUtil<DieselGeneratorVo> util = new ExcelUtil<DieselGeneratorVo>(DieselGeneratorVo.class);
        return util.exportExcel(engineRoomDataService.convert(list), "发电机" + tableName.substring(tableName.length() - 1) + "数据");
    }


}
