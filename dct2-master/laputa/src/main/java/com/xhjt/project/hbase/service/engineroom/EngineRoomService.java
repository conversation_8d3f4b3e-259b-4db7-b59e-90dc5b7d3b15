package com.xhjt.project.hbase.service.engineroom;

import com.xhjt.common.utils.ListUtil;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.engineroom.*;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.*;
import com.xhjt.dctcore.commoncore.enums.EngineroomEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.engineroom.domain.EngineRoomConfigEntity;
import com.xhjt.project.engineroom.service.EngineRoomConfigService;
import com.xhjt.project.netty.service.StoreService;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 机舱数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class EngineRoomService {
    private Logger logger = LoggerFactory.getLogger(EngineRoomService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    @Autowired
    private EngineRoomConfigService engineRoomConfigService;

    @Autowired
    private BallastWaterService ballastWaterService;
    @Autowired
    private BoilerFeedWaterService boilerFeedWaterService;
    @Autowired
    private CompressedAirService compressedAirService;
    @Autowired
    private DieselGeneratorService dieselGeneratorService;
    @Autowired
    private EmergencyGeneratorService emergencyGeneratorService;
    @Autowired
    private FwGeneratorService fwGeneratorService;
    @Autowired
    private MainThruster4Service mainThruster4Service;
    @Autowired
    private TankLevelService tankLevelService;
    @Autowired
    private MainSideThrustService mainSideThrustService;
    @Autowired
    private StationDistanceService stationDistanceService;
    @Autowired
    private FuelOilService fuelOilService;
    @Autowired
    private SwCoolingService swCoolingService;
    @Autowired
    private PowerManageService powerManageService;
    @Autowired
    private PropulsionService propulsionService;
    @Autowired
    private TunnelThrusterService thrusterService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 保存机舱所有数据
     *
     * @param dataListStr 机舱数据 (code,status,value|code..|...)
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void save(String sn, String dataListStr, Long timeStamp) throws Exception {

        ValueOperations<String, Long> opsForValue = redisTemplate.opsForValue();

        List<EngineroomData> dataList = Lists.newArrayList();
        int totalSize = convertData(sn, dataListStr, dataList);

        List<EngineroomData> allList = mergeList(dataList, sn, timeStamp);

        if (allList.size() != totalSize) {
            return;
        }

        //保存数据长度

        StringBuilder sb = new StringBuilder();
        for (EngineroomData engineroomData : allList) {
            sb.append(engineroomData.mergeSendStr()).append("|");
        }

        // 保存原始数据
        EngineroomDataVo engineroomDataVo = new EngineroomDataVo();
        engineroomDataVo.setId(hBaseDaoUtil.getRowKey(timeStamp));
        engineroomDataVo.setInfo(sb.toString());
        opsForValue.set(RedisParameter.ENGINE_ROOM_DATE, timeStamp);
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "engineRoom"), engineroomDataVo);

        // 保存
        save2HbaseAndRedis(sn, allList, timeStamp);
    }


    /**
     * 转换字符串
     *
     * @param dataListStr
     * @return
     */
    private int convertData(String sn, String dataListStr, List<EngineroomData> dataList) {
        int totalSize = 0;

        if (StringUtils.isBlank(dataListStr) || !dataListStr.contains("|")) {
            return totalSize;
        }

        Map<Integer, EngineRoomConfigEntity> map = engineRoomConfigService.getConfigMap(sn, 1);

        String[] dataListArr = dataListStr.split("\\|");

        EngineroomData engineroomData;
        for (String dataStr : dataListArr) {
            if (dataStr.contains("@&")) {
                totalSize = Integer.valueOf(dataStr.replace("@&", ""));
            }
            if (StringUtils.isBlank(dataStr) || !dataStr.contains(",")) {
                continue;
            }
            String[] dataArr = dataStr.split(",");
            if (dataArr.length != 3) {
                continue;
            }
            if (StringUtils.isBlank(dataArr[0])) {
                continue;
            }

            EngineRoomConfigEntity configEntity = map.get(Integer.valueOf(dataArr[0]));
            if (configEntity == null) {
                continue;
            }

            engineroomData = new EngineroomData();
            engineroomData.setNumber(configEntity.getCodeStr());
            engineroomData.setName(configEntity.getName());
            engineroomData.setSymbol(configEntity.getSymbol());
            engineroomData.setStatus(dataArr[1]);
            engineroomData.setValue(dataArr[2]);

            dataList.add(engineroomData);
        }
        return totalSize;
    }

    /**
     * 合并redis中数据和接收到的数据
     *
     * @param newList
     * @return
     * @throws Exception
     */
    private List<EngineroomData> mergeList(List<EngineroomData> newList, String sn, Long timeStamp) throws Exception {
        ValueOperations<String, List<EngineroomData>> opsForValue = redisTemplate.opsForValue();
        List<EngineroomData> allList = opsForValue.get(RedisParameter.ENGINE_ROOM_DATA + sn + "-" + timeStamp);

        if (allList == null) {
            allList = Lists.newArrayList();
        }
        if (newList.size() == 0) {
            return allList;
        }
        List<String> numberList = ListUtil.fetchFieldValueList(allList, "number");
        for (EngineroomData engineroomData : newList) {
            if (numberList.contains(engineroomData.getNumber())) {
                continue;
            }
            allList.add(engineroomData);
        }

        opsForValue.set(RedisParameter.ENGINE_ROOM_DATA + sn + "-" + timeStamp, allList, 1 * 24, TimeUnit.HOURS);

        return allList;
    }


    /**
     * 保存到hbase中
     *
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void save2HbaseAndRedis(String sn, List<EngineroomData> list, Long timeStamp) {

        Map<String, EngineroomData> map = new HashMap<String, EngineroomData>();
        for (EngineroomData engineroomData : list) {
            map.put(engineroomData.getNumber(), engineroomData);
        }

        BallastWater ballastWater = new BallastWater(map);
        ballastWater.setTimeStamp(timeStamp);
        BallastWaterVo ballastWaterVo = ballastWaterService.save(sn, ballastWater, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.BallastWater.getValue().toString(), timeStamp, ballastWaterVo);

        BoilerFeedWater boilerFeedWater = new BoilerFeedWater(map);
        boilerFeedWater.setTimeStamp(timeStamp);
        BoilerFeedWaterVo boilerFeedWaterVo = boilerFeedWaterService.save(sn, boilerFeedWater, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.BoilerFeedWater.getValue().toString(), timeStamp, boilerFeedWaterVo);

        CompressedAir compressedAir = new CompressedAir(map);
        compressedAir.setTimeStamp(timeStamp);
        CompressedAirVo compressedAirVo = compressedAirService.save(sn, compressedAir, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.CompressedAir.getValue().toString(), timeStamp, compressedAirVo);

        EmergencyGenerator emergencyGenerator = new EmergencyGenerator(map);
        emergencyGenerator.setTimeStamp(timeStamp);
        EmergencyGeneratorVo emergencyGeneratorVo = emergencyGeneratorService.save(sn, emergencyGenerator, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.EmergencyGenerator.getValue().toString(), timeStamp, emergencyGeneratorVo);

        FwGenerator fwGenerator = new FwGenerator(map);
        fwGenerator.setTimeStamp(timeStamp);
        FwGeneratorVo fwGeneratorVo = fwGeneratorService.save(sn, fwGenerator, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.FwGenerator.getValue().toString(), timeStamp, fwGeneratorVo);

        MainThruster4 mainThruster4 = new MainThruster4(map);
        mainThruster4.setTimeStamp(timeStamp);
        MainThruster4Vo mainThruster4Vo = mainThruster4Service.save(sn, mainThruster4, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.MainThruster4.getValue().toString(), timeStamp, mainThruster4Vo);

        DieselGenerator dieselGenerator1 = new DieselGenerator("01", map);
        dieselGenerator1.setTimeStamp(timeStamp);
        DieselGeneratorVo dieselGenerator1Vo = dieselGeneratorService.save(sn, dieselGenerator1, timeStamp, "diesel_generator1");
        storeService.save2Redis(sn, EngineroomEnum.DieselGenerator1.getValue().toString(), timeStamp, dieselGenerator1Vo);

        DieselGenerator dieselGenerator2 = new DieselGenerator("02", map);
        dieselGenerator2.setTimeStamp(timeStamp);
        DieselGeneratorVo dieselGenerator2Vo = dieselGeneratorService.save(sn, dieselGenerator2, timeStamp, "diesel_generator2");
        storeService.save2Redis(sn, EngineroomEnum.DieselGenerator2.getValue().toString(), timeStamp, dieselGenerator2Vo);

        DieselGenerator dieselGenerator3 = new DieselGenerator("03", map);
        dieselGenerator3.setTimeStamp(timeStamp);
        DieselGeneratorVo dieselGenerator3Vo = dieselGeneratorService.save(sn, dieselGenerator3, timeStamp, "diesel_generator3");
        storeService.save2Redis(sn, EngineroomEnum.DieselGenerator3.getValue().toString(), timeStamp, dieselGenerator3Vo);

        //燃油系统数据
        FuelOilVo fuelOilVo = new FuelOilVo(map);
        fuelOilService.save(sn, fuelOilVo, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.FuelOil.getValue().toString(), timeStamp, fuelOilVo);

        //海水冷却系统数据
        SwCoolingVo swCoolingVo = new SwCoolingVo(map);
        swCoolingService.save(sn, swCoolingVo, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.SwCooling.getValue().toString(), timeStamp, swCoolingVo);

        //电力管理系统数据
        PowerManageVo powerManageVo = new PowerManageVo(map);
        powerManageService.save(sn, powerManageVo, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.PowerManage.getValue().toString(), timeStamp, powerManageVo);

        //侧推
        TunnelThrusterVo tunnelThrusterVo = new TunnelThrusterVo(map);
        thrusterService.save(sn, tunnelThrusterVo, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.TunnelThruster.getValue().toString(), timeStamp, tunnelThrusterVo);

        //主推2
        PropulsionVo propulsionVo = new PropulsionVo(map);
        propulsionService.save(sn, propulsionVo, timeStamp);
        storeService.save2Redis(sn, EngineroomEnum.Propulsion.getValue().toString(), timeStamp, propulsionVo);


        // 油舱液位数据
        TankLevelVo tankLevelVo = new TankLevelVo(map);
        tankLevelService.save(sn, tankLevelVo, timeStamp);

        // 主推侧推数据
        MainSideThrustVo mainSideThrustVo = new MainSideThrustVo(map);
        mainSideThrustService.save(sn, mainSideThrustVo, timeStamp);

        // 站位距离数据
        stationDistanceService.save(sn, map, timeStamp);
    }

}
