package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.MainThruster4;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.MainThruster4Vo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * MainThruster4 操作类
 */
@Service
@Transactional(readOnly = true)
public class MainThruster4Service {

    private Logger logger = LoggerFactory.getLogger(MainThruster4Service.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 保存mainThruster4
     *
     * @param mainThruster4
     */
    @Transactional(rollbackFor = Exception.class)
    public MainThruster4Vo save(String sn, MainThruster4 mainThruster4, Long timeStamp) {

        MainThruster4Vo mainThruster4Vo = new MainThruster4Vo();

        mainThruster4Vo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        mainThruster4Vo.setSpeed(mainThruster4.getSpeed());
        mainThruster4Vo.setTorque(mainThruster4.getTorque());
        mainThruster4Vo.setPower(mainThruster4.getPower());
        mainThruster4Vo.setVoltage(mainThruster4.getVoltage());
        mainThruster4Vo.setCurrent(mainThruster4.getCurrent());
        mainThruster4Vo.setVfdIgbtTemp(mainThruster4.getVfdIgbtTemp());
        mainThruster4Vo.setwTempU(mainThruster4.getwTempU());
        mainThruster4Vo.setwTempV(mainThruster4.getwTempV());
        mainThruster4Vo.setwTempW(mainThruster4.getwTempW());
        mainThruster4Vo.setVfdwPress(mainThruster4.getVfdwPress());
        mainThruster4Vo.setVfdwTemp(mainThruster4.getVfdwTemp());

        logger.info("mainThruster4开始保存到hbase，---{}", JSONObject.toJSONString(mainThruster4Vo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "main_thruster4"), mainThruster4Vo);

        return mainThruster4Vo;
    }
}
