package com.xhjt.project.view.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.view.domain.ViewTemplateEntity;
import com.xhjt.project.view.service.ViewTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模版信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/view/template")
public class ViewTemplateController extends BaseController {

    @Autowired
    private ViewTemplateService viewTemplateService;

    /**
     * 获取模版信息列表
     */
    @PreAuthorize("@ss.hasPermi('view:template:list')")
    @GetMapping("/page")
    public TableDataInfo page(ViewTemplateEntity template) {
        startPage();
        List<ViewTemplateEntity> list = viewTemplateService.selectTemplateList(template);
        return getDataTable(list);
    }

    /**
     * 获取模版信息列表
     */
    @PreAuthorize("@ss.hasPermi('view:template:list')")
    @GetMapping("/list")
    public AjaxResult list(ViewTemplateEntity template) {
        List<ViewTemplateEntity> list = viewTemplateService.selectTemplateList(template);
        return AjaxResult.success(list);
    }

    @Log(title = "模版管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('view:template:export')")
    @GetMapping("/export")
    public AjaxResult export(ViewTemplateEntity template) {
        List<ViewTemplateEntity> list = viewTemplateService.selectTemplateList(template);
        ExcelUtil<ViewTemplateEntity> util = new ExcelUtil<ViewTemplateEntity>(ViewTemplateEntity.class);
        return util.exportExcel(list, "模版数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('view:template:query')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable Long templateId) {
        return AjaxResult.success(viewTemplateService.selectTemplateById(templateId));
    }

    /**
     * 新增模版信息
     */
    @PreAuthorize("@ss.hasPermi('view:template:add')")
    @Log(title = "模版新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ViewTemplateEntity template) {
        template.setCreateBy(SecurityUtils.getUsername());
        return toAjax(viewTemplateService.addTemplate(template));
    }

    /**
     * 修改模版信息
     */
    @PreAuthorize("@ss.hasPermi('view:template:edit')")
    @Log(title = "模版修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ViewTemplateEntity template) {
        template.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(viewTemplateService.updateTemplate(template));
    }

    /**
     * 删除模版信息
     */
    @PreAuthorize("@ss.hasPermi('view:template:remove')")
    @Log(title = "模版删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds) {
        return toAjax(viewTemplateService.deleteTemplateByIds(templateIds));
    }

    /**
     * 启用
     */
    @PreAuthorize("@ss.hasPermi('view:template:edit')")
    @PostMapping(value = "/enable/{id}")
    public AjaxResult enable(@PathVariable Long id) {
        return AjaxResult.success(viewTemplateService.enable(id));
    }

    /**
     * 禁用
     */
    @PreAuthorize("@ss.hasPermi('view:template:edit')")
    @PostMapping(value = "/disable/{id}")
    public AjaxResult disable(@PathVariable Long id) {
        return AjaxResult.success(viewTemplateService.disable(id));
    }
}
