package com.xhjt.project.view.service.impl;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.view.domain.ModuleConfigEntity;
import com.xhjt.project.view.mapper.ModuleConfigMapper;
import com.xhjt.project.view.service.ModuleConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模块配置管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ModuleConfigServiceImpl implements ModuleConfigService {
    @Autowired
    private ModuleConfigMapper moduleConfigMapper;

    /**
     * 查询模块配置信息
     *
     * @param moduleConfigId 模块配置ID
     * @return 模块配置信息
     */
    @Override
    public ModuleConfigEntity selectModuleConfigById(Long moduleConfigId) {
        ModuleConfigEntity moduleConfig = new ModuleConfigEntity();
        moduleConfig.setId(moduleConfigId);
        return moduleConfigMapper.selectModuleConfig(moduleConfig);
    }

    /**
     * 查询模块配置列表
     *
     * @param moduleConfig 模块配置信息
     * @return 模块配置集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<ModuleConfigEntity> selectModuleConfigList(ModuleConfigEntity moduleConfig) {
        return moduleConfigMapper.selectModuleConfigList(moduleConfig);
    }

    /**
     * 新增模块配置
     *
     * @param moduleConfig 模块配置信息
     * @return 结果
     */
    @Override
    public int addModuleConfig(ModuleConfigEntity moduleConfig) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
            moduleConfig.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        return moduleConfigMapper.addModuleConfig(moduleConfig);
    }

    /**
     * 修改模块配置
     *
     * @param moduleConfig 模块配置信息
     * @return 结果
     */
    @Override
    public int updateModuleConfig(ModuleConfigEntity moduleConfig) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
            moduleConfig.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        return moduleConfigMapper.updateModuleConfig(moduleConfig);
    }

    /**
     * 删除模块配置信息
     *
     * @param moduleConfigId 参数ID
     * @return 结果
     */
    @Override
    public int deleteModuleConfigById(Long moduleConfigId) {
        return moduleConfigMapper.deleteModuleConfigById(moduleConfigId);
    }

    /**
     * 批量删除参数信息
     *
     * @param moduleConfigIds 需要删除的参数ID
     * @return 结果
     */
    @Override
    public int deleteModuleConfigByIds(Long[] moduleConfigIds) {
        return moduleConfigMapper.deleteModuleConfigByIds(moduleConfigIds);
    }

}
