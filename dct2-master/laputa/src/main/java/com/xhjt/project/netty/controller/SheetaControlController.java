package com.xhjt.project.netty.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.MainSideThrustVo;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.monitor.domain.Ctll;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 传输属性 信息操作处理
 *  2022-4-27
 * <AUTHOR>
@CrossOrigin
@RestController
@RequestMapping("/sheetacontrol/sync")
public class SheetaControlController extends BaseController {

    @Autowired
    private SnapshotChannelService snapshotChannelService;

    /**
     * 控制船端机器重启
     */
    @GetMapping("/controller/{sn}")
    public AjaxResult controller(@PathVariable("sn") String sn) throws SchedulerException {
        Ctll ctll = new Ctll();
        ctll.setName("reboot");
        ctll.setCode("boot");
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(ctll), 1, "controller");
        try {
            snapshotChannelService.sync2ShipController(syncEntity,ctll.getCode(),sn);
            return AjaxResult.success("操作成功，请等待。。。",ctll);
        } catch (Exception e) {
            logger.info("---info异常---", e);
        }
        return AjaxResult.error("操作失败");

    }

    /**
     * 修改船端redis参数值
     */
    @GetMapping("/controller/{sn}/{name}/{number}")
    public AjaxResult controller(@PathVariable("sn") String sn,@PathVariable("name") String name,@PathVariable("number") Integer number) throws SchedulerException {
        Ctll ctll = new Ctll();
        ctll.setName(name);
        ctll.setCode(number+"");
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(ctll), 1, "updateRedis");
        try {
            snapshotChannelService.sync2ShipController(syncEntity,"uprs",sn);
            return AjaxResult.success("操作成功，请等待。。。",ctll);
        } catch (Exception e) {
            logger.info("---info异常---", e);
        }
        return AjaxResult.error("操作失败");

    }

    /**
     * 控制船端机器开启实时数据传输
     */
    @GetMapping("/macs/{sn}/{mac}")
    public AjaxResult macs(@PathVariable("sn") String sn,@PathVariable("mac") String mac) throws SchedulerException {
        Ctll ctll = new Ctll();
        ctll.setName(mac);
        ctll.setCode("macs");
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(ctll), 1, "macsUpdate");
        try {
            snapshotChannelService.sync2ShipController(syncEntity,ctll.getCode(),sn);
            return AjaxResult.success("操作成功，请等待。。。",ctll);
        } catch (Exception e) {
            logger.info("---info异常---", e);
        }
        return AjaxResult.error("操作失败");

    }
}
