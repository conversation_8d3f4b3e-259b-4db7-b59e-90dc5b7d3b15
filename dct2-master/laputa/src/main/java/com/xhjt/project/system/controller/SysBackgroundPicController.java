package com.xhjt.project.system.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.file.FileUploadUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.config.ProjectConfig;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.system.domain.SysBackgroundPic;
import com.xhjt.project.system.service.IBackgroundPicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @program: laputa
 * @description: 图片处理
 * @author: Mrs.<PERSON>
 * @create: 2021-12-17 16:18
 **/
@RestController
@RequestMapping("/system/backgroundpic")
public class SysBackgroundPicController extends BaseController {

    @Autowired
    private IBackgroundPicService backgroundPicService;

    /**
     * 获取图片管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:backgroundpic:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysBackgroundPic backgroundpic) {
        startPage();
        //backgroundpic.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<SysBackgroundPic> list = backgroundPicService.selectBackPicList(backgroundpic);
        return getDataTable(list);
    }

    /**
     * 根据图片管理编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:backgroundpic:query')")
    @GetMapping(value = "/{backgroundpicId}")
    public AjaxResult getInfo(@PathVariable Long backgroundpicId) {
        return AjaxResult.success(backgroundPicService.selectById(backgroundpicId));
    }

    /**
     * 新增图片管理
     */
    @PreAuthorize("@ss.hasPermi('system:backgroundpic:add')")
    @Log(title = "图片管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysBackgroundPic backgroundpic) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        backgroundpic.setDeptId(deptId);
        return toAjax(backgroundPicService.insertBackgroundPic(backgroundpic));
    }

    /**
     * 修改图片管理
     */
    @PreAuthorize("@ss.hasPermi('system:backgroundpic:edit')")
    @Log(title = "图片管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysBackgroundPic backgroundpic) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        backgroundpic.setDeptId(deptId);
        return toAjax(backgroundPicService.updateBackgroudPic(backgroundpic));
    }

    /**
     * 删除图片管理
     */
    @PreAuthorize("@ss.hasPermi('system:backgroundpic:remove')")
    @Log(title = "图片管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{picIds}")
    public AjaxResult remove(@PathVariable Long[] picIds) {
        return toAjax(backgroundPicService.deteleBackgroundPicIds(picIds));
    }
    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:backgroundpic:edit')")
    @Log(title = "图片管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeBgStatus")
    public AjaxResult changeBgStatus(@RequestBody SysBackgroundPic backgroundpic) {
        return toAjax(backgroundPicService.updateBackgroudPic(backgroundpic));
    }

    /**
     * 图片上传
     * @param file
     * @return
     * @throws Exception
     */

    @PreAuthorize("@ss.hasPermi('system:backgroundpic:edit')")
    @Log(title = "图片上传", businessType = BusinessType.IMPORT)
    @PostMapping("/uploadPic")
    public AjaxResult uploadPic(@RequestParam("file") MultipartFile file) throws Exception {
        String result = FileUploadUtils.uploadPic(ProjectConfig.getProfile()+"/sysBackgroundPic",file);

        if (StringUtils.isBlank(result)) {
            return AjaxResult.error("上传失败");
        }
        SysBackgroundPic sysBackgroundPic = new SysBackgroundPic();
        String directory = ProjectConfig.getProfile() + result.substring(0, result.lastIndexOf("/") + 1);
        String fileName = result.substring(result.lastIndexOf("/") + 1);
        sysBackgroundPic.setName(file.getOriginalFilename());
        sysBackgroundPic.setFileName(fileName);
        sysBackgroundPic.setDirectory(directory);
        return AjaxResult.success("上传成功", sysBackgroundPic);
    }

}
