package com.xhjt.project.monitor.mapper;

import com.xhjt.project.monitor.domain.ReceiveLog;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.Map;

/**
 * 接收数据记录 数据层
 *
 * <AUTHOR>
 */
public interface ReceiveLogMapper {
    /**
     * 新增
     */
    public void insert(ReceiveLog receiveLog);

    /**
     * 查询集合
     */
    public List<ReceiveLog> selectList(ReceiveLog receiveLog);

    public List<ReceiveLog> selectReceiveLogList();

    /**
     * 批量删除
     */
    public int deleteByIds(Long[] ids);

    /**
     * 查询详细
     */
    public ReceiveLog selectById(Long id);

    /**
     *  根据查询集合
     */
    public List<ReceiveLog> selectListByRecordTime(Long startTime,Long endTime,String sn);

    /**
     * 查询秒级数据
     */
    public List<Map<Long,Integer>>selectListForMinutes( String sn);
}
