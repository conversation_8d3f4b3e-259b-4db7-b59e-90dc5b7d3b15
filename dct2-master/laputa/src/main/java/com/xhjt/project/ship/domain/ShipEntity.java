package com.xhjt.project.ship.domain;

import com.xhjt.dctcore.commoncore.annotation.Excel;
import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 船只信息
 *
 * <AUTHOR>
 */
public class ShipEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long shipId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * sn
     */
    @Excel(name = "sn")
    private String sn;

    /**
     * 船名
     */
    @Excel(name = "船名")
    private String name;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private int status;

    /**
     * 公司ID
     */
    private Long comId;

    /**
     * 添加模块JSON
     */
    private String moduleJson;

    /**
     * MMSI
     */
    @Excel(name = "MMSI")
    private String mmsi;

    /**
     * 呼号
     */
    @Excel(name = "呼号")
    private String callSign;

    /**
     * IMO
     */
    @Excel(name = "IMO")
    private String imo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 搜索值
     */
    private String label;

    public Long getShipId() {
        return shipId;
    }

    public void setShipId(Long shipId) {
        this.shipId = shipId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Long getComId() {
        return comId;
    }

    public void setComId(Long comId) {
        this.comId = comId;
    }

    public String getModuleJson() {
        return moduleJson;
    }

    public void setModuleJson(String moduleJson) {
        this.moduleJson = moduleJson;
    }

    public String getMmsi() {
        return mmsi;
    }

    public void setMmsi(String mmsi) {
        this.mmsi = mmsi;
    }

    public String getCallSign() {
        return callSign;
    }

    public void setCallSign(String callSign) {
        this.callSign = callSign;
    }

    public String getImo() {
        return imo;
    }

    public void setImo(String imo) {
        this.imo = imo;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
