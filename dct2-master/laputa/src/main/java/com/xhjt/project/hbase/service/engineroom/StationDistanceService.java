package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.engineroom.EngineroomData;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.StationDistanceVo;
import com.xhjt.dctcore.commoncore.utils.AnalysisUtils;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.domain.TravelActionNodeEntity;
import com.xhjt.project.ship.service.CruiseService;
import com.xhjt.project.ship.service.CruiseStationService;
import com.xhjt.project.ship.service.TravelActionNodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * 站位距离数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class StationDistanceService {

    private Logger logger = LoggerFactory.getLogger(StationDistanceService.class);

    @Autowired
    private TravelActionNodeService travelActionNodeService;

    /**
     * 地球半径
     */
    private static double EARTH_RADIUS = 6366.70702;
    /**
     * 马达转速判断值
     */
    private static double MTR_SPEED_THRESHOLD = 15.0;
    /**
     * 范围判断值
     */
    private static double RANGE_THRESHOLD = 30.0;

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    //    @Autowired
//    private GpsService gpsService;
    @Autowired
    private CruiseStationService cruiseStationService;
    @Autowired
    private CruiseService cruiseService;

    /**
     * 保存
     *
     * @param map
     * @param timeStamp
     * @param sn
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public StationDistanceVo save(String sn, Map<String, EngineroomData> map, Long timeStamp) {
        StationDistanceVo stationDistanceVo = new StationDistanceVo();

        try {
            String key = hBaseDaoUtil.getRowKey(timeStamp);
            stationDistanceVo.setId(key);
            stationDistanceVo.setMt4MtrSpeed(AnalysisUtils.analysisValue(map.get("05100")));
            stationDistanceVo.setMt5MtrSpeed(AnalysisUtils.analysisValue(map.get("05237")));

            //获取当前时间的GPS信息
//            List<GpsHbaseVo> gpsHbaseVos = gpsService.getGpsInfo(sn, timeStamp);
//            for (GpsHbaseVo gpsHbaseVo : gpsHbaseVos) {
//                boolean condition = StringUtils.isBlank(gpsHbaseVo.getLatitude()) ||
//                        StringUtils.isBlank(gpsHbaseVo.getLongitude()) ||
//                        StringUtils.isBlank(gpsHbaseVo.getGroundRateJ()) ||
//                        "null".equals(gpsHbaseVo.getGroundRateJ());
//                if (!condition) {
//                    stationDistanceVo.setLongitude(gpsHbaseVo.getLongitude());
//                    stationDistanceVo.setLatitude(gpsHbaseVo.getLatitude());
//                    stationDistanceVo.setGroundRate(gpsHbaseVo.getGroundRateJ());
//                    break;
//                }
//            }

            // 计算与站位的距离
            if (!StringUtils.isBlank(stationDistanceVo.getLongitude()) && !StringUtils.isBlank(stationDistanceVo.getLatitude())) {
                String distanceMap = getDistanceMap(stationDistanceVo, sn);
                stationDistanceVo.setDistanceMap(distanceMap);
            }

            logger.info("stationDistanceVo开始保存到hbase，---{}", JSONObject.toJSONString(stationDistanceVo));
            hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "s_station_distance"), stationDistanceVo);
        } catch (Exception e) {
            logger.error("站位距离，保存出错--{}", e);
        }

        // 判断当前节点的动作类型
        judgeTravelAction(stationDistanceVo, hBaseDaoUtil.getTableName(sn, "s_station_distance"), stationDistanceVo.getTime(), sn);

        return stationDistanceVo;
    }


    private String getDistanceMap(StationDistanceVo stationDistanceVo, String sn) {
        List<CruiseEntity> cruiseEntityList = cruiseService.selectNewCruiseBySn(sn);
        if (cruiseEntityList.size() == 0) {
            return "";
        }

        Map<String, String> map = new HashMap<>();

        CruiseStationEntity queryEntity = new CruiseStationEntity();
        queryEntity.setCruiseId(cruiseEntityList.get(0).getCruiseId());
        List<CruiseStationEntity> cruiseStationEntities = cruiseStationService.selectNewCruiseStationList(queryEntity);

        for (CruiseStationEntity entity : cruiseStationEntities) {
            double d = countDistance(Double.valueOf(entity.getLatitude()), Double.valueOf(entity.getLongitude()),
                    Double.valueOf(stationDistanceVo.getLatitude()), Double.valueOf(stationDistanceVo.getLongitude()));
            map.put(entity.getStationCode(), String.valueOf(d));
        }

        return JSONObject.toJSONString(map);
    }

    private double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 计算坐标间的距离，单位为海里
     *
     * @param lat1
     * @param lng1
     * @param lat2
     * @param lng2
     * @return
     */
    public double countDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = s / 1.852;
        s = Math.round(s * 1000d) / 1000d;
        return s;
    }

    /**
     * 判断当前节点的动作类型
     *
     * @param currentStation
     * @param tableName
     * @param timeStamp
     * @param sn
     */
    @Transactional(rollbackFor = Exception.class)
    public void judgeTravelAction(StationDistanceVo currentStation, String tableName, Long timeStamp, String sn) {

        try {
            TravelActionInfo ca = getActionInfo(currentStation);
            logger.info("行程动作节点,-222当前--{}", JSONObject.toJSONString(ca));

            // 获取历史站位动态信息
            StationDistanceVo hisStation = new StationDistanceVo();
            hBaseDaoUtil.getLatestRow(hisStation, tableName, timeStamp - 100);
            TravelActionInfo ha = getActionInfo(hisStation);
            logger.info("行程动作节点,-333历史--{}", JSONObject.toJSONString(ha));
            if (ca == null || ha == null) {
                return;
            }

            int actionType = 0;
            // 行驶
            if (!ha.stopped && !ca.stopped) {
                if (!ha.inRange && ca.inRange) {
                    // 进范围(首次)
                    actionType = 1;
                }
                if (ha.inRange && !ca.inRange) {
                    // 出范围(首次)
                    actionType = 4;
                }
            }
            // 停船
            if (!ha.stopped && ca.stopped) {
                if (!ca.inRange) {
                    // 意外停船
                    actionType = 5;
                }
                if (ca.inRange) {
                    // 到站停船或复位
                    actionType = 2;
                }
            }
            // 启动
            if (ha.stopped && !ca.stopped) {
                if (!ha.inRange) {
                    // 意外停船启动
                    actionType = 6;
                }
                if (ha.inRange && !ca.inRange) {
                    // 离站出范围
                    actionType = 4;
                }
                if (ha.inRange && ca.inRange) {
                    // 站内启动
                    actionType = 3;
                }
            }
            if (actionType == 0) {
                return;
            }

            TravelActionNodeEntity travelActionNodeEntity = new TravelActionNodeEntity();
            travelActionNodeEntity.setSn(sn);
            CruiseEntity cruiseEntity = cruiseService.queryCurrentCruiseBySn(sn);
            travelActionNodeEntity.setCruiseId(cruiseEntity.getCruiseId());
            travelActionNodeEntity.setTime(timeStamp);
            travelActionNodeEntity.setLongitude(currentStation.getLongitude());
            travelActionNodeEntity.setLatitude(currentStation.getLatitude());
            if (StringUtils.isNotBlank(currentStation.getGroundRate()) && !"null".equals(currentStation.getGroundRate())) {
                travelActionNodeEntity.setGroundRate(Double.valueOf(currentStation.getGroundRate()));
            }
            travelActionNodeEntity.setMt4MtrSpeed(Double.valueOf(currentStation.getMt4MtrSpeed()));
            travelActionNodeEntity.setMt5MtrSpeed(Double.valueOf(currentStation.getMt5MtrSpeed()));

            travelActionNodeEntity.setRelationStation(ca.getRelationStation());
            travelActionNodeEntity.setRelationStationDistance(ca.getRelationStationDistance());
            travelActionNodeEntity.setActionType(actionType);

            logger.info("行程动作节点,-444--actionType--{}---{}", actionType, JSONObject.toJSONString(travelActionNodeEntity));

            travelActionNodeService.addTravelActionNode(travelActionNodeEntity);
        } catch (Exception e) {
            logger.error("行程动作行为判断出错----{}", e);
        }
    }

    /**
     * 获取行为信息
     *
     * @param stationDistanceVo
     * @return
     */
    private TravelActionInfo getActionInfo(StationDistanceVo stationDistanceVo) {
        if (stationDistanceVo == null) {
            return null;
        }
        boolean stopped = false;
        // 马达转速趋近0。判断为停船
        if (Math.abs(Double.valueOf(stationDistanceVo.getMt4MtrSpeed())) < MTR_SPEED_THRESHOLD &&
                Math.abs(Double.valueOf(stationDistanceVo.getMt5MtrSpeed())) < MTR_SPEED_THRESHOLD &&
                Math.abs(Double.valueOf(stationDistanceVo.getGroundRate())) < 3) {
            stopped = true;
        }

        boolean inRange = false;
        String relationStation = "";
        double distance = 999999;
        Map<String, String> map = JSON.parseObject(stationDistanceVo.getDistanceMap(), Map.class);
        if (map == null) {
            return null;
        }
        for (String key : map.keySet()) {
            if (Double.valueOf(map.get(key)) <= RANGE_THRESHOLD) {
                inRange = true;
            }
            if (Double.valueOf(map.get(key)) < distance) {
                distance = Double.valueOf(map.get(key));
                relationStation = key;
            }
        }

        return new TravelActionInfo(stopped, inRange, relationStation, distance);
    }

    public class TravelActionInfo {
        // 已停船
        boolean stopped;
        // 在范围内
        boolean inRange;
        // 关联站位
        String relationStation;
        // 关联站位距离
        Double relationStationDistance;

        public TravelActionInfo() {
        }

        public TravelActionInfo(boolean stopped, boolean inRange, String relationStation, Double relationStationDistance) {
            this.stopped = stopped;
            this.inRange = inRange;
            this.relationStation = relationStation;
            this.relationStationDistance = relationStationDistance;
        }

        public boolean isStopped() {
            return stopped;
        }

        public void setStopped(boolean stopped) {
            this.stopped = stopped;
        }

        public boolean isInRange() {
            return inRange;
        }

        public void setInRange(boolean inRange) {
            this.inRange = inRange;
        }

        public String getRelationStation() {
            return relationStation;
        }

        public void setRelationStation(String relationStation) {
            this.relationStation = relationStation;
        }

        public Double getRelationStationDistance() {
            return relationStationDistance;
        }

        public void setRelationStationDistance(Double relationStationDistance) {
            this.relationStationDistance = relationStationDistance;
        }
    }
}
