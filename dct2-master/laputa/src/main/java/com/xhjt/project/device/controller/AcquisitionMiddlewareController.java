package com.xhjt.project.device.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.device.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 采集终端-设备
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/am")
public class AcquisitionMiddlewareController extends BaseController {

    @Autowired
    private AcquisitionMiddlewareService acquisitionMiddlewareService;
    @Autowired
    private DeviceService deviceService;
    /**
     * 获取采集终端信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(AcquisitionMiddleware am) {
        startPage();
        List<AcquisitionMiddleware> list = acquisitionMiddlewareService.selectAcquisitionMiddlewareList(am);
        list.stream().forEach(acquisitionMiddleware -> {
            DeviceEntity device = new DeviceEntity();
            device.setMac(acquisitionMiddleware.getMac());
            device.setConnectType(0);//采集终端
            List<DeviceEntity> deviceEntity = deviceService.selectDeviceList(device);
            if (deviceEntity != null && deviceEntity.size()>0){
                acquisitionMiddleware.setDeviceStatus(1);
            }else {
                acquisitionMiddleware.setDeviceStatus(0);
            }
            //当没有type的时候--方便前端实时预览加的判断
            if (acquisitionMiddleware.getType() == null){
                acquisitionMiddleware.setType(0);
            }
        });
        return getDataTable(list);
    }

}
