package com.xhjt.project.monitor.service.impl;

import com.xhjt.dctcore.commoncore.domain.engineroom.EngineroomData;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.framework.manager.AsyncManager;
import com.xhjt.framework.manager.factory.AsyncFactory;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.monitor.domain.ReceiveLog;
import com.xhjt.project.monitor.mapper.ReceiveLogMapper;
import com.xhjt.project.monitor.service.IReceiveLogService;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class ReceiveLogServiceImpl implements IReceiveLogService {

    private static final Logger logger = LoggerFactory.getLogger(ReceiveLogServiceImpl.class);
    @Autowired
    private ReceiveLogMapper receiveLogMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public synchronized void record(TransferPackage transferPackage) {
        try {
            int msgLength = transferPackage.getMessage().length() + 21;
            if (transferPackage.getPackageType() == 1) {
                msgLength += 8;
            }

            ValueOperations<String, Integer> opsForValue = redisTemplate.opsForValue();
            long nt = System.currentTimeMillis();
            long second = nt/1000;
            // 累加同一秒的总长度
            String key = RedisParameter.RECEIVE_TOTAL_LENGTH_S + transferPackage.getSn() + "-" + second;
            int lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
            opsForValue.set(key, lenTemp + msgLength, 1, TimeUnit.HOURS);
            // 累加同一秒的补数据的长度
            if (transferPackage.getIsRepair() == 1) {
                key = RedisParameter.RECEIVE_REPAIR_LENGTH_S + transferPackage.getSn() + "-" + second;
                lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
                opsForValue.set(key, lenTemp + msgLength, 1, TimeUnit.HOURS);
            }
            // 累加同一秒的图片数据的长度
            if (transferPackage.getPackageType() == 1) {
                key = RedisParameter.RECEIVE_PIC_LENGTH_S + transferPackage.getSn() + "-" + second;
                lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
                opsForValue.set(key, lenTemp + msgLength, 1, TimeUnit.HOURS);
            }
            // 累加同一秒的条数
            key = RedisParameter.RECEIVE_LINES_S + transferPackage.getSn() + "-" + second;
            lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
            opsForValue.set(key, lenTemp + 1, 1, TimeUnit.HOURS);


            // 累加同一分钟的总长度
            long minute = nt / 1000 / 60;
            key = RedisParameter.RECEIVE_TOTAL_LENGTH_M + transferPackage.getSn() + "-" + minute;
            lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
            opsForValue.set(key, lenTemp + msgLength, 1, TimeUnit.HOURS);
            // 累加同一分钟的补数据的长度
            if (transferPackage.getIsRepair() == 1) {
                key = RedisParameter.RECEIVE_REPAIR_LENGTH_M + transferPackage.getSn() + "-" + minute;
                lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
                opsForValue.set(key, lenTemp + msgLength, 1, TimeUnit.HOURS);
            }
            // 累加同一分钟的图片数据的长度
            if (transferPackage.getPackageType() == 1) {
                key = RedisParameter.RECEIVE_PIC_LENGTH_M + transferPackage.getSn() + "-" + minute;
                lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
                opsForValue.set(key, lenTemp + msgLength, 1, TimeUnit.HOURS);
            }
            // 累加同一分钟的条数
            key = RedisParameter.RECEIVE_LINES_M + transferPackage.getSn() + "-" + minute;
            lenTemp = opsForValue.get(key) == null ? 0 : opsForValue.get(key);
            opsForValue.set(key, lenTemp + 1, 1, TimeUnit.HOURS);

            // 入库
            key = RedisParameter.RECEIVE_DATA_LENGTH_SAVE + transferPackage.getSn() + "-" + (minute - 1);
            if (opsForValue.get(key) == null) {
                // 已经入库
                opsForValue.set(key, 1, 1, TimeUnit.HOURS);

                ReceiveLog receiveLog = new ReceiveLog();
                receiveLog.setSn(transferPackage.getSn());
                receiveLog.setRecordTime((minute - 1) * 60 * 1000);

                key = RedisParameter.RECEIVE_TOTAL_LENGTH_M + transferPackage.getSn() + "-" + (minute - 1);
                receiveLog.setTotalLength(opsForValue.get(key));

                key = RedisParameter.RECEIVE_LINES_M + transferPackage.getSn() + "-" + (minute - 1);
                receiveLog.setTotalLines(opsForValue.get(key));

                key = RedisParameter.RECEIVE_REPAIR_LENGTH_M + transferPackage.getSn() + "-" + (minute - 1);
                receiveLog.setRepairLength(opsForValue.get(key));

                key = RedisParameter.RECEIVE_PIC_LENGTH_M + transferPackage.getSn() + "-" + (minute - 1);
                receiveLog.setPicLength(opsForValue.get(key));
                // 保存数据库
                AsyncManager.me().execute(AsyncFactory.recordReceive(receiveLog));
            }
        } catch (Exception e) {
            logger.error("数据接收长度记录出错，--{}", e);
        }
    }

    /**
     * 新增
     */
    @Override
    public void insert(ReceiveLog receiveLog) {

        receiveLogMapper.insert(receiveLog);
    }

    /**
     * 查询集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<ReceiveLog> selectList(ReceiveLog receiveLog) {
        return receiveLogMapper.selectList(receiveLog);
    }

    @Override
    public List<ReceiveLog> selectReceiveLogList() {
        return receiveLogMapper.selectReceiveLogList();
    }

    /**
     * 批量删除
     *
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids) {
        return receiveLogMapper.deleteByIds(ids);
    }

    /**
     * 查询详细
     *
     * @return 操作日志对象
     */
    @Override
    public ReceiveLog selectById(Long id) {
        return receiveLogMapper.selectById(id);
    }

    @Override
    public List<ReceiveLog> selectListByRecordTime(Long startTime,Long endTime,String sn) {
        return receiveLogMapper.selectListByRecordTime(startTime,endTime,sn);
    }

    /**
     * 查询秒级数据
     * @param sn
     * @return
     */
    @Override
    public List<Map<Long,Integer>> selectListForMinutes(String sn) {

        ValueOperations<String,Integer> operations = redisTemplate.opsForValue();
        List<Map<Long,Integer>> list = new ArrayList<Map<Long,Integer>>();

        String[] prefixs = {RedisParameter.RECEIVE_TOTAL_LENGTH_S,
                RedisParameter.RECEIVE_LINES_S,
                RedisParameter.RECEIVE_REPAIR_LENGTH_S,RedisParameter.RECEIVE_PIC_LENGTH_S};

        Long second = System.currentTimeMillis()/1000;
       for(String prefix:prefixs){
           Map<Long,Integer> map = new HashMap<>();
           for(int i=3480;i>=0;i--){
               String key = prefix+sn+"-"+(second-i);
               map.put(second-i,operations.get(key));
           }
           list.add(map);
       }
        return list;
    }

    /**
     * 监控机舱数据
     */
    @Override
    public List<Map<Long, Integer>> queryEngineRoomForMinutes() {
        List list = Lists.newArrayList();
        Map<Long, Integer> map = new TreeMap();
        ValueOperations<String, List<EngineroomData>> opsForValue = redisTemplate.opsForValue();
        String prefix = RedisParameter.ENGINE_ROOM_DATA + "aa0001-";
        Long second = System.currentTimeMillis()/1000*1000-System.currentTimeMillis()/1000%60*1000;
        for(int i = 1440;i>=0;i--){
            String key = prefix+(second-i*60*1000);
            if(opsForValue.get(key)!=null){
                map.put(second-i*60*1000,opsForValue.get(key).size());
            }else{
                map.put(second-i*60*1000,0);
            }
        }
        list.add(map);
        return list;
    }
}
