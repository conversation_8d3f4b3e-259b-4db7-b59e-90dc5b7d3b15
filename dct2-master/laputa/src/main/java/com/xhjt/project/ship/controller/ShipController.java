package com.xhjt.project.ship.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xhjt.common.constant.HttpStatus;
import com.xhjt.common.constants.RedisKeyConstants;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.JsonUtil;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.ServletUtils;
import com.xhjt.common.utils.ip.IpUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.utils.EncrypDESUtils;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.config.ProjectConfig;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.CruiseService;
import com.xhjt.project.ship.service.CruiseStationService;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.domain.SysModule;
import com.xhjt.project.system.service.ISysDeptService;
import com.xhjt.project.system.service.ISysModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船只信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ship")
public class ShipController extends BaseController {

    @Autowired
    private ShipService shipService;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    private ISysModuleService moduleService;

    @Resource
    private ValueOperations<String, String> valueOperations;

    @Autowired
    private ISysOperLogService iSysOperLogService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private CruiseService cruiseService;

    @Autowired
    private CruiseStationService cruiseStationService;

    /**
     * 获取船只信息动态列
     */
    @GetMapping("/getColumn")
    public AjaxResult getColumn() {
        List<HashMap<String, Object>> res = new ArrayList<>();
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(0);
        sysModule.setTableShow(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);

        sysModule.setComIds(comIds);
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        for (SysModule o:moduleList) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("id",o.getId().toString());
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("characterType",o.getCharacterType()+"");
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }

    /**
     * 获取船只信息列表
     */
    @PreAuthorize("@ss.hasPermi('ship:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipEntity ship) {
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        SysModule sysModule1 = new SysModule();
        sysModule1.setType(0);
        sysModule1.setTableShow(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule1.setComIds(comIds);

        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule1);
        List<String> properties = new ArrayList<>();//属性，用于判断
        for (SysModule sysModule:moduleList) {
            properties.add(sysModule.getProperty());
        }
        ship.setDeptId(deptId);
        startPage();
        List<ShipEntity> list = shipService.selectNewShipList(ship);//船只表信息
        List<HashMap> resultList = new ArrayList<>();//最终返回
        for (ShipEntity s:list) {
            HashMap<String,Object> map= new HashMap<>();
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(properties.contains(entry.getKey())){//如果属于动态列中
                        map.put("id",s.getShipId());
                        Object value = entry.getValue();
                        SysModule sysModule = new SysModule();
                        sysModule.setProperty(entry.getKey());
                        sysModule.setType(0);
                        SysModule selectModule = moduleService.selectModuleListByProperty(sysModule);
                        if (!entry.getValue().equals("") && entry.getValue() != null){
                            if(selectModule.getCharacterType() == 1){// 1.时间
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                value = sdf.format(new Date(Long.parseLong(value+"")));
                            } else if(selectModule.getCharacterType() == 3) {// 3.日期
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                value = sdf.format(new Date(Long.parseLong(value+"")));
                            }
                        }
                        map.put(entry.getKey(),value);//存入map
                        map.put(entry.getKey(),entry.getValue());//存入map
                    }
                }
                resultList.add(map);
            }
        }
        //分页
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(resultList);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 获取全部船只信息列表
     */
    @PreAuthorize("@ss.hasPermi('ship:info:list')")
    @GetMapping("/allList")
    public AjaxResult allList() {
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        ShipEntity ship = new ShipEntity();
        ship.setDeptId(sysDept.getDeptId());
        List<ShipEntity> list = shipService.selectAllNewShip(ship);//船只表信息
        return AjaxResult.success(list);
    }

    @Log(title = "船只管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('ship:info:export')")
    @GetMapping("/export")
    public AjaxResult export(ShipEntity ship) {
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        SysModule sysModule1 = new SysModule();
        sysModule1.setType(0);
        sysModule1.setTableShow(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule1.setComIds(comIds);

        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule1);
        List<String> properties = new ArrayList<>();//属性，用于判断
        for (SysModule sysModule:moduleList) {
            properties.add(sysModule.getProperty());
        }
        ship.setDeptId(deptId);

        List<ShipEntity> list = shipService.selectNewShipList(ship);//船只表信息
        startPage();
        List<HashMap> resultList = new ArrayList<>();//最终返回
        for (ShipEntity s:list) {
            HashMap<String,Object> map= new HashMap<>();
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(properties.contains(entry.getKey())){//如果属于动态列中
                        map.put("id",s.getShipId());
                        map.put(entry.getKey(),entry.getValue());//存入map
                    }
                }
                resultList.add(map);
            }
        }
        List<ShipEntity> resStation = new ArrayList<>();
        for(HashMap hashMap : resultList){
            ShipEntity shipEntity = JSON.parseObject(JSON.toJSONString(hashMap), ShipEntity.class);
            resStation.add(shipEntity);
        }

        ExcelUtil<ShipEntity> util = new ExcelUtil<ShipEntity>(ShipEntity.class);
        return util.exportExcel(resStation, "船只数据");
    }

    /**
     * 获取新增表单
     */
    @GetMapping(value = "/addForm")
    public AjaxResult getAddForm(){
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        SysModule sysModule = new SysModule();
        sysModule.setType(0);
        sysModule.setFormShow(1);
        sysModule.setIsRequired(0);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o: list) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("isRequired",o.getIsRequired());
            map.put("value",null);
            map.put("characterType",o.getCharacterType());
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }

    /**
     * 获取新增表单-必填
     */
    @GetMapping(value = "/addForm2")
    public AjaxResult getAddForm2(){
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(0);
        sysModule.setFormShow(1);
        sysModule.setIsRequired(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByComIds(sysModule);

        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o: list) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("isRequired",o.getIsRequired());
            map.put("value",null);
            map.put("characterType",o.getCharacterType());
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }
    /**
     * 新增船只信息
     */
    @PreAuthorize("@ss.hasPermi('ship:info:add')")
    @Log(title = "船只新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody JSONObject shipEntity) {

        int count = shipService.addShip(shipEntity);
        if(count == -1){
            AjaxResult.error("船名已存在");
        }

        //存入redis
        String sql = "SELECT sn FROM ship WHERE status = 1";
        List<String> snList = jdbcTemplate.queryForList(sql, String.class);
        if (snList.size() == 0) {
            snList.add("TEMP");
        }
        logger.info("更新SN集合成功----{}", JsonUtil.obj2String(snList));
        valueOperations.set(RedisKeyConstants.ALL_ENABLE_SHIP_SN, JsonUtil.obj2String(snList));
        return AjaxResult.success();
    }




    /**
     * 修改-获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:info:query')")
    @GetMapping(value = "/NoRequired/{shipId}")
    public AjaxResult getInfo(@PathVariable Long shipId) {
        ShipEntity ship = shipService.selectNewShipById(shipId);
        JSONObject jsonObject = JSONObject.parseObject(ship.getModuleJson());//将船只表的moduleJson String类型转为json
        SysModule sysModule = new SysModule();
        sysModule.setType(0);
        sysModule.setIsRequired(0);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        sysModule.setComId(sysDept.getDeptId());
        Map map1 = moduleService.getModuleShowByParam(sysModule);
        if(map1 == null){
            sysModule.setComId(sysDept.getParentId());
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        if(map1 == null){
            sysModule.setComId(Long.parseLong(100+""));
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        String property = map1.get("property")+"";
        String[] propertyArr = property.split(",");
        for(int i = 0;i < propertyArr.length;i++){
            if(!jsonObject.containsKey(propertyArr[i])){
                jsonObject.put(propertyArr[i],null);
            }
        }
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String,Object>> list = new ArrayList<>();

        for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
            for(SysModule module: moduleList){
                if(entry.getKey().equals(module.getProperty()) && module.getIsRequired() == 0 && module.getTableShow() == 1 && module.getFormShow() == 1){
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("property",entry.getKey());
                    map.put("value",entry.getValue());
                    map.put("label",module.getLabel());
                    map.put("isRequired",module.getIsRequired());
                    map.put("characterType",module.getCharacterType());
                    map.put("sortNum",module.getSortNum());
                    list.add(map);
                }
            }
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 修改-获取详细信息-必填
     */
    @PreAuthorize("@ss.hasPermi('ship:info:query')")
    @GetMapping(value = "/Required/{shipId}")
    public AjaxResult getInfo2(@PathVariable Long shipId) {
        ShipEntity ship = shipService.selectNewShipById(shipId);
        JSONObject jsonObject = JSONObject.parseObject(ship.getModuleJson());//将船只表的moduleJson String类型转为json
        SysModule sysModule = new SysModule();
        sysModule.setType(0);
        sysModule.setIsRequired(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        sysModule.setComId(sysDept.getDeptId());
        Map map1 = moduleService.getModuleShowByParam(sysModule);
        if(map1 == null){
            sysModule.setComId(sysDept.getParentId());
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        if(map1 == null){
            sysModule.setComId(Long.parseLong(100+""));
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        String property = map1.get("property")+"";
        String[] propertyArr = property.split(",");
        for(int i = 0;i < propertyArr.length;i++){
            if(!jsonObject.containsKey(propertyArr[i])){
                jsonObject.put(propertyArr[i],null);
            }
        }
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String,Object>> list = new ArrayList<>();

        for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
            for(SysModule module: moduleList){
                if(entry.getKey().equals(module.getProperty()) && module.getIsRequired() == 1 && module.getTableShow() == 1 && module.getFormShow() == 1){
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("property",entry.getKey());
                    map.put("value",entry.getValue());
                    map.put("label",module.getLabel());
                    map.put("isRequired",module.getIsRequired());
                    map.put("characterType",module.getCharacterType());
                    map.put("sortNum",module.getSortNum());
                    list.add(map);
                }
            }
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(list);
    }


    /**
     * 修改船只信息
     *         1、获取数据库信息 ->selectShip 如果id不对，显示数据不存在
     *         2、获取表单信息 ->ship  通过name查询 船名是否已存在
     *         3、两者对比moduleJson -> 不同的修改
     *         4、打印日志
     */
    @PreAuthorize("@ss.hasPermi('ship:info:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody JSONObject shipEntity) {
        //获取数据库信息 ->selectShip 如果id不对，显示数据不存在
        ShipEntity selectShip = shipService.selectNewShipById(Long.parseLong(shipEntity.get("id").toString())) ;
        if (selectShip == null) {
            throw new CustomException("数据不存在");
        }

        //互获取表单信息 ->ship  通过name查询 船名是否已存在
        ShipEntity ship = new ShipEntity();
        List<HashMap> list = (List) shipEntity.get("editNoRequiredForm");
        List<HashMap> list2 = (List) shipEntity.get("editRequiredForm");
        for(int i = 0;i < list2.size();i++){
            list.add(list2.get(i));
        }
        List<HashMap> res = new ArrayList<>();
        for(int i = 0;i < list.size();i++){
            HashMap<String,Object> map = new HashMap();
            if(list.get(i).get("value")!=null && !list.get(i).get("value").equals("null") && !list.get(i).get("value").equals("")){
                map.put("property",list.get(i).get("property")+"");
                map.put("value",list.get(i).get("value"));
                res.add(map);
            }
            if(list.get(i).get("property").equals("name")){
                ship.setName(list.get(i).get("value")+"");
            }
        }

        Map map = res.stream().collect(Collectors.toMap(p->p.get("property"), p->p.get("value")));//moduleJSon(Map)
        String updateBy = SecurityUtils.getUsername();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());

        map.put("deptId",SecurityUtils.getLoginUser().getUser().getDeptId());
        map.put("status",1);
        map.put("updateBy",updateBy);
        map.put("updateTime",new Date());
        map.put("comId",sysDept.getParentId());
        String s = JSONObject.toJSONString(map);

        ship.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        ship.setStatus(1);
        ship.setModuleJson(s);
        ship.setShipId(Long.parseLong(shipEntity.get("id")+""));
        ship.setSn(selectShip.getSn());

        List<ShipEntity> list3 = shipService.selectShipByName(ship);
        if (list3.size() > 0 ) {
            if(!map.get("name").equals(selectShip.getName())){
                return AjaxResult.error("船名已存在");
            }
        }

        //两者对比moduleJson -> form如果没有select的（比如cereateBy、cereateTime）
        String formModuleJson = ship.getModuleJson();
        JSONObject formJsonObject = JSONObject.parseObject(formModuleJson);
        String selectModuleJson = selectShip.getModuleJson();
        JSONObject selectJsonObject = JSONObject.parseObject(selectModuleJson);
        for (Map.Entry<String, Object> selectEntry: selectJsonObject.entrySet()) {
            if(!formJsonObject.containsKey(selectEntry.getKey()) && selectEntry.getKey()!=null){
                formJsonObject.put(selectEntry.getKey(),selectEntry.getValue());
            }
        }

        ship.setModuleJson(formJsonObject+"");
        int row = shipService.updateNewShip(ship);

        //打印日志
        String log = "";
        for (Map.Entry<String, Object> entry: formJsonObject.entrySet()) {
            for (Map.Entry<String, Object> entry2: selectJsonObject.entrySet()) {
                if(entry.getKey().equals(entry2.getKey()) && !entry.getValue().equals(entry2.getValue()) && !entry.getKey().equals("updateTime")){
                    log = log + "{" + entry.getKey() + "的[" + entry2.getValue() + "]改为[" + entry.getValue() +" ]}";
                }
            }
        }
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setTitle("船只修改");
        sysOperLog.setMethod("修改");
        sysOperLog.setBusinessType(2);
        sysOperLog.setRequestMethod("PUT");
        sysOperLog.setOperatorType(1);
        sysOperLog.setOperUrl("/ship");
        sysOperLog.setOperName(SecurityUtils.getUsername());
        sysOperLog.setOperLocation("内网IP");
        sysOperLog.setOperParam(log);
        sysOperLog.setJsonResult("{\"msg\":\"操作成功\",\"code\":200}");
        sysOperLog.setStatus(0);
        sysOperLog.setOperTime(new Date());
        sysOperLog.setOperIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        iSysOperLogService.insertOperlog(sysOperLog);

        return toAjax(row);
    }

    /**
     * 删除船只信息
     */
    @PreAuthorize("@ss.hasPermi('ship:info:remove')")
    @DeleteMapping("/{shipIds}")
    public AjaxResult remove(@PathVariable Long[] shipIds) {
        int row = 0;
        String log = "";
        for (Long shipId : shipIds) {
            log = log + "[" + shipService.selectNewShipById(shipId).getName() + "]";
        }
        //打印日志
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setTitle("船只删除");
        sysOperLog.setMethod("删除");
        sysOperLog.setBusinessType(3);
        sysOperLog.setRequestMethod("DELETE");

        sysOperLog.setOperName(SecurityUtils.getUsername());
        sysOperLog.setOperatorType(1);
        sysOperLog.setOperUrl("/ship");
        sysOperLog.setOperLocation("内网IP");
        sysOperLog.setOperParam(log);
        sysOperLog.setJsonResult("{\"msg\":\"操作成功\",\"code\":200}");
        sysOperLog.setStatus(0);
        sysOperLog.setOperTime(new Date());
        sysOperLog.setOperIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        iSysOperLogService.insertOperlog(sysOperLog);
        //同步删除操作
        for (Long shipId : shipIds) {
            ShipEntity selectShip = shipService.selectNewShipById(shipId);
            String sn = selectShip.getSn();
            CruiseEntity cruiseEntity = new CruiseEntity();
            cruiseEntity.setSn(sn);
            cruiseEntity.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
            CruiseStationEntity cruiseStationEntity = new CruiseStationEntity();
            cruiseStationEntity.setSn(sn);
            cruiseStationEntity.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
            List<CruiseEntity> list1 = cruiseService.selectNewCruiseList(cruiseEntity);
            List<CruiseStationEntity> list2 = cruiseStationService.selectNewCruiseStationList(cruiseStationEntity);
            if(list1.size() == 0 && list2.size() == 0){
                row = shipService.deleteNewShipById(shipId);
            } else if(list1.size() != 0){
                return AjaxResult.error("请先删除相关航次信息");
            }else{
                return AjaxResult.error("请先删除相关站位信息");
            }
        }
        return toAjax(row);
    }

    /**
     * 启用
     */
    @PreAuthorize("@ss.hasPermi('ship:info:edit')")
    @PostMapping(value = "/enable/{id}")
    public AjaxResult enable(@PathVariable Long id) {
        return AjaxResult.success(shipService.enable(id));
    }

    /**
     * 禁用
     */
    @PreAuthorize("@ss.hasPermi('ship:info:edit')")
    @PostMapping(value = "/disable/{id}")
    public AjaxResult disable(@PathVariable Long id) {
        return AjaxResult.success(shipService.disable(id));
    }

    /**
     * 生成sn配置文件
     */
    @PreAuthorize("@ss.hasPermi('ship:info:edit')")
    @PostMapping(value = "/snGrant/{id}")
    public AjaxResult snGrant(@PathVariable Long id) {
        //TODO:待验证
        ShipEntity ship = shipService.selectNewShipById(id);
        String sourceStr = "sn=" + ship.getSn();
        String secretStr = EncrypDESUtils.strEncryption(sourceStr);
        String dirPath = ProjectConfig.getProfile();
        File dir = new File(dirPath);
        String filePath = dirPath + "/sn" + System.currentTimeMillis() + ".txt";
        File file = new File(filePath);
        try {
            if (!dir.exists()) {
                dir.mkdirs();
            }
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fileWriter = new FileWriter(file, true);
            BufferedWriter bufferedWriter=new BufferedWriter(fileWriter);
            bufferedWriter.write(secretStr);
            bufferedWriter.close();
            fileWriter.close();
        } catch (Exception e) {
            logger.error("写入文件失败");
        }
//        filePath = filePath.replace(ProjectConfig.getProfile(), "/prod-api/profile");

        return AjaxResult.success(file.getName());
    }

    public String getComIds(Long comId, String comIds){
        while(comId != 0){
            SysDept sysDept1 = deptService.selectDeptById(comId);
            comId = sysDept1.getParentId();
            comIds = comIds + "," + comId.toString();
        }
        return comIds;
    }

}
