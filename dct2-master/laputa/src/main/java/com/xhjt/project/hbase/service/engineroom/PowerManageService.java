package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.PowerManageVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * 电力管理系统数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class PowerManageService {
    private Logger logger = LoggerFactory.getLogger(PowerManageService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * @param powerManageVo
     */
    @Transactional(rollbackFor = Exception.class)
    public PowerManageVo save(String sn, PowerManageVo powerManageVo, Long timeStamp) {
        powerManageVo.setId(hBaseDaoUtil.getRowKey(timeStamp));
        logger.info("powerManageVo开始保存到hbase，---{}", JSONObject.toJSONString(powerManageVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "power_manage"), powerManageVo);
        return powerManageVo;
    }
}
