package com.xhjt.project.snapshot.controller;


import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.snapshot.domain.SnapshotRealEntity;
import com.xhjt.project.snapshot.domain.SnapshotVideoEntity;
import com.xhjt.project.snapshot.service.SnapshotLogService;
import com.xhjt.project.snapshot.service.SnapshotVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 视频回放
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/video")
public class SnapshotVideoController extends BaseController {

    @Autowired
    private SnapshotLogService snapshotLogService;

    @Autowired
    private SnapshotVideoService snapshotVideoService;

    @PreAuthorize("@ss.hasPermi('snapshot:video:list')")
    @GetMapping("/list")
    public AjaxResult list(SnapshotVideoEntity snapshotVideoEntity) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        snapshotVideoEntity.setDeptId(deptId);
        List<SnapshotLogEntity> list = snapshotLogService.selectSnapshotVideo(snapshotVideoEntity);
        return AjaxResult.success(list);
    }

    @PreAuthorize("@ss.hasPermi('snapshot:video:add')")
    @PutMapping
    public AjaxResult add(SnapshotVideoEntity snapshotVideoEntity) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        snapshotVideoEntity.setDeptId(deptId);
        SnapshotVideoEntity snapshotVideoEntity1 = snapshotVideoService.selectSnapshotVideo(snapshotVideoEntity);
        int row = 0;
        if(snapshotVideoEntity1 != null){
            row =  snapshotVideoService.updateSnapshotVideo(snapshotVideoEntity);
        }else {
            row = snapshotVideoService.insertSnapshotVideo(snapshotVideoEntity);
        }
        return toAjax(row);
    }
}
