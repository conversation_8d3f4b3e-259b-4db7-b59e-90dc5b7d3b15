package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.AcquisitionMiddleware;

import java.util.List;

/**
 * 采集中间件数据层
 *
 * <AUTHOR>
 */
public interface AcquisitionMiddlewareMapper {
    /**
     * 查询采集中间件
     *
     * @param acquisitionMiddleware 采集中间件
     * @return 设备信息
     */
    public AcquisitionMiddleware selectAcquisitionMiddleware(AcquisitionMiddleware acquisitionMiddleware);

    /**
     * 查询设备列表
     *
     * @param acquisitionMiddleware 采集中间件
     * @return 设备集合
     */
    public List<AcquisitionMiddleware> selectAcquisitionMiddlewareList(AcquisitionMiddleware acquisitionMiddleware);

    /**
     * 新增
     *
     * @param acquisitionMiddleware 采集中间件
     * @return 结果
     */
    public int addAcquisitionMiddleware(AcquisitionMiddleware acquisitionMiddleware);

    /**
     * 修改
     *
     * @param acquisitionMiddleware 采集中间件
     * @return 结果
     */
    public int updateAcquisitionMiddleware(AcquisitionMiddleware acquisitionMiddleware);

    /**
     * 连接全部失效
     * @return
     */
    public int connectStatusInvalid();
    /**
     * 删除
     *
     * @param id 参数ID
     * @return 结果
     */
    public int deleteAcquisitionMiddlewareById(Long id);

    /**
     * 删除
     *
     * @param mac 参数mac
     * @return 结果
     */
    public int deleteAcquisitionMiddlewareByMac(String mac);

    /**
     * 删除
     *
     * @param acquisitionMiddleware
     * @return 结果
     */
    public int deleteByIpAndPort(AcquisitionMiddleware acquisitionMiddleware);

    /**
     * 删除
     *
     * @return 结果
     */
    public int delete4Invalid();

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    public int deleteAcquisitionMiddlewareByIds(Long[] ids);
}
