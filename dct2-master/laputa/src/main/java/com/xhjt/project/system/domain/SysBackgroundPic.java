package com.xhjt.project.system.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;


/**
 * @program: laputa
 * @description: 背景图片实体类
 * @author: Mrs<PERSON>
 * @create: 2021-12-17 15:35
 **/

public class SysBackgroundPic extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer type;

    private String name;


    private Integer bgStatus;

    private Long sortNum;

    private String fileName;

    private String directory;

    private Long deptId;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }


    public Integer getBgStatus() {
        return bgStatus;
    }

    public void setBgStatus(Integer bgStatus) {
        this.bgStatus = bgStatus;
    }

    public Long getSortNum() {
        return sortNum;
    }

    public void setSortNum(Long sortNum) {
        this.sortNum = sortNum;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory == null ? null : directory.trim();
    }

        @Override
        public String toString() {
            return "SysBackgroundPic{" +
                    "id=" + id +
                    ", type=" + type +
                    ", name='" + name + '\'' +
                    ", bgStatus=" + bgStatus +
                    ", sortNum=" + sortNum +
                    ", fileName='" + fileName + '\'' +
                    ", directory='" + directory + '\'' +
                    '}';

    }
}
