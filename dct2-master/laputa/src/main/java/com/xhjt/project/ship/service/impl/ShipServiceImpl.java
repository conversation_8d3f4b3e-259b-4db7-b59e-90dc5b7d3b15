package com.xhjt.project.ship.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.JsonUtil;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.dctcore.commoncore.utils.RandomUtil;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.mapper.ShipMapper;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.service.ISysDeptService;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船只管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ShipServiceImpl implements ShipService {
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private ShipMapper shipMapper;
    @Autowired
    private ISysDeptService deptService;

    @Resource
    private ValueOperations<String, String> valueOperations;


    @Override
    public List<ShipEntity> selectShipByFactor(ShipEntity ship) {

        return shipMapper.selectNewShipByFactor(ship);
    }

    @Override
    public ShipEntity selectNewShipById(Long shipId) {
        ShipEntity ship = new ShipEntity();
        ship.setShipId(shipId);
        return shipMapper.selectNewShip(ship);
    }

    /**
     * 查询船只信息
     */
    @Override
    public ShipEntity selectShipBySn(String sn) {
        ShipEntity ship = new ShipEntity();
        ship.setSn(sn);
        return shipMapper.selectNewShip(ship);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<ShipEntity> selectNewShipList(ShipEntity ship) {
        return shipMapper.selectNewShipList(ship);
    }

    @Override
    public List<ShipEntity> selectAllNewShip(ShipEntity ship) {
        return shipMapper.selectAllNewShip(ship);
    }

    /**
     * 新增船只
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addShip(JSONObject shipEntity) {
        ShipEntity ship = new ShipEntity();
        List<HashMap> list = (List) shipEntity.get("editNoRequiredForm");
        List<HashMap> list2 = (List) shipEntity.get("editRequiredForm");
        for(int i = 0;i < list2.size();i++){
            list.add(list2.get(i));
        }

        List<HashMap> res = new ArrayList<>();
        for(int i = 0;i < list.size();i++){
            HashMap<String,Object> map = new HashMap();
            if(list.get(i).get("value")!=null && !list.get(i).get("value").equals("null")&& !list.get(i).get("value").equals("")){
                map.put("property",list.get(i).get("property")+"");
                map.put("value",list.get(i).get("value"));
                res.add(map); //moduleJSon(List<Map>)
            }
            if(list.get(i).get("property").equals("name")){
                ship.setName(list.get(i).get("value")+"");
            }
        }

        String sn  = getNewSn();  // 生成sn号
        String createBy = SecurityUtils.getUsername();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Map map = res.stream().collect(Collectors.toMap(p->p.get("property"), p->p.get("value")));//moduleJSon(Map)
        map.put("comId",sysDept.getParentId());
        map.put("deptId",SecurityUtils.getLoginUser().getUser().getDeptId());
        map.put("status",1);
        map.put("createBy",createBy);
        map.put("createTime",new Date());
        map.put("sn",sn);
        ship.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        ship.setStatus(1);
        ship.setCreateBy(createBy);
        ship.setSn(sn);
        String s = JSONObject.toJSONString(map);
        ship.setModuleJson(s);
        ship.setComId(sysDept.getParentId());

        List<ShipEntity> selectList = selectShipByName(ship);
        if (selectList.size() > 0) {
            return -1;
        }

        // 利用sn 创建hbase命名空间
        hBaseDaoUtil.createNameSpace(ship.getSn());
        // 更新redis中的 SN
        renewShip4Redis();

        return shipMapper.addNewShip(ship);
    }

    /**
     * 生成新的SN
     *
     * @return
     */
    private String getNewSn() {
        String sn = RandomUtil.randomStr(6);

        ShipEntity ship = selectShipBySn(sn);
        if (ship != null) {
            return getNewSn();
        }
        return sn;
    }


    @Override
    public int updateNewShip(ShipEntity ship) {
        return shipMapper.updateNewShip(ship);
    }

    /**
     * 启用
     *
     * @param id 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ShipEntity enable(Long id) {
        ShipEntity ship = selectNewShipById(id);
        if (ship == null) {
            return null;
        }
        if (ship.getStatus() == 1) {
            return ship;
        }
        ship.setStatus(1);
        ship.setSn(ship.getSn());
        updateNewShip(ship);

//         更新redis中的 SN
        renewShip4Redis();

        return ship;
    }

    /**
     * 禁用
     *
     * @param id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ShipEntity disable(Long id) {
        ShipEntity ship = selectNewShipById(id);
        if (ship == null) {
            return null;
        }
        if (ship.getStatus() == 0) {
            return ship;
        }
        ship.setStatus(0);
        ship.setSn(ship.getSn());
        updateNewShip(ship);

        // 更新redis中的 SN
        renewShip4Redis();

        return ship;
    }


    /**
     * 更新redis中SN
     */
    @Override
    public void renewShip4Redis() {
        List<ShipEntity> list = selectAllNewShip(null);

        List<String> snList = Lists.newArrayList();
        for (ShipEntity ship : list) {
            if (ship.getStatus() == 0) {
                continue;
            }
            snList.add(ship.getSn());
        }

        if (snList.size() == 0) {
            snList.add("TEMP");
        }

        valueOperations.set(RedisParameter.ALL_ENABLE_SHIP_SN, JsonUtil.obj2String(snList));
    }


    @Override
    public List<ShipEntity> selectShipByName(ShipEntity ship1) {
        return shipMapper.selectShipByName(ship1);
    }

    @Override
    public int deleteNewShipById(Long shipId) {
        ShipEntity ship = selectNewShipById(shipId);

        // 先判断是否已经没有设备挂在船舶下
        List<DeviceEntity> deviceEntities = deviceService.queryListBySn(ship.getSn());
        if (deviceEntities.size() > 0) {
            throw new CustomException("船舶下存在设备，请先删除设备!");
        }
        // 更新redis中的 SN
        renewShip4Redis();

        // 删除hbase命名空间
        hBaseDaoUtil.dropNameSpace(ship.getSn());

        return shipMapper.deleteNewShipById(shipId);
    }
}
