package com.xhjt.project.engineroom.mapper;

import com.xhjt.project.engineroom.domain.EngineRoomConfigEntity;

import java.util.List;

/**
 * 机舱数据结构 数据层
 *
 * <AUTHOR>
 */
public interface EngineRoomConfigMapper {
    /**
     * 查询机舱数据结构
     *
     * @return 机舱数据结构
     */
    public EngineRoomConfigEntity selectConfig(EngineRoomConfigEntity engineRoomConfigEntity);

    /**
     * 查询列表
     */
    public List<EngineRoomConfigEntity> selectList(EngineRoomConfigEntity engineRoomConfigEntity);

    /**
     * 新增
     *
     * @return 结果
     */
    public int add(EngineRoomConfigEntity engineRoomConfigEntity);

    /**
     * 修改
     *
     * @return 结果
     */
    public int update(EngineRoomConfigEntity engineRoomConfigEntity);

    /**
     * 删除
     *
     * @return 结果
     */
    public int deleteById(Long id);

    /**
     * 批量删除参数信息
     *
     * @return 结果
     */
    public int deleteByIds(Long[] ids);
}