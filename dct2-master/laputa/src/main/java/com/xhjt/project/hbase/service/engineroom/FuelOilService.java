package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.FuelOilVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * 燃油系统数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class FuelOilService {
    private Logger logger = LoggerFactory.getLogger(FuelOilService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * @param fuelOilVo
     */
    @Transactional(rollbackFor = Exception.class)
    public FuelOilVo save(String sn, FuelOilVo fuelOilVo, Long timeStamp) {
        fuelOilVo.setId(hBaseDaoUtil.getRowKey(timeStamp));
        logger.info("fuelOilVo开始保存到hbase，---{}", JSONObject.toJSONString(fuelOilVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "fuel_oil"), fuelOilVo);
        return fuelOilVo;
    }
}
