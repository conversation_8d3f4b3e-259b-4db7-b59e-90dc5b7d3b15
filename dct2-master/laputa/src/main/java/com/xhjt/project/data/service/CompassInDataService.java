package com.xhjt.project.data.service;

import com.xhjt.common.constant.HttpStatus;
import com.xhjt.dctcore.commoncore.domain.hbase.CompassInHbaseVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.web.page.TableDataInfo;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * compass数据信息 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CompassInDataService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    public List<CompassInHbaseVo> selectCompassInList(Long startTime, Long endTime, String tableName) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new CompassInHbaseVo(), tableName, rowList);
    }

    public TableDataInfo queryCompassInPage(Long startTime, Long endTime, String tableName, int currentPage, int pageSize, String sort) {
        List<CompassInHbaseVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new CompassInHbaseVo(), tableName, list, rowList, currentPage, pageSize, sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }
}
