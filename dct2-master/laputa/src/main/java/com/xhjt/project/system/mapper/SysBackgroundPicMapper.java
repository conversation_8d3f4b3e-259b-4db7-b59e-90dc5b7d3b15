package com.xhjt.project.system.mapper;

import com.xhjt.project.system.domain.SysBackgroundPic;

import java.util.List;

public interface SysBackgroundPicMapper {

    /**
     * 查询图片
     * @param id 图片ID
     * @return  图片信息
     */
    public SysBackgroundPic selectById(Long id);

    /**
     * 查询图片列表
     * @param sysBackgroundPic 图片信息
     * @return 图片集合
     */
    public List<SysBackgroundPic> selectBackPicList(SysBackgroundPic sysBackgroundPic);

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 批量删除图片信息
     * @param picIds
     * @return
     */
    public int deteleBackgroundPicIds(Long[] picIds);

    /**
     * 新增图片
     * @param record
     * @return
     */
    int insert(SysBackgroundPic record);

    /**
     * 插入含有数据的属性，对于为空的属性，不予以处理
     * @param record
     * @return
     */
    int insertSelective(SysBackgroundPic record);

    /**
     * 通过主键查询
     * @param id
     * @return
     */
    SysBackgroundPic selectByPrimaryKey(Long id);


    /**
     * 通过修改ID更新所有设置了值的列
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(SysBackgroundPic record);
    /**
     * 通过主键修改
     * @param record
     * @return
     */
    int updateByPrimaryKey(SysBackgroundPic record);


}
