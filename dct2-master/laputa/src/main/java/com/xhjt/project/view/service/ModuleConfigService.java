package com.xhjt.project.view.service;

import com.xhjt.project.view.domain.ModuleConfigEntity;

import java.util.List;

/**
 * 模块配置管理 服务层
 *
 * <AUTHOR>
 */
public interface ModuleConfigService {
    /**
     * 查询模块配置信息
     *
     * @param moduleConfigId 模块配置ID
     * @return 模块配置信息
     */
    public ModuleConfigEntity selectModuleConfigById(Long moduleConfigId);

    /**
     * 查询模块配置列表
     *
     * @param moduleConfig 模块配置信息
     * @return 模块配置集合
     */
    public List<ModuleConfigEntity> selectModuleConfigList(ModuleConfigEntity moduleConfig);

    /**
     * 新增模块配置
     *
     * @param moduleConfig 模块配置信息
     * @return 结果
     */
    public int addModuleConfig(ModuleConfigEntity moduleConfig);

    /**
     * 修改模块配置
     *
     * @param moduleConfig 模块配置信息
     * @return 结果
     */
    public int updateModuleConfig(ModuleConfigEntity moduleConfig);

    /**
     * 删除模块配置信息
     *
     * @param moduleConfigId 参数ID
     * @return 结果
     */
    public int deleteModuleConfigById(Long moduleConfigId);

    /**
     * 批量删除参数信息
     *
     * @param moduleConfigIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteModuleConfigByIds(Long[] moduleConfigIds);

}
