package com.xhjt.project.hbase.service.engineroom;


import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.PropulsionVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * 主推2系统数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class PropulsionService {

    private Logger logger = LoggerFactory.getLogger(PropulsionService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * @param propulsionVo
     */
    @Transactional(rollbackFor = Exception.class)
    public PropulsionVo save(String sn, PropulsionVo propulsionVo, Long timeStamp) {
        propulsionVo.setId(hBaseDaoUtil.getRowKey(timeStamp));
        logger.info("propulsionVo开始保存到hbase，---{}", JSONObject.toJSONString(propulsionVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "propulsion"), propulsionVo);
        return propulsionVo;
    }
}
