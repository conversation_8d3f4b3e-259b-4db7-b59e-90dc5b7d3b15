package com.xhjt.project.device.service;


import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.ShipTerminalManage;

import java.util.List;

/**
 * 船舶终端管理
 */
public interface ShipTerminalManageService {

    List<ShipTerminalManage> selectShipTerminalManageList(ShipTerminalManage shipTerminalManage);

    void handleBySync(SyncEntity syncEntity, String sn);
}
