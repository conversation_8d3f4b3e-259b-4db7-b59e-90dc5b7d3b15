package com.xhjt.project.device.domain;

/**
 * 设备
 *
 * <AUTHOR>
 */
public class ChannelStatus {

    /**
     * 设备编码
     */
    private String code;

    /**
     * 快照存储状态
     */
    private Integer trStatus;


    /**
     * 快照传输状态
     */
    private Integer transferStatus;

    /**
     * 截屏连接状态 1成功 0失败
     * @return
     */
    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getTrStatus() {
        return trStatus;
    }

    public void setTrStatus(Integer trStatus) {
        this.trStatus = trStatus;
    }

    public Integer getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(Integer transferStatus) {
        this.transferStatus = transferStatus;
    }
}
