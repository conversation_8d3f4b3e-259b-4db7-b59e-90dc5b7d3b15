package com.xhjt.project.snapshot.service;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.snapshot.domain.SnapshotRealEntity;

import java.util.List;

public interface SnapshotRealService {

    List<SnapshotRealEntity> selectSnapshotRealList(SnapshotRealEntity snapshotRealEntity);

    int addSnapshotReal(SnapshotRealEntity snapshotRealEntity);

    int editSnapshotReal(SnapshotRealEntity snapshotRealEntity);

    SnapshotRealEntity selectNewest(SnapshotRealEntity snapshotRealEntity);

    SnapshotRealEntity selectSnapshotReal(SnapshotRealEntity snapshotRealEntity);
}
