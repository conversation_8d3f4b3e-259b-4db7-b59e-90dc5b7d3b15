package com.xhjt.project.device.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.domain.ShipTerminalManage;
import com.xhjt.project.device.service.ShipTerminalManageService;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 船舶终端管理
 */
@RestController
@RequestMapping("/shipTerminalManage")
public class ShipTerminalManageController extends BaseController {

    @Autowired
    private ShipTerminalManageService shipTerminalManageService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ShipService shipService;

    /**
     * 获取船舶终端管理信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:shipTerminalManage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipTerminalManage shipTerminalManage) {
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long deptId = sysDept.getDeptId();
        shipTerminalManage.setDeptId(deptId);

        startPage();
        List<ShipTerminalManage> list = shipTerminalManageService.selectShipTerminalManageList(shipTerminalManage);
        return getDataTable(list);
    }

}
