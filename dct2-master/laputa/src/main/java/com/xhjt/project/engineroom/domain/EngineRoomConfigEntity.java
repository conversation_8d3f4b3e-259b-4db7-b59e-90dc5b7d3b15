package com.xhjt.project.engineroom.domain;


import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 机舱数据结构,入库保存
 *
 * <AUTHOR>
 */
public class EngineRoomConfigEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String sn;

    /**
     * 数字编号，传输时使用
     */
    private Integer code;

    /**
     * 编号
     */
    private String codeStr;

    /**
     * 名称
     */
    private String name;

    /**
     * 符号
     */
    private String symbol;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getCodeStr() {
        return codeStr;
    }

    public void setCodeStr(String codeStr) {
        this.codeStr = codeStr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
}
