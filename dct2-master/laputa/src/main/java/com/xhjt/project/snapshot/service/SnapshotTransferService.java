package com.xhjt.project.snapshot.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.constant.ScheduleConstants;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.monitor.domain.SysJob;
import com.xhjt.project.netty.service.SyncService;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.snapshot.domain.TransferSnapshotVo;
import com.xhjt.project.snapshot.mapper.SnapshotTransferMapper;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 传输管理 实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotTransferService {

    @Autowired
    private SnapshotTransferMapper snapshotTransferMapper;
    @Autowired
    private ShipService shipService;
    @Autowired
    private SyncService syncService;

    @Transactional(rollbackFor = Exception.class)
    @DataScope(deptAlias = "d")
    public List<SnapshotTransferEntity> selectSnapshotTransferList(SnapshotTransferEntity snapshotTransfer) {
        return snapshotTransferMapper.selectSnapshotTransferList(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotTransferEntity> queryList(SnapshotTransferEntity snapshotTransfer) {
        return snapshotTransferMapper.selectSnapshotTransferList(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotTransferEntity selectSnapshotTransferById(Long id) {
        SnapshotTransferEntity snapshotTransfer = new SnapshotTransferEntity();
        snapshotTransfer.setId(id);
        return snapshotTransferMapper.selectSnapshotTransfer(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotTransferEntity selectBySnAndChannelCode(String sn, String channelCode) {
        SnapshotTransferEntity snapshotTransfer = new SnapshotTransferEntity();
        snapshotTransfer.setSn(sn);
        snapshotTransfer.setChannelCode(channelCode);
        return snapshotTransferMapper.selectSnapshotTransfer(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public int addSnapshotTransfer(SnapshotTransferEntity snapshotTransfer) {

        //已存在,不添加
        if (selectBySnAndChannelCode(snapshotTransfer.getSn(), snapshotTransfer.getChannelCode()) != null) {
            return 0;
        }
        ShipEntity shipEntity = shipService.selectShipBySn(snapshotTransfer.getSn());
        snapshotTransfer.setDeptId(shipEntity.getDeptId());
        return snapshotTransferMapper.addSnapshotTransfer(snapshotTransfer);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateSnapshotTransfer(SnapshotTransferEntity snapshotTransfer) {
        int rows = snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);

        SnapshotUtil.renewSnapshotTransfer(snapshotTransfer);
        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotTransferById(Long id) {
        int rows = snapshotTransferMapper.deleteSnapshotTransferById(id);
        SnapshotUtil.addAllInfo();
        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(Long id, Integer status) {
        SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(id);

        snapshotTransfer.setStatus(status);

        int rows = snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);
        SnapshotUtil.renewSnapshotTransfer(snapshotTransfer);

        return rows;
    }


    /**
     * 船上更新
     *
     * @param syncEntity
     * @param sn
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleBySync(SyncEntity syncEntity, String sn) {
        TransferSnapshotVo transferSnapshotVo = JSON.parseObject(syncEntity.getJsonObject(), TransferSnapshotVo.class);
        SnapshotTransferEntity syncTransfer = transferSnapshotVo.getSnapshotTransferEntity();
        SnapshotTransferEntity snapshotTransferEntity = selectBySnAndChannelCode(sn, syncTransfer.getChannelCode());

        if (syncEntity.getAction() == 1 && snapshotTransferEntity == null) {

            syncTransfer.setSn(sn);
            syncTransfer.setCreateBy("sync");

            ShipEntity shipEntity = shipService.selectShipBySn(syncTransfer.getSn());
            syncTransfer.setDeptId(shipEntity.getDeptId());
            if (StringUtils.isNotBlank(syncTransfer.getConnectStatus()) && (syncTransfer.getConnectStatus().equalsIgnoreCase("1") || syncTransfer.getConnectStatus().equalsIgnoreCase("连接成功"))){
                syncTransfer.setConnectStatus("1");
            }else {
                syncTransfer.setConnectStatus("0");
            }
            snapshotTransferMapper.addSnapshotTransfer(syncTransfer);

        } else if (syncEntity.getAction() == 2 && snapshotTransferEntity != null) {

            snapshotTransferEntity.setResolvingPower(syncTransfer.getResolvingPower());
            snapshotTransferEntity.setCompartment(syncTransfer.getCompartment());
            snapshotTransferEntity.setCost(syncTransfer.getCost());
            snapshotTransferEntity.setStatus(syncTransfer.getStatus());
            snapshotTransferEntity.setTransferStatus(syncTransfer.getTransferStatus());
            if (StringUtils.isNotBlank(syncTransfer.getConnectStatus()) && (syncTransfer.getConnectStatus().equalsIgnoreCase("1") || syncTransfer.getConnectStatus().equalsIgnoreCase("连接成功"))){
                snapshotTransferEntity.setConnectStatus("1");
            }else {
                snapshotTransferEntity.setConnectStatus("0");
            }
            snapshotTransferEntity.setUpdateBy("sync");

            snapshotTransferMapper.updateSnapshotTransfer(snapshotTransferEntity);

        } else if (syncEntity.getAction() == 3 && snapshotTransferEntity != null) {
            deleteSnapshotTransferById(snapshotTransferEntity.getId());
        }
    }


    /**
     * 更新到船端
     *
     * @param transferEntity
     * @param action
     */
    public void sync2Ship(SnapshotTransferEntity transferEntity, int action) {
        SnapshotTransferEntity entity = new SnapshotTransferEntity();
        entity.setSn(transferEntity.getSn());
        entity.setChannelCode(transferEntity.getChannelCode());
        entity.setResolvingPower(transferEntity.getResolvingPower());
        entity.setCompartment(transferEntity.getCompartment());
        entity.setCost(transferEntity.getCost());
        entity.setStatus(transferEntity.getStatus());
        entity.setTransferStatus(transferEntity.getTransferStatus());
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(entity), action, "snapshotTransfer");

        KafkaMessage kafkaMessage = new KafkaMessage(transferEntity.getChannelCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(transferEntity.getSn());
        syncService.sendSync2Kafka(kafkaMessage);
    }

    @Transactional(rollbackFor = Exception.class)
    public int changeTrStatus(Long id, Integer status) throws SchedulerException {
        SnapshotTransferEntity snapshotTransfer = selectSnapshotTransferById(id);
        //这边需要同步到船端去调用
//        SysJob sysJob = sysJobService.selectJobByName(snapshotTransfer.getChannelCode());
//        if (sysJob != null) {
//            sysJob.setStatus(status == 1 ? ScheduleConstants.Status.NORMAL.getValue() : ScheduleConstants.Status.PAUSE.getValue());
//            sysJobService.changeStatus(sysJob);
//        }
        //传输状态开启时，存储状态也开启
        if (status==1){
            snapshotTransfer.setStatus(status);//存储状态
        }
        snapshotTransfer.setTransferStatus(status);//传输状态

        return snapshotTransferMapper.updateSnapshotTransfer(snapshotTransfer);
    }
}
