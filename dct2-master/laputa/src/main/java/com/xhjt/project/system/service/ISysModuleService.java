package com.xhjt.project.system.service;

import com.xhjt.project.system.domain.SysModule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ISysModuleService {

    List<HashMap> getModuleShow(SysModule sysModule);

    int insertModule(SysModule moduleInput);

    String checkModuleLabelUnique(SysModule module);

    List<SysModule> selectModuleListByType(SysModule module);

    int deleteModuleById(SysModule sysModule);

    int updateModule(SysModule moduleInput);

    List<SysModule> getAddForm(int type);

    String checkModulePropertyUnique(SysModule module);

    int deleteModuleByType(SysModule module);

    Map getModuleShowByParam(SysModule sysModule);

    List<SysModule> selectModuleListByComIds(SysModule sysModule);

    List<SysModule> selectModuleListByEdit(SysModule sysModule);

    SysModule selectModuleListByProperty(SysModule sysModule);
}
