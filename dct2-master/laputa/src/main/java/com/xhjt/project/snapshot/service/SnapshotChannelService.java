package com.xhjt.project.snapshot.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.ListUtil;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.device.domain.ChannelStatus;
import com.xhjt.project.netty.service.SyncService;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.snapshot.domain.TransferSnapshotVo;
import com.xhjt.project.snapshot.mapper.SnapshotChannelMapper;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 通道配置 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotChannelService {
    public final static Logger logger = LoggerFactory.getLogger(SnapshotChannelService.class);
    @Autowired
    private SnapshotChannelMapper snapshotChannelMapper;
    @Autowired
    private ShipService shipService;
    @Autowired
    private SyncService syncService;
    @Autowired
    private SnapshotTransferService snapshotTransferService;

    @Transactional(rollbackFor = Exception.class)
    @DataScope(deptAlias = "d")
    public List<SnapshotChannelEntity> selectSnapshotChannelList(SnapshotChannelEntity snapshotChannelEntity) {
        return snapshotChannelMapper.selectSnapshotChannelList(snapshotChannelEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotChannelEntity selectSnapshotChannel(SnapshotChannelEntity snapshotChannelEntity) {
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannelEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotChannelEntity> queryList(SnapshotChannelEntity snapshotChannelEntity) {
        return snapshotChannelMapper.selectSnapshotChannelList(snapshotChannelEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotChannelEntity> queryUnboundList(SnapshotChannelEntity snapshotChannelEntity) {
        List<SnapshotChannelEntity> queryList = snapshotChannelMapper.selectSnapshotChannelList(snapshotChannelEntity);

        return queryList.stream()
                .filter(channel -> SnapshotUtil.getTransfer(channel.getSn(), channel.getCode()) == null)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotChannelEntity selectSnapshotChannelById(Long id) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setId(id);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    public  SnapshotChannelEntity  selectSnapshotChannelByName(String name,String sn) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setName(name);
        snapshotChannel.setSn(sn);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotChannelEntity selectSnapshotChannelBySnAndCode(String sn, String code) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setSn(sn);
        snapshotChannel.setCode(code);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotChannelEntity addSnapshotChannel(SnapshotChannelEntity snapshotChannel) throws Exception {

        if (selectSnapshotChannelByName(snapshotChannel.getName(),snapshotChannel.getSn()) != null) {
            return null;
        }
        snapshotChannel.setCode(getCode());

        ShipEntity shipEntity = shipService.selectShipBySn(snapshotChannel.getSn());
        snapshotChannel.setDeptId(shipEntity.getDeptId());
        int row = snapshotChannelMapper.addSnapshotChannel(snapshotChannel);
        if (row>0){
            SnapshotChannelEntity snapshotChannelEntity = this.selectSnapshotChannelBySnAndCode(snapshotChannel.getSn(),snapshotChannel.getCode());
            return snapshotChannelEntity;
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateSnapshotChannel(SnapshotChannelEntity snapshotChannel) {
        SnapshotChannelEntity channelEntity = selectSnapshotChannelByName(snapshotChannel.getName(),snapshotChannel.getSn());
        if (channelEntity != null && !channelEntity.getId().equals(snapshotChannel.getId())) {
            return 0;
        }

        return snapshotChannelMapper.updateSnapshotChannel(snapshotChannel);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotChannelById(Long id) {
        return snapshotChannelMapper.deleteSnapshotChannelById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotChannelByCode(String code) {
        return snapshotChannelMapper.deleteSnapshotChannelByCode(code);
    }

    private String getCode() throws Exception {
        List<SnapshotChannelEntity> list = selectSnapshotChannelList(null);
        List<String> codeList = ListUtil.fetchFieldValuesList(list, "code");

        return Stream.iterate(0, i -> i++)
                .map(i -> getCharAndNum(4))
                .filter(str -> !codeList.contains(str))
                .limit(1)
                .collect(Collectors.joining());
    }

    private String getCharAndNum(int length) {
        StringBuilder val = new StringBuilder();
        Random random = new Random();

        Stream.iterate(0, i -> i + 1).limit(length).forEach(i -> {
            // 字符串或者数字
            if (random.nextInt(2) % 2 == 0) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val.append((char) (choice + random.nextInt(26)));
            } else {
                val.append((random.nextInt(10)));
            }
        });

        return val.toString();
    }


    /**
     * 船上更新
     *
     * @param syncEntity
     * @param sn
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleBySync(SyncEntity syncEntity, String sn) {
        TransferSnapshotVo transferSnapshotVo = JSON.parseObject(syncEntity.getJsonObject(), TransferSnapshotVo.class);
        SnapshotChannelEntity syncChannel = transferSnapshotVo.getSnapshotChannelEntity();
        SnapshotChannelEntity snapshotChannelEntity = selectSnapshotChannelBySnAndCode(sn, syncChannel.getCode());

        if (syncEntity.getAction() == 1 && snapshotChannelEntity == null) {

            syncChannel.setSn(sn);
            syncChannel.setCreateBy("sync");

            ShipEntity shipEntity = shipService.selectShipBySn(syncChannel.getSn());
            syncChannel.setDeptId(shipEntity.getDeptId());

            snapshotChannelMapper.addSnapshotChannel(syncChannel);

        } else if (syncEntity.getAction() == 2 && snapshotChannelEntity != null) {

            snapshotChannelEntity.setName(syncChannel.getName());
            snapshotChannelEntity.setAddress(syncChannel.getAddress());
            snapshotChannelEntity.setStorageTime(syncChannel.getStorageTime());
            snapshotChannelEntity.setUpdateBy("sync");

            snapshotChannelMapper.updateSnapshotChannel(snapshotChannelEntity);

        } else if (syncEntity.getAction() == 3 && snapshotChannelEntity != null) {
            deleteSnapshotChannelById(snapshotChannelEntity.getId());
        }
    }

    /**
     * 更新到船端
     *
     * @param channelEntity
     * @param action
     */
    public void sync2Ship(SnapshotChannelEntity channelEntity, int action) {
        SnapshotChannelEntity entity = new SnapshotChannelEntity();
        entity.setSn(channelEntity.getSn());
        entity.setCode(channelEntity.getCode());
        entity.setName(channelEntity.getName());
        entity.setAddress(channelEntity.getAddress());
        entity.setStorageTime(channelEntity.getStorageTime());

        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(entity), action, "snapshotChannel");

        KafkaMessage kafkaMessage = new KafkaMessage(channelEntity.getCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(channelEntity.getSn());
        syncService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 5分钟船端通道状态定时同步到岸端
     * @param channelStatus
     * @param sn
     */
    public void handleStatusBySync(ChannelStatus channelStatus, String sn) {
        SnapshotChannelEntity snapshotChannelEntity = selectSnapshotChannelBySnAndCode(sn, channelStatus.getCode());
        if (snapshotChannelEntity == null){
            return;
        }
        SnapshotTransferEntity snapshotTransferEntity = snapshotTransferService.selectBySnAndChannelCode(sn,snapshotChannelEntity.getCode());
        if (snapshotTransferEntity!=null){
            snapshotTransferEntity.setTransferStatus(channelStatus.getTransferStatus());
            snapshotTransferEntity.setStatus(channelStatus.getTrStatus());
            snapshotTransferEntity.setConnectStatus(String.valueOf(channelStatus.getStatus()));
            snapshotTransferEntity.setUpdateBy("sync");
            snapshotTransferService.updateSnapshotTransfer(snapshotTransferEntity);
            logger.info("定时同步结束555------{}---",snapshotTransferEntity.getConnectStatus());
        }

    }

    /**
     * 更新快照通道及属性到船端
     * @param transferSnapshotVo
     * @param action
     */
    public void syncNewShore(TransferSnapshotVo transferSnapshotVo, int action) {
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(transferSnapshotVo), action, "transfer2Snapshot");
        KafkaMessage kafkaMessage = new KafkaMessage(transferSnapshotVo.getSnapshotChannelEntity().getCode(), JSONObject.toJSONString(syncEntity), transferSnapshotVo.getSnapshotTransferEntity().getCost(), System.currentTimeMillis());
        kafkaMessage.setSn(transferSnapshotVo.getSnapshotChannelEntity().getSn());
        syncService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 预览
     *
     * @param channelEntity
     * @param action
     */
    public void sync2ShipView(SnapshotChannelEntity channelEntity, int action) {
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(channelEntity), action, "snapshotView");
        KafkaMessage kafkaMessage = new KafkaMessage(channelEntity.getCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(channelEntity.getSn());
        syncService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 控制类操作-目前操作船端重启机器以及修改redis action均默认为1
     *
     * @param
     * @param
     */
    public void sync2ShipController(SyncEntity syncEntity,String code, String sn) {
        KafkaMessage kafkaMessage = new KafkaMessage(code, JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(sn);
        syncService.sendSync2Kafka(kafkaMessage);
    }

}
