package com.xhjt.project.view.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.view.domain.ViewSchemeEntity;
import com.xhjt.project.view.service.ViewSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视图方案信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/view/scheme")
public class ViewSchemeController extends BaseController {

    @Autowired
    private ViewSchemeService viewSchemeService;

    /**
     * 获取视图方案信息列表
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:list')")
    @GetMapping("/list")
    public TableDataInfo list(ViewSchemeEntity viewScheme) {
        startPage();
        List<ViewSchemeEntity> list = viewSchemeService.selectViewSchemeList(viewScheme);
        return getDataTable(list);
    }

    @Log(title = "视图方案管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('view:scheme:export')")
    @GetMapping("/export")
    public AjaxResult export(ViewSchemeEntity viewScheme) {
        List<ViewSchemeEntity> list = viewSchemeService.selectViewSchemeList(viewScheme);
        ExcelUtil<ViewSchemeEntity> util = new ExcelUtil<ViewSchemeEntity>(ViewSchemeEntity.class);
        return util.exportExcel(list, "视图方案数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:query')")
    @GetMapping(value = "/{viewSchemeId}")
    public AjaxResult getInfo(@PathVariable Long viewSchemeId) {
        return AjaxResult.success(viewSchemeService.selectViewSchemeById(viewSchemeId));
    }

    /**
     * 新增视图方案信息
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:add')")
    @Log(title = "视图方案新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ViewSchemeEntity viewScheme) {
        viewScheme.setCreateBy(SecurityUtils.getUsername());
        return toAjax(viewSchemeService.addViewScheme(viewScheme));
    }

    /**
     * 修改视图方案信息
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:edit')")
    @Log(title = "视图方案修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ViewSchemeEntity viewScheme) {
        viewScheme.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(viewSchemeService.updateViewScheme(viewScheme));
    }

    /**
     * 删除视图方案信息
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:remove')")
    @Log(title = "视图方案删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{viewSchemeIds}")
    public AjaxResult remove(@PathVariable Long[] viewSchemeIds) {
        return toAjax(viewSchemeService.deleteViewSchemeByIds(viewSchemeIds));
    }

    /**
     * 启用
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:edit')")
    @PostMapping(value = "/enable/{id}")
    public AjaxResult enable(@PathVariable Long id) {
        return toAjax(viewSchemeService.enable(id));
    }

    /**
     * 禁用
     */
    @PreAuthorize("@ss.hasPermi('view:scheme:edit')")
    @PostMapping(value = "/disable/{id}")
    public AjaxResult disable(@PathVariable Long id) {
        return toAjax(viewSchemeService.disable(id));
    }
}
