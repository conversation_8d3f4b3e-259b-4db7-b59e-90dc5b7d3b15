package com.xhjt.project.snapshot.mapper;

import com.xhjt.project.snapshot.domain.SnapshotRealEntity;

import java.util.List;

public interface SnapshotRealMapper {

    SnapshotRealEntity selectChannelReal(SnapshotRealEntity snapshotRealEntity);

    List<SnapshotRealEntity> selectChannelRealList(SnapshotRealEntity snapshotRealEntity);

    int addSnapshotReal(SnapshotRealEntity snapshotRealEntity);

    int editSnapshotReal(SnapshotRealEntity snapshotRealEntity);

    SnapshotRealEntity selectNewest(SnapshotRealEntity snapshotRealEntity);

    SnapshotRealEntity selectSnapshotReal(SnapshotRealEntity snapshotRealEntity);
}
