package com.xhjt.project.ship.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xhjt.common.constant.HttpStatus;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.ServletUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.ip.IpUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.common.SynchroEntity;
import com.xhjt.project.monitor.domain.SysOperLog;
import com.xhjt.project.monitor.service.ISysOperLogService;
import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.service.CruiseService;
import com.xhjt.project.ship.service.CruiseStationService;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.system.domain.SysDept;
import com.xhjt.project.system.domain.SysModule;
import com.xhjt.project.system.service.ISysDeptService;
import com.xhjt.project.system.service.ISysModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 航次信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cruise")
public class CruiseController extends BaseController {

    @Autowired
    private CruiseService cruiseService;

    @Autowired
    private ShipService shipService;

    @Autowired
    private ISysModuleService moduleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysOperLogService iSysOperLogService;

    @Autowired
    private CruiseStationService cruiseStationService;


    /**
     * 获取航次信息动态列
     */
    @GetMapping("/getColumn")
    public AjaxResult getColumn() {
        List<HashMap<String, Object>> res = new ArrayList<>();
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(1);
        sysModule.setTableShow(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);

        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        for (SysModule o:moduleList) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("id",o.getId().toString());
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("characterType",o.getCharacterType()+"");
            map.put("sortNum",o.getSortNum()+"");
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });

        return AjaxResult.success(res);
    }

    /**
     * 获取新增表单
     */
    @GetMapping(value = "/addForm")
    public AjaxResult getAddForm(){
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(1);
        sysModule.setFormShow(1);
        sysModule.setIsRequired(0);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o: list) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("isRequired",o.getIsRequired());
            map.put("value",null);
            map.put("characterType",o.getCharacterType());
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }

    /**
     * 获取新增表单-必填
     */
    @GetMapping(value = "/addForm2")
    public AjaxResult getAddForm2(){
        SysModule sysModule = new SysModule();
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        sysModule.setType(1);
        sysModule.setFormShow(1);
        sysModule.setIsRequired(1);
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        List<SysModule> list = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> res = new ArrayList<>();
        for (SysModule o: list) {
            HashMap<String,Object> map = new HashMap<>();
            map.put("property",o.getProperty());
            map.put("label",o.getLabel());
            map.put("isRequired",o.getIsRequired());
            map.put("value",null);
            map.put("characterType",o.getCharacterType());
            map.put("sortNum",o.getSortNum());
            res.add(map);
        }

        Collections.sort(res, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(res);
    }

    /**
     * 获取航次信息列表
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(CruiseEntity cruise) {
        SysModule sysModule1 = new SysModule();
        sysModule1.setType(1);
        sysModule1.setTableShow(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule1.setComIds(comIds);
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule1);

        List<String> properties = new ArrayList<>();//属性，用于判断
        for (SysModule sysModule:moduleList) {
            properties.add(sysModule.getProperty());
        }
        cruise.setDeptId(deptId);
        startPage();
        List<CruiseEntity> list = cruiseService.selectNewCruiseList(cruise);
        List<HashMap> resultList = new ArrayList<>();//最终返回
        for (CruiseEntity s:list) {
            HashMap<String,Object> map= new HashMap<>();
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(properties.contains(entry.getKey())){//如果属于动态列中
                        map.put("cruiseId",s.getCruiseId());
                        Object value = entry.getValue();
                        SysModule sysModule = new SysModule();
                        sysModule.setProperty(entry.getKey());
                        sysModule.setType(1);
                        SysModule selectModule = moduleService.selectModuleListByProperty(sysModule);
                        if (!entry.getValue().equals("") && entry.getValue() != null){
                            if(selectModule.getCharacterType() == 1){// 1.时间
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                value = sdf.format(new Date(Long.parseLong(value+"")));
                            } else if(selectModule.getCharacterType() == 3) {// 3.日期
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                value = sdf.format(new Date(Long.parseLong(value+"")));
                            }
                        }

                        map.put(entry.getKey(),value);//存入map
                    }
                }
                resultList.add(map);
            }
        }
        //分页
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(resultList);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 获取全部航次信息列表
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:list')")
    @GetMapping("/allList")
    public AjaxResult allList(CruiseEntity cruise) {
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());

        cruise.setDeptId(sysDept.getDeptId());
        List<CruiseEntity> list = cruiseService.selectAllNewCruise(cruise);
        for (CruiseEntity s:list) {
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(entry.getKey()=="cruiseName"){
                        s.setCruiseName(entry.getValue()+"");
                    }
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 新增航次信息
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:add')")
    @Log(title = "航次新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody JSONObject cruise) {
        CruiseEntity cruise1 = new CruiseEntity();
        String code = "";
        String sn = "";
        String cruiseName = "";
        List<HashMap> editRequiredForm = (List<HashMap>) cruise.get("editRequiredForm");
        for(HashMap hashMap : editRequiredForm){
            if(hashMap.get("property").equals("code")){
                code = hashMap.get("value")+"";
            }
            if(hashMap.get("property").equals("sn")){
                sn = hashMap.get("value")+"";
            }
            if(hashMap.get("property").equals("cruiseName")){
                cruiseName = hashMap.get("value")+"";
            }
        }
//        cruise1.setCode(code);
        cruise1.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        cruise1.setSn(sn);
        cruise1.setCruiseName(cruiseName);
        List<CruiseEntity> list = cruiseService.selectCruiseNewByFactor(cruise1);
        if (list!=null && list.size() > 0) {
            return AjaxResult.error("航次名称已存在");
        }

        int row = cruiseService.addCruise(cruise);
        if (row > 0) {
            //同步添加操作
//            ShipEntity ship = shipService.selectShipBySn(cruise1.getSn());
//            SynchroEntity synchro = new SynchroEntity();
//            synchro.setObject(cruise);
//            synchro.setAction("insert");
//            synchro.setModule("cruise");
//            synchroService.synchroDataRequest(synchro, ship.getSn());
        }
        return toAjax(row);
    }


    @Log(title = "航次管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('cruise:info:export')")
    @GetMapping("/export")
    public AjaxResult export(CruiseEntity cruise) {
        SysModule sysModule1 = new SysModule();
        sysModule1.setType(1);
        sysModule1.setTableShow(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule1.setComIds(comIds);
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule1);

        List<String> properties = new ArrayList<>();//属性，用于判断
        for (SysModule sysModule:moduleList) {
            properties.add(sysModule.getProperty());
        }
        cruise.setDeptId(deptId);
        List<CruiseEntity> list = cruiseService.selectNewCruiseList(cruise);
        startPage();
        List<HashMap> resultList = new ArrayList<>();//最终返回
        for (CruiseEntity s:list) {
            HashMap<String,Object> map= new HashMap<>();
            if(s.getModuleJson()!=null){
                String moduleJson = s.getModuleJson();
                JSONObject jsonObject = JSONObject.parseObject(moduleJson);//将船只表的moduleJson String类型转为json
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
                    if(properties.contains(entry.getKey())){//如果属于动态列中
                        map.put("cruiseId",s.getCruiseId());
                        Object value = entry.getValue();
                        if(entry.getKey().contains("Time") && !entry.getValue().equals("") && entry.getValue() != null){
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            value = sdf.format(new Date(Long.parseLong(value+"")));
                        }
                        map.put(entry.getKey(),value);//存入map
                    }
                }
                resultList.add(map);
            }
        }
        List<CruiseEntity> resStation = new ArrayList<>();
        for(HashMap hashMap : resultList){
            CruiseEntity cruiseEntity = JSON.parseObject(JSON.toJSONString(hashMap), CruiseEntity.class);
            resStation.add(cruiseEntity);
        }

        ExcelUtil<CruiseEntity> util = new ExcelUtil<CruiseEntity>(CruiseEntity.class);
        return util.exportExcel(resStation, "航次数据");
    }

    /**
     * 修改-获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:query')")
    @GetMapping(value = "/NoRequired/{cruiseId}")
    public AjaxResult getInfo(@PathVariable Long cruiseId) {
        CruiseEntity cruise = cruiseService.selectNewCruiseById(cruiseId);
        JSONObject jsonObject = JSONObject.parseObject(cruise.getModuleJson());//将moduleJson String类型转为json
        SysModule sysModule = new SysModule();
        sysModule.setType(1);
        sysModule.setIsRequired(0);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        sysModule.setComId(sysDept.getDeptId());
        Map map1 = moduleService.getModuleShowByParam(sysModule);
        if(map1 == null){
            sysModule.setComId(sysDept.getParentId());
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        if(map1 == null){
            sysModule.setComId(Long.parseLong(100+""));
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        String property = map1.get("property")+"";
        String[] propertyArr = property.split(",");
        for(int i = 0;i < propertyArr.length;i++){
            if(!jsonObject.containsKey(propertyArr[i])){
                jsonObject.put(propertyArr[i],null);
            }
        }
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> list = new ArrayList<>();

        for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
            for(SysModule module: moduleList){
                if(entry.getKey().equals(module.getProperty()) && module.getIsRequired() == 0 && module.getTableShow() == 1 && module.getFormShow() == 1){
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("property",entry.getKey());
                    map.put("value",entry.getValue());
                    map.put("label",module.getLabel());
                    map.put("isRequired",module.getIsRequired());
                    map.put("characterType",module.getCharacterType());
                    map.put("sortNum",module.getSortNum());
                    list.add(map);
                }
            }
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 修改-获取详细信息-必填
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:query')")
    @GetMapping(value = "/Required/{cruiseId}")
    public AjaxResult getInfo2(@PathVariable Long cruiseId) {
        CruiseEntity cruise = cruiseService.selectNewCruiseById(cruiseId);
        JSONObject jsonObject = JSONObject.parseObject(cruise.getModuleJson());//将moduleJson String类型转为json
        SysModule sysModule = new SysModule();
        sysModule.setType(1);
        sysModule.setIsRequired(1);
        SysDept sysDept = deptService.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
        Long comId = sysDept.getParentId();
        Long deptId = sysDept.getDeptId();
        String comIds = deptId + "," + comId + "";
        comIds = getComIds(comId,comIds);
        sysModule.setComIds(comIds);
        sysModule.setComId(sysDept.getDeptId());
        Map map1 = moduleService.getModuleShowByParam(sysModule);
        if(map1 == null){
            sysModule.setComId(sysDept.getParentId());
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        if(map1 == null){
            sysModule.setComId(Long.parseLong(100+""));
            map1 = moduleService.getModuleShowByParam(sysModule);
        }
        String property = map1.get("property")+"";
        String[] propertyArr = property.split(",");
        for(int i = 0;i < propertyArr.length;i++){
            if(!jsonObject.containsKey(propertyArr[i])){
                jsonObject.put(propertyArr[i],null);
            }
        }
        List<SysModule> moduleList = moduleService.selectModuleListByComIds(sysModule);
        List<HashMap<String, Object>> list = new ArrayList<>();

        for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {//对moduleJson遍历
            for(SysModule module: moduleList){
                if(entry.getKey().equals(module.getProperty()) && module.getIsRequired() == 1 && module.getTableShow() == 1 && module.getFormShow() == 1){
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("property",entry.getKey());
                    map.put("value",entry.getValue());
                    map.put("label",module.getLabel());
                    map.put("isRequired",module.getIsRequired());
                    map.put("characterType",module.getCharacterType());
                    map.put("sortNum",module.getSortNum());
                    list.add(map);
                }
            }
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer name1 = Integer.parseInt(o1.get("sortNum")+"");
                Integer name2 = Integer.parseInt(o2.get("sortNum")+"");
                return name1.compareTo(name2);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 修改航次信息
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody JSONObject cruiseEntity) {
        //获取数据库信息 ->selectCruise 如果id不对，显示数据不存在
        CruiseEntity selectCruise = cruiseService.selectNewCruiseById(Long.parseLong(cruiseEntity.get("cruiseId").toString())) ;
        if (selectCruise == null) {
            throw new CustomException("数据不存在");
        }
        //获取表单信息
        CruiseEntity cruise = new CruiseEntity();
        List<HashMap> list = (List) cruiseEntity.get("editNoRequiredForm");
        List<HashMap> list2 = (List) cruiseEntity.get("editRequiredForm");
        for(int i = 0;i < list2.size();i++){
            list.add(list2.get(i));
        }
        List<HashMap> res = new ArrayList<>();
        String cruiseName = "";
        for(int i = 0;i < list.size();i++){
            HashMap<String,Object> map = new HashMap();
            if(list.get(i).get("value")!=null && !list.get(i).get("value").equals("null")){
                map.put("property",list.get(i).get("property")+"");
                map.put("value",list.get(i).get("value"));
                if(map.get("property").equals("cruiseName")){
                    cruiseName = map.get("value")+"";
                }
                res.add(map);
            }
        }

        Map map = res.stream().collect(Collectors.toMap(p->p.get("property"), p->p.get("value")));//moduleJSon(Map)
        String updateBy = SecurityUtils.getUsername();
        map.put("deptId",SecurityUtils.getLoginUser().getUser().getDeptId());
        map.put("updateBy",updateBy);
        Date date = new Date();
        map.put("updateTime",date);
        map.put("editTime",date);
        cruise.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());

        String s = JSONObject.toJSONString(map);
        cruise.setModuleJson(s);
        cruise.setSn(selectCruise.getSn());
        cruise.setCruiseId(Long.parseLong(cruiseEntity.get("cruiseId")+""));

        CruiseEntity cruise1 = new CruiseEntity();
        cruise1.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        cruise1.setSn(selectCruise.getSn());
        cruise1.setCruiseName(cruiseName);
        List<CruiseEntity> list3 = cruiseService.selectCruiseNewByFactor(cruise1);
        if (list3!=null && list3.size() > 0) {
            if (list3.get(0).getCruiseId()!=selectCruise.getCruiseId()) {
                return AjaxResult.error("航次名称已存在");
            }
        }


        //两者对比moduleJson -> form如果没有select的（比如cereateBy、cereateTime）
        String formModuleJson = cruise.getModuleJson();
        JSONObject formJsonObject = JSONObject.parseObject(formModuleJson);
        String selectModuleJson = selectCruise.getModuleJson();
        JSONObject selectJsonObject = JSONObject.parseObject(selectModuleJson);
        for (Map.Entry<String, Object> selectEntry: selectJsonObject.entrySet()) {
            if(!formJsonObject.containsKey(selectEntry.getKey()) && selectEntry.getKey()!=null && StringUtils.isNotBlank(selectEntry.getValue()+"")){
                formJsonObject.put(selectEntry.getKey(),selectEntry.getValue());
            }
        }

        cruise.setModuleJson(formJsonObject+"");
        int row = cruiseService.updateNewCruise(cruise);
        if (row > 0) {
            //同步修改操作
//            ShipEntity ship = shipService.selectShipBySn(cruise.getSn());
//            SynchroEntity synchro = new SynchroEntity();
//            synchro.setObject(cruise);
//            synchro.setAction("update");
//            synchro.setModule("cruise");
//            synchroService.synchroDataRequest(synchro, ship.getSn());
        }

        //打印日志
        String log = "";
        for (Map.Entry<String, Object> entry: formJsonObject.entrySet()) {
            for (Map.Entry<String, Object> entry2: selectJsonObject.entrySet()) {
                if(entry.getKey().equals(entry2.getKey()) && !entry.getValue().equals(entry2.getValue()) && !entry.getKey().equals("updateTime")){
                    log = log + "{" + entry.getKey() + "的[" + entry2.getValue() + "]改为[" + entry.getValue() +" ]}";
                }
            }
        }
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setTitle("航次修改");
        sysOperLog.setMethod("修改");
        sysOperLog.setBusinessType(2);
        sysOperLog.setRequestMethod("PUT");
        sysOperLog.setOperatorType(1);
        sysOperLog.setOperUrl("/cruise");
        sysOperLog.setOperLocation("内网IP");
        sysOperLog.setOperParam(log);
        sysOperLog.setJsonResult("{\"msg\":\"操作成功\",\"code\":200}");
        sysOperLog.setStatus(0);
        sysOperLog.setOperName(SecurityUtils.getUsername());
        sysOperLog.setOperTime(new Date());
        sysOperLog.setOperIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        iSysOperLogService.insertOperlog(sysOperLog);

        return toAjax(row);
    }

    /**
     * 删除航次信息
     */
    @PreAuthorize("@ss.hasPermi('cruise:info:remove')")
    @DeleteMapping("/{cruiseIds}")
    public AjaxResult remove(@PathVariable Long[] cruiseIds) {
        int row = 0;


        //打印日志
        String log = "";
        for (Long id : cruiseIds) {
            log = log + "[" + cruiseService.selectNewCruiseById(id).getCruiseName() + "]";
        }
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setTitle("航次删除");
        sysOperLog.setMethod("删除");
        sysOperLog.setBusinessType(3);
        sysOperLog.setRequestMethod("DELETE");
        sysOperLog.setOperatorType(1);
        sysOperLog.setOperUrl("/cruise");
        sysOperLog.setOperLocation("内网IP");
        sysOperLog.setOperParam(log);
        sysOperLog.setOperName(SecurityUtils.getUsername());
        sysOperLog.setJsonResult("{\"msg\":\"操作成功\",\"code\":200}");
        sysOperLog.setStatus(0);
        sysOperLog.setOperTime(new Date());
        sysOperLog.setOperIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        iSysOperLogService.insertOperlog(sysOperLog);

        //同步删除操作
        for (Long cruiseId : cruiseIds) {
            CruiseEntity cruiseEntity = cruiseService.selectNewCruiseById(cruiseId);
            String code = cruiseEntity.getCode();
            CruiseStationEntity cruiseStationEntity = new CruiseStationEntity();
            cruiseStationEntity.setCruiseCode(code);
            List<CruiseStationEntity> list = cruiseStationService.selectNewCruiseStationList(cruiseStationEntity);
            if(list.size() == 0){
                row = cruiseService.deleteNewCruiseById(cruiseId);
                if (row > 0) {
//                ShipEntity ship = shipService.selectShipBySn(cruiseEntity.getSn());
                    SynchroEntity synchro = new SynchroEntity();
                    synchro.setObject(cruiseEntity.getCode());
                    synchro.setAction("delete");
                    synchro.setModule("cruise");
//                synchroService.synchroDataRequest(synchro, ship.getSn());
                }
            } else{
                return AjaxResult.error("请先删除相关站位信息");
            }

        }
        return toAjax(row);

    }

    public String getComIds(Long comId, String comIds){
        while(comId != 0){
            SysDept sysDept1 = deptService.selectDeptById(comId);
            comId = sysDept1.getParentId();
            comIds = comIds + "," + comId.toString();
        }
        return comIds;
    }
}
