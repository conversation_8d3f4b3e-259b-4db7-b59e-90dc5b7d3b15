package com.xhjt.project.snapshot.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.snapshot.domain.SnapshotRealEntity;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotLogService;
import com.xhjt.project.snapshot.service.SnapshotRealService;
import com.xhjt.project.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 实时查看
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/real")
public class SnapshotRealController extends BaseController {

    @Autowired
    private SnapshotRealService snapshotRealService;

    @PreAuthorize("@ss.hasPermi('snapshot:real:list')")
    @GetMapping("/list")
    public TableDataInfo list(SnapshotRealEntity snapshotRealEntity) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();

        //real
        snapshotRealEntity.setDeptId(deptId);
        List<SnapshotRealEntity> realList = snapshotRealService.selectSnapshotRealList(snapshotRealEntity);

        List<SnapshotRealEntity> result = new ArrayList<>();
        for (SnapshotRealEntity snapshotRealEntity1 : realList) {
            SnapshotRealEntity realEntity = new SnapshotRealEntity();
            realEntity.setSn(snapshotRealEntity1.getSn());
            realEntity.setDeptId(snapshotRealEntity1.getDeptId());
            realEntity.setChannelCode(snapshotRealEntity1.getChannelCode());

            if(snapshotRealService.selectNewest(realEntity)!= null){
                SnapshotRealEntity snapshotRealEntity2 = snapshotRealService.selectNewest(realEntity);
                Integer mark = snapshotRealEntity1.getMark();
                snapshotRealEntity2.setMark(mark);
                result.add(snapshotRealEntity2);
            }
        }

        return getDataTable(result);
    }

    @PreAuthorize("@ss.hasPermi('snapshot:real:add')")
    @PutMapping
    public AjaxResult add(SnapshotRealEntity snapshotRealEntity) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        snapshotRealEntity.setDeptId(deptId);
        String user = SecurityUtils.getUsername();
        SnapshotRealEntity realList = snapshotRealService.selectSnapshotReal(snapshotRealEntity);
        int row = 0;
        if(realList == null){
            snapshotRealEntity.setStatus(1);
            snapshotRealEntity.setCreateBy(user);
            row = snapshotRealService.addSnapshotReal(snapshotRealEntity);
        }else{
            snapshotRealEntity.setUpdateBy(user);
            snapshotRealEntity.setStatus(1);
            row = snapshotRealService.editSnapshotReal(snapshotRealEntity);
        }
        return toAjax(row);
    }

    @PreAuthorize("@ss.hasPermi('snapshot:real:query')")
    @GetMapping("/form")
    public AjaxResult getForm(SnapshotRealEntity snapshotRealEntity) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        snapshotRealEntity.setDeptId(deptId);
        SnapshotRealEntity realList = snapshotRealService.selectSnapshotReal(snapshotRealEntity);
        return AjaxResult.success(realList);
    }

}
