package com.xhjt.project.device.service;

import com.alibaba.fastjson.JSON;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.mapper.AcquisitionMiddlewareMapper;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.mapper.ShipMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class AcquisitionMiddlewareService {
    private Logger logger = LoggerFactory.getLogger(AcquisitionMiddlewareService.class);
    @Autowired
    private AcquisitionMiddlewareMapper acquisitionMiddlewareMapper;
    @Autowired
    private ShipMapper shipMapper;

    /**
     * 查询设备信息
     *
     * @param id 设备ID
     * @return 设备信息
     */
    public AcquisitionMiddleware selectAcquisitionMiddlewareById(Long id) {
        AcquisitionMiddleware acquisitionMiddleware = new AcquisitionMiddleware();
        acquisitionMiddleware.setId(id);
        return acquisitionMiddlewareMapper.selectAcquisitionMiddleware(acquisitionMiddleware);
    }

    /**
     * 查询设备信息
     *
     * @return 设备信息
     */
    public AcquisitionMiddleware selectAcqMiddlewareBySnAndMac(String mac,String sn) {
        AcquisitionMiddleware acquisitionMiddleware = new AcquisitionMiddleware();
        acquisitionMiddleware.setMac(mac);
        acquisitionMiddleware.setSn(sn);
        return acquisitionMiddlewareMapper.selectAcquisitionMiddleware(acquisitionMiddleware);
    }


    /**
     * 查询设备列表
     *
     * @param acquisitionMiddleware 设备信息
     * @return 设备集合
     */
    public List<AcquisitionMiddleware> selectAcquisitionMiddlewareList(AcquisitionMiddleware acquisitionMiddleware) {
        return acquisitionMiddlewareMapper.selectAcquisitionMiddlewareList(acquisitionMiddleware);
    }

    /**
     * 新增设备
     *
     * @param acquisitionMiddleware 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addAcquisitionMiddleware(AcquisitionMiddleware acquisitionMiddleware) {
        ShipEntity param = new ShipEntity();
        param.setSn(acquisitionMiddleware.getSn());
        ShipEntity shipEntity = shipMapper.selectNewShip(param);
        acquisitionMiddleware.setDeptId(shipEntity.getDeptId());
        return acquisitionMiddlewareMapper.addAcquisitionMiddleware(acquisitionMiddleware);
    }

    /**
     * 解析同步过来的采集终端进行入库操作
     *
     * @param syncEntity
     * @param sn
     */
    public void handleBySync(SyncEntity syncEntity, String sn) {
        AcquisitionMiddleware acquisitionMiddleware = JSON.parseObject(syncEntity.getJsonObject(), AcquisitionMiddleware.class);
        if (acquisitionMiddleware == null) {
            logger.error("采集终端解析错误------");
            return;
        }

        AcquisitionMiddleware middleware = this.selectAcqMiddlewareBySnAndMac(acquisitionMiddleware.getMac(),sn);

        if (syncEntity.getAction() == 1 && middleware == null) {
            //新增过来的
            acquisitionMiddleware.setSn(sn);
            this.addAcquisitionMiddleware(acquisitionMiddleware);
        } else if (syncEntity.getAction() == 2) {
            if(middleware != null){
                Long mId = middleware.getId();
                Long deptId = middleware.getDeptId();
                BeanUtils.copyProperties(acquisitionMiddleware,middleware);
                //修改过来的
                middleware.setSn(sn);
                middleware.setId(mId);
                middleware.setDeptId(deptId);
                middleware.setConnectStatus(1);
                this.updateAcquisitionMiddleware(middleware);
            }else{
                //新增过来的
                acquisitionMiddleware.setSn(sn);
                acquisitionMiddleware.setConnectStatus(1);
                this.addAcquisitionMiddleware(acquisitionMiddleware);
            }
        } else if (syncEntity.getAction() == 3 && middleware != null) {
            //删除过来的
            this.deleteAcquisitionMiddlewareById(middleware.getId());
        }
    }

    /**
     * 删除设备信息
     *
     * @param id 参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteAcquisitionMiddlewareById(Long id) {
        return acquisitionMiddlewareMapper.deleteAcquisitionMiddlewareById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteAcquisitionMiddlewareByMac(String mac) {
        return acquisitionMiddlewareMapper.deleteAcquisitionMiddlewareByMac(mac);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByIpAndPort(AcquisitionMiddleware am) {
        return acquisitionMiddlewareMapper.deleteByIpAndPort(am);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete4Invalid() {
        return acquisitionMiddlewareMapper.delete4Invalid();
    }

    /**
     * 修改设备
     *
     * @param am 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateAcquisitionMiddleware(AcquisitionMiddleware am) {
        return acquisitionMiddlewareMapper.updateAcquisitionMiddleware(am);
    }
}
