package com.xhjt.project.device.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.Transfer2DeviceVo;
import com.xhjt.project.device.domain.Transfer2SheetaEntity;
import com.xhjt.project.device.mapper.TransferAttributeMapper;
import com.xhjt.project.netty.service.SyncService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class TransferAttributeService {
    private Logger logger = LoggerFactory.getLogger(TransferAttributeService.class);
    @Autowired
    private TransferAttributeMapper transferAttributeMapper;

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private DeviceAttributeService deviceAttributeService;
    @Autowired
    private SyncService syncService;

    @Resource
    private ValueOperations<String, String> valueOperations;

    /**
     * 查询设备信息
     *
     * @param transferAttributeId 设备ID
     * @return 设备信息
     */
    @Transactional(rollbackFor = Exception.class)
    public TransferAttributeEntity selectTransferAttributeById(Long transferAttributeId) {
        TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
        transferAttribute.setId(transferAttributeId);
        return transferAttributeMapper.selectTransferAttribute(transferAttribute);
    }

    /**
     * 查询设备列表
     *
     * @param transferAttribute 设备信息
     * @return 设备集合
     */
    @DataScope(deptAlias = "d")
    @Transactional(rollbackFor = Exception.class)
    public List<TransferAttributeEntity> selectTransferAttributeList(TransferAttributeEntity transferAttribute) {
        return transferAttributeMapper.selectTransferAttributeList(transferAttribute);
    }

    /**
     * 查询设备列表
     *
     * @return 设备集合
     */
    @Transactional(rollbackFor = Exception.class)
    public List<TransferAttributeEntity> selectListBySnAndCode(String sn, String code) {
        TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
        transferAttribute.setSn(sn);
        transferAttribute.setDeviceCode(code);

        return transferAttributeMapper.selectTransferAttributeList(transferAttribute);
    }

    /**
     * 新增设备
     *
     * @param transferAttribute 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addTransferAttribute(TransferAttributeEntity transferAttribute) {
        return transferAttributeMapper.addTransferAttribute(transferAttribute);
    }

    /**
     * 修改设备
     *
     * @param transferAttribute 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Transfer2SheetaEntity> updateTransferAttribute(TransferAttributeEntity transferAttribute) {
        List<Transfer2SheetaEntity> entityList = new ArrayList<>();
        DeviceEntity device = deviceService.selectByCodeAndSn(transferAttribute.getSn(), transferAttribute.getDeviceCode());

        List<DeviceAttributeEntity> attributes = deviceAttributeService.selectListByType(device.getType());
        Map<String, String> attributeMap = attributes.stream().collect(Collectors.toMap(DeviceAttributeEntity::getName, DeviceAttributeEntity::getLabel));

        deleteTransferAttributeByCode(transferAttribute.getSn(), transferAttribute.getDeviceCode());
        for (int i = 0; i < transferAttribute.getTransferAttributes().length; i++) {
            TransferAttributeEntity entity = new TransferAttributeEntity();
            entity.setDeptId(device.getDeptId());
            entity.setSn(transferAttribute.getSn());
            entity.setDeviceCode(transferAttribute.getDeviceCode());
            entity.setName(transferAttribute.getTransferAttributes()[i]);
            entity.setLabel(attributeMap.get(transferAttribute.getTransferAttributes()[i]));
            entity.setOrderNum(i + 1);
            entity.setCreateBy(transferAttribute.getUpdateBy());

            //仅用于同步到船端使用
            Transfer2SheetaEntity transfer2SheetaEntity = new Transfer2SheetaEntity();
            BeanUtils.copyProperties(entity,transfer2SheetaEntity);
            entityList.add(transfer2SheetaEntity);
            transferAttributeMapper.addTransferAttribute(entity);
        }
//        Stream.iterate(0, i -> i + 1).limit(transferAttribute.getTransferAttributes().length).forEach(index -> {
//            TransferAttributeEntity entity = new TransferAttributeEntity();
//            entity.setDeptId(device.getDeptId());
//            entity.setSn(transferAttribute.getSn());
//            entity.setDeviceCode(transferAttribute.getDeviceCode());
//            entity.setName(transferAttribute.getTransferAttributes()[index]);
//            entity.setLabel(attributeMap.get(transferAttribute.getTransferAttributes()[index]));
//            entity.setOrderNum(index + 1);
//            entity.setCreateBy(transferAttribute.getUpdateBy());
//
//            //仅用于同步到船端使用
//            Transfer2SheetaEntity transfer2SheetaEntity = new Transfer2SheetaEntity();
//            BeanUtils.copyProperties(entity,transfer2SheetaEntity);
//            entityList.add(transfer2SheetaEntity);
//            transferAttributeMapper.addTransferAttribute(entity);
//        });

        return entityList;
    }


    /**
     * 修改设备
     *
     * @param list 设备信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBySync(List<TransferAttributeEntity> list, String sn, String deviceCode) {

        DeviceEntity device = deviceService.selectByCodeAndSn(sn, deviceCode);

        List<DeviceAttributeEntity> attributes = deviceAttributeService.selectListByType(device.getType());
        Map<String, String> map = attributes.stream().collect(Collectors.toMap(DeviceAttributeEntity::getName, DeviceAttributeEntity::getLabel));

        deleteTransferAttributeByCode(sn, deviceCode);

        if (list == null || list.size() == 0) {
            return;
        }
        list.forEach(entity -> {
            entity.setDeptId(device.getDeptId());
            entity.setSn(sn);
            entity.setDeviceCode(deviceCode);
            entity.setLabel(map.get(entity.getName()));
            entity.setCreateBy("sync");

            transferAttributeMapper.addTransferAttribute(entity);
        });
    }

    /**
     * 删除设备信息
     *
     * @param transferAttributeId 参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteTransferAttributeById(Long transferAttributeId) {
        return transferAttributeMapper.deleteTransferAttributeById(transferAttributeId);
    }

    /**
     * 批量删除参数信息
     *
     * @param transferAttributeIds 需要删除的参数ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteTransferAttributeByIds(Long[] transferAttributeIds) {
        return transferAttributeMapper.deleteTransferAttributeByIds(transferAttributeIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteTransferAttributeByCode(String sn, String deviceCode) {
        TransferAttributeEntity transferAttribute = new TransferAttributeEntity();
        transferAttribute.setSn(sn);
        transferAttribute.setDeviceCode(deviceCode);

        return transferAttributeMapper.deleteTransferAttributeByCode(transferAttribute);
    }


    /**
     * 更新redis中属性集合
     *
     * @throws Exception
     */
    public void renewAll() {
        List<TransferAttributeEntity> list = selectTransferAttributeList(null);

        list.stream()
                .collect(groupingBy(ta -> ta.getSn() + "_" + ta.getDeviceCode(),
                        mapping(TransferAttributeEntity::getName, toList())))
                .forEach((k, v) -> valueOperations.set("TRANSFER_ATTRIBUTE_" + k, JSONObject.toJSONString(v)));
    }

    /**
     * 查询所有启用的
     *
     * @return
     */
    public List<String> queryAttributesByCode(String sn, String code) {

        String attributesStr = valueOperations.get(getKey(sn, code));
        if (StringUtils.isNotBlank(attributesStr)) {
            return JSONObject.parseArray(attributesStr, String.class);
        }

        return renewAttributes(sn, code);
    }

    /**
     * 更新redis中属性集合
     *
     * @throws Exception
     */
    public List<String> renewAttributes(String sn, String code) {

        List<TransferAttributeEntity> list = selectListBySnAndCode(sn, code);

        List<String> attributes = list.stream().map(TransferAttributeEntity::getName).collect(toList());

        logger.info("更新属性集合成功----{}", JSONObject.toJSONString(attributes));

        valueOperations.set(getKey(sn, code), JSONObject.toJSONString(attributes));

        return attributes;
    }


    private String getKey(String sn, String code) {
        return "TRANSFER_ATTRIBUTE_" + sn + "_" + code;
    }


    /**
     * 同步到船端
     *
     * @param transferAttribute
     */
    public void sync2Shore(TransferAttributeEntity transferAttribute) {
        List<TransferAttributeEntity> sourceList = selectListBySnAndCode(transferAttribute.getSn(), transferAttribute.getDeviceCode());

        List<TransferAttributeEntity> list = sourceList.stream().map(ta -> {
            TransferAttributeEntity entity = new TransferAttributeEntity();
            entity.setName(ta.getName());
            entity.setOrderNum(ta.getOrderNum());
            return entity;
        }).collect(Collectors.toList());

        SyncEntity syncEntity = new SyncEntity();
        syncEntity.setJsonObject(JSONObject.toJSONString(list));
        syncEntity.setAction(2);
        syncEntity.setModule("transferAttribute");

        KafkaMessage kafkaMessage = new KafkaMessage(transferAttribute.getDeviceCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(transferAttribute.getSn());
        syncService.sendSync2Kafka(kafkaMessage);
    }

    /**
     * 同步到船端-new
     *
     * @param
     */
    public void syncNewShore(Transfer2DeviceVo transferDeviceVo, int action, String sn) {
        List<Transfer2SheetaEntity> sourceList = transferDeviceVo.getTransferAttributeEntities();
        if (sourceList != null && sourceList.size()>0) {
            List<Transfer2SheetaEntity> list = sourceList.stream().map(ta -> {
                Transfer2SheetaEntity entity = new Transfer2SheetaEntity();
                entity.setName(ta.getName());
                entity.setOrderNum(ta.getOrderNum());
                return entity;
            }).collect(Collectors.toList());
        }
        SyncEntity syncEntity = new SyncEntity();
        syncEntity.setJsonObject(JSONObject.toJSONString(transferDeviceVo));
        syncEntity.setAction(action);
        syncEntity.setModule("transferAdevice");

        KafkaMessage kafkaMessage = new KafkaMessage(transferDeviceVo.getDeviceStatus().getCode(), JSONObject.toJSONString(syncEntity), 10, System.currentTimeMillis());
        kafkaMessage.setSn(sn);
        syncService.sendSync2Kafka(kafkaMessage);
    }
}
