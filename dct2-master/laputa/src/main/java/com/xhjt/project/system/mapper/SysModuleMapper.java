package com.xhjt.project.system.mapper;

import com.xhjt.project.system.domain.SysModule;
import io.swagger.models.auth.In;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface SysModuleMapper {

    SysModule checkModuleLabelUnique(SysModule module);

    List<HashMap> getModuleShow(SysModule sysModule);

    List<SysModule> selectModuleListByType(SysModule type);

    List<SysModule> selectModuleListByEdit(SysModule sysModule);

    List<SysModule> getAddForm(int type);

    int insertModule(SysModule module);

    int deleteModuleById(SysModule sysModule);

    int updateModule(SysModule module);

    SysModule checkModulePropertyUnique(SysModule module);

    int deleteModuleByType(SysModule module);

    Map getModuleShowByParam(SysModule sysModule);

    List<SysModule> selectModuleListByComIds(SysModule sysModule);

    SysModule selectModuleListByProperty(SysModule sysModule);
}
