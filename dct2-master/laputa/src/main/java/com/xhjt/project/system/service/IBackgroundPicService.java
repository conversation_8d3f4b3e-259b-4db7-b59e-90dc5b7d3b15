package com.xhjt.project.system.service;

import com.xhjt.project.system.domain.SysBackgroundPic;

import java.util.List;

/**
 * @program: laputa
 * @description: 图片管理 服务层
 * @author: Mrs.<PERSON>
 * @create: 2021-12-17 16:05
 **/

public interface IBackgroundPicService {
    /**
     * 查询图片
     * @param Id 图片ID
     * @return  图片信息
     */
    public SysBackgroundPic selectById(Long Id);

    /**
     * 查询图片列表
     * @param sysBackgroundPic 图片信息
     * @return 图片集合
     */
    public List<SysBackgroundPic> selectBackPicList(SysBackgroundPic sysBackgroundPic);

    /**
     * 查询图片列表
     * @param sysBackgroundPic 图片信息
     * @return 图片集合
     */
    public List<SysBackgroundPic> selectBackPicListView(SysBackgroundPic sysBackgroundPic);

    /**
     * 添加图片
     * @param sysBackgroundPic
     * @return 结果
     */
    public int insertBackgroundPic(SysBackgroundPic sysBackgroundPic);

    /**
     * 修改图片
     * @param sysBackgroundPic
     * @return
     */
    public int updateBackgroudPic(SysBackgroundPic sysBackgroundPic);

    /**
     * 批量删除图片
     * @param Id
     * @return
     */
    public int deteleBackgroundPicId(Long Id);

    /**
     * 批量删除图片信息
     * @param Ids
     * @return
     */
    public int deteleBackgroundPicIds(Long[] Ids);



}
