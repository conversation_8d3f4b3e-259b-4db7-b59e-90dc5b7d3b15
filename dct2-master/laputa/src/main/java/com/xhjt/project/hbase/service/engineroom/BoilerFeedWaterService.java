package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.BoilerFeedWater;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.BoilerFeedWaterVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;

/**
 * <AUTHOR>
 * BoilerFeedWater 操作类
 */
@Service
@Transactional(readOnly = true)
public class BoilerFeedWaterService {

    private Logger logger = LoggerFactory.getLogger(BoilerFeedWaterService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 保存boilerFeedWater
     *
     * @param boilerFeedWater
     */
    @Transactional(rollbackFor = Exception.class)
    public BoilerFeedWaterVo save(String sn, BoilerFeedWater boilerFeedWater, Long timeStamp) {

        BoilerFeedWaterVo boilerFeedWaterVo = new BoilerFeedWaterVo();

        Calendar calender = Calendar.getInstance();
        calender.setTimeInMillis(timeStamp);
        boilerFeedWaterVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        boilerFeedWaterVo.setMfPressure(boilerFeedWater.getMfPressure());
        boilerFeedWaterVo.setMfrTemp(boilerFeedWater.getMfrTemp());
        boilerFeedWaterVo.setOutletTemp(boilerFeedWater.getOutletTemp());
        boilerFeedWaterVo.setFrTemp(boilerFeedWater.getFrTemp());

        logger.info(" boilerFeedWater开始保存到hbase，---{}", JSONObject.toJSONString(boilerFeedWaterVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "boiler_feedWater"), boilerFeedWaterVo);

        return boilerFeedWaterVo;
    }
}
