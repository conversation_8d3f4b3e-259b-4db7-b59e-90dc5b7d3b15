package com.xhjt.project.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.constant.UserConstants;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.system.domain.SysModule;
import com.xhjt.project.system.mapper.SysModuleMapper;
import com.xhjt.project.system.service.ISysModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class SysModuleServiceImpl implements ISysModuleService {
    @Autowired
    private SysModuleMapper moduleMapper;

    @Override
    @DataScope(deptAlias = "d")
    public List<HashMap> getModuleShow(SysModule sysModule){
        return moduleMapper.getModuleShow(sysModule);
    }

    @Override
    public int insertModule(SysModule moduleInput) {
        List<SysModule> moduleList = moduleInput.getModuleList();
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        String createBy = SecurityUtils.getUsername();
        int type = moduleInput.getType();
        Long comId = moduleInput.getComId();
        for (SysModule sysModule : moduleList) {
            sysModule.setDeptId(deptId);
            sysModule.setCreateBy(createBy);
            sysModule.setType(type);
            sysModule.setComId(comId);
            moduleMapper.insertModule(sysModule);
        }
        return 1;
    }

    @Override
    public String checkModuleLabelUnique(SysModule module) {
        Long moduleId = StringUtils.isNull(module.getId()) ? -1L : module.getId();
        SysModule info = moduleMapper.checkModuleLabelUnique(module);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != moduleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public List<SysModule> selectModuleListByType(SysModule module) {
        return moduleMapper.selectModuleListByType(module);
    }

    @Override
    public int deleteModuleById(SysModule sysModule) {
        return moduleMapper.deleteModuleById(sysModule);
    }

    @Override
    public int updateModule(SysModule moduleInput) {
        int type = moduleInput.getType();
        Long comId = moduleInput.getComId();
        List<SysModule> moduleList = moduleInput.getModuleList();
        String userName = SecurityUtils.getUsername();
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        for (SysModule sysModule : moduleList) {
            sysModule.setDeptId(deptId);
            Date date = new Date();
            sysModule.setType(type);
            sysModule.setComId(comId);
            if(sysModule.getId() != null) {//如果含有ID 就是修改
                sysModule.setUpdateTime(date);
                sysModule.setUpdateBy(userName);
                moduleMapper.updateModule(sysModule);
            }
            else{//新增
                sysModule.setCreateTime(date);
                sysModule.setCreateBy(userName);
                moduleMapper.insertModule(sysModule);
            }
        }
        return 1;
    }

    @Override
    public List<SysModule> getAddForm(int type) {
        return moduleMapper.getAddForm(type);
    }

    @Override
    public String checkModulePropertyUnique(SysModule module) {
        Long moduleId = StringUtils.isNull(module.getId()) ? -1L : module.getId();
        SysModule info = moduleMapper.checkModulePropertyUnique(module);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != moduleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public int deleteModuleByType(SysModule module) {
        return moduleMapper.deleteModuleByType(module);
    }

    @Override
    public Map getModuleShowByParam(SysModule sysModule) {
        return moduleMapper.getModuleShowByParam(sysModule);
    }

    @Override
    public List<SysModule> selectModuleListByComIds(SysModule sysModule) {
        return moduleMapper.selectModuleListByComIds(sysModule);
    }

    @Override
    public List<SysModule> selectModuleListByEdit(SysModule sysModule) {
        return moduleMapper.selectModuleListByEdit(sysModule);
    }

    @Override
    public SysModule selectModuleListByProperty(SysModule sysModule) {
        return moduleMapper.selectModuleListByProperty(sysModule);
    }
}
