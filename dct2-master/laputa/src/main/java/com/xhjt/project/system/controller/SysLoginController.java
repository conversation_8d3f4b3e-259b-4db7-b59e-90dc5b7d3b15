package com.xhjt.project.system.controller;

import com.xhjt.common.constant.Constants;
import com.xhjt.common.utils.ServletUtils;
import com.xhjt.framework.security.LoginBody;
import com.xhjt.framework.security.LoginUser;
import com.xhjt.framework.security.service.SysLoginService;
import com.xhjt.framework.security.service.SysPermissionService;
import com.xhjt.framework.security.service.TokenService;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.system.domain.SysBackgroundPic;
import com.xhjt.project.system.domain.SysMenu;
import com.xhjt.project.system.domain.SysUser;
import com.xhjt.project.system.service.IBackgroundPicService;
import com.xhjt.project.system.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IBackgroundPicService backgroundPicService;

    /**
     * 登录方法
     *
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 用户信息
        SysUser user = loginUser.getUser();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 获取背景图片列表
     * @return
     */
    @GetMapping("getPicList")
    public AjaxResult getPicList(){
        SysBackgroundPic sysBackgroundPic = new SysBackgroundPic();
        sysBackgroundPic.setBgStatus(1);//过滤启用的数据
      /*  sysBackgroundPic.setType(0);//过滤背景图片*/
        List<SysBackgroundPic> sysBackgroundPics = backgroundPicService.selectBackPicListView(sysBackgroundPic);
        return AjaxResult.success(sysBackgroundPics);
    }
}
