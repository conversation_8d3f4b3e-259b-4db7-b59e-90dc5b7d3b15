package com.xhjt.project.ship.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.project.ship.domain.ShipEntity;

import java.util.List;

/**
 * 船只管理 服务层
 *
 * <AUTHOR>
 */
public interface ShipService {
    /**
     * 根据条件判断
     */
    List<ShipEntity> selectShipByFactor(ShipEntity ship);

    /**
     * 根据shipId查询船只信息
     */
    ShipEntity selectNewShipById(Long shipId);

    /**
     * 根据sn查询船只信息
     */
    ShipEntity selectShipBySn(String sn);

    /**
     * 根据船只名称查询船只信息
     */
    List<ShipEntity> selectShipByName(ShipEntity ship1);

    /**
     * 查询船只列表
     */
    List<ShipEntity> selectNewShipList(ShipEntity ship);

    /**
     * 查询船只列表
     */
    List<ShipEntity> selectAllNewShip(ShipEntity ship);

    /**
     * 新增船只
     */
    int addShip(JSONObject ship);

    /**
     * 修改船只
     */
    int updateNewShip(ShipEntity ship);

    /**
     * 启用
     */
    ShipEntity enable(Long id);

    /**
     * 停用
     */
    ShipEntity disable(Long id);

    void renewShip4Redis();

    int deleteNewShipById(Long shipId);
}
