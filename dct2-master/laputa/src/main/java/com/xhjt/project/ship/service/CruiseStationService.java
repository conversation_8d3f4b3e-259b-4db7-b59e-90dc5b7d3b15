package com.xhjt.project.ship.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.domain.CruiseStationEntity;

import java.util.List;

/**
 * 航次站位管理 服务层
 *
 * <AUTHOR>
 */
public interface CruiseStationService {
    /**
     * 查询列表
     */
    List<CruiseStationEntity> selectNewCruiseStationList(CruiseStationEntity cruiseStation);

    CruiseStationEntity selectNewCruiseStationById(Long id);

    CruiseStationEntity selectNewCruiseStationByName(CruiseStationEntity cruiseStation);

    /**
     * 新增航次站位
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    int addNewCruise(JSONObject cruiseStation);

    /**
     * 修改航次站位
     */
    int updateNewCruiseStation(CruiseStationEntity cruiseStation);

    /**
     * 删除航次站位信息
     *
     */
    int deleteNewCruiseStationById(Long id);

}
