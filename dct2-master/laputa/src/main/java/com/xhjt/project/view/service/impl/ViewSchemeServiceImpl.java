package com.xhjt.project.view.service.impl;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.view.domain.ModuleConfigEntity;
import com.xhjt.project.view.domain.ViewSchemeEntity;
import com.xhjt.project.view.mapper.ModuleConfigMapper;
import com.xhjt.project.view.mapper.ViewSchemeMapper;
import com.xhjt.project.view.service.ViewSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 视图方案管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ViewSchemeServiceImpl implements ViewSchemeService {
    @Autowired
    private ViewSchemeMapper viewSchemeMapper;
    @Autowired
    private ModuleConfigMapper moduleConfigMapper;

    /**
     * 查询视图方案信息
     *
     * @param viewSchemeId 视图方案ID
     * @return 视图方案信息
     */
    @Override
    public ViewSchemeEntity selectViewSchemeById(Long viewSchemeId) {
        ViewSchemeEntity viewScheme = new ViewSchemeEntity();
        viewScheme.setId(viewSchemeId);
        ViewSchemeEntity viewSchemeEntity = viewSchemeMapper.selectViewScheme(viewScheme);

        ModuleConfigEntity moduleConfigEntity = new ModuleConfigEntity();
        moduleConfigEntity.setSchemeId(viewSchemeId);
        List<ModuleConfigEntity> moduleConfigList = moduleConfigMapper.selectModuleConfigList(moduleConfigEntity);
        viewSchemeEntity.setModuleConfigList(moduleConfigList);

        return viewSchemeEntity;
    }

    /**
     * 查询视图方案列表
     *
     * @param viewScheme 视图方案信息
     * @return 视图方案集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<ViewSchemeEntity> selectViewSchemeList(ViewSchemeEntity viewScheme) {
        return viewSchemeMapper.selectViewSchemeList(viewScheme);
    }

    /**
     * 新增视图方案
     *
     * @param viewScheme 视图方案信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addViewScheme(ViewSchemeEntity viewScheme) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
            viewScheme.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }

        //查看相同页面类型是否已经有方案了,第一个方案默认启用
        ViewSchemeEntity queryEntity = new ViewSchemeEntity();
        queryEntity.setSn(viewScheme.getSn());
        queryEntity.setPageType(viewScheme.getPageType());
        List<ViewSchemeEntity> list = selectViewSchemeList(queryEntity);
        if (list.size() > 0) {
            viewScheme.setStatus(0);
        } else {
            viewScheme.setStatus(1);
        }

        int result = viewSchemeMapper.addViewScheme(viewScheme);

        for (ModuleConfigEntity moduleConfigEntity : viewScheme.getModuleConfigList()) {
            moduleConfigEntity.setSchemeId(viewScheme.getId());
            moduleConfigEntity.setDeptId(viewScheme.getDeptId());
            moduleConfigEntity.setCreateBy(viewScheme.getCreateBy());
            result = moduleConfigMapper.addModuleConfig(moduleConfigEntity);
        }

        return result;
    }

    /**
     * 修改视图方案
     *
     * @param viewScheme 视图方案信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateViewScheme(ViewSchemeEntity viewScheme) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
            viewScheme.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        int result = viewSchemeMapper.updateViewScheme(viewScheme);

        for (ModuleConfigEntity moduleConfigEntity : viewScheme.getModuleConfigList()) {
            if (moduleConfigEntity.getId() == null) {
                moduleConfigEntity.setSchemeId(viewScheme.getId());
                moduleConfigEntity.setDeptId(viewScheme.getDeptId());
                moduleConfigEntity.setCreateBy(viewScheme.getCreateBy());
                result = moduleConfigMapper.addModuleConfig(moduleConfigEntity);
            } else {
                moduleConfigEntity.setUpdateBy(viewScheme.getUpdateBy());
                result = moduleConfigMapper.updateModuleConfig(moduleConfigEntity);
            }
        }

        return result;
    }

    /**
     * 启用
     *
     * @param id 设备信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int enable(Long id) {
        ViewSchemeEntity viewSchemeEntity = selectViewSchemeById(id);
        if (viewSchemeEntity == null) {
            return 0;
        }
        if (viewSchemeEntity.getStatus() == 1) {
            return 1;
        }

        //查看相同页面类型是否已经有方案了,一个页面只能启动一种方案
        ViewSchemeEntity queryEntity = new ViewSchemeEntity();
        queryEntity.setSn(viewSchemeEntity.getSn());
        queryEntity.setPageType(viewSchemeEntity.getPageType());
        queryEntity.setStatus(1);
        List<ViewSchemeEntity> list = selectViewSchemeList(queryEntity);
        if (list.size() > 0) {
            return 0;
        }

        viewSchemeEntity.setStatus(1);
        return updateViewScheme(viewSchemeEntity);
    }

    /**
     * 修改设备
     *
     * @param id
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int disable(Long id) {
        ViewSchemeEntity viewSchemeEntity = selectViewSchemeById(id);
        if (viewSchemeEntity == null) {
            return 0;
        }
        if (viewSchemeEntity.getStatus() == 0) {
            return 1;
        }
        viewSchemeEntity.setStatus(0);
        return updateViewScheme(viewSchemeEntity);
    }

    /**
     * 删除视图方案信息
     *
     * @param viewSchemeId 参数ID
     * @return 结果
     */
    @Override
    public int deleteViewSchemeById(Long viewSchemeId) {
        return viewSchemeMapper.deleteViewSchemeById(viewSchemeId);
    }

    /**
     * 批量删除参数信息
     *
     * @param viewSchemeIds 需要删除的参数ID
     * @return 结果
     */
    @Override
    public int deleteViewSchemeByIds(Long[] viewSchemeIds) {
        return viewSchemeMapper.deleteViewSchemeByIds(viewSchemeIds);
    }

}
