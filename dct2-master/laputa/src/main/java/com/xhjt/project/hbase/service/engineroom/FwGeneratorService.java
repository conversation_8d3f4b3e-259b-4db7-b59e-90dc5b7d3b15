package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.FwGenerator;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.FwGeneratorVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * BallastWater 操作类
 */
@Service
@Transactional(readOnly = true)
public class FwGeneratorService {

    private Logger logger = LoggerFactory.getLogger(FwGeneratorService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 保存fwGenerator
     *
     * @param fwGenerator
     */
    @Transactional(rollbackFor = Exception.class)
    public FwGeneratorVo save(String sn, FwGenerator fwGenerator, Long timeStamp) {

        FwGeneratorVo fwGeneratorVo = new FwGeneratorVo();

        fwGeneratorVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        fwGeneratorVo.setSalinity1(fwGenerator.getSalinity1());
        fwGeneratorVo.setSalinity2(fwGenerator.getSalinity2());

        logger.info("fwGenerator开始保存到hbase，---{}", JSONObject.toJSONString(fwGeneratorVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "fw_generator"), fwGeneratorVo);

        return fwGeneratorVo;
    }
}
