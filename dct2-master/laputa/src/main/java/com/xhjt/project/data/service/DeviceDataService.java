package com.xhjt.project.data.service;

import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceAttributeService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.TransferAttributeService;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备数据信息 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class DeviceDataService {

    protected Logger logger = LoggerFactory.getLogger(DeviceDataService.class);

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private DeviceAttributeService deviceAttributeService;

    public List<TableHead> getTableHead(Long deviceId) {
        DeviceEntity device = deviceService.selectDeviceById(deviceId);

        List<TransferAttributeEntity> attributeList = transferAttributeService.selectListBySnAndCode(device.getSn(), device.getCode());

        List<TableHead> list = Lists.newArrayList();
        addBjTime(list);

        for (TransferAttributeEntity entity : attributeList) {
            TableHead tableHead = new TableHead();
            tableHead.setLabel(entity.getLabel());
            tableHead.setProperty(entity.getName());

            list.add(tableHead);
        }

        return list;
    }

    /**
     * 根据type获取表头集合
     * @param type
     * @return
     */
    public List<TableHead> getTableHeadByType(Integer type) {
        List<DeviceAttributeEntity> attributeList = deviceAttributeService.selectListByType(type);
        List<TableHead> list = Lists.newArrayList();
        addBjTime(list);
        for (DeviceAttributeEntity entity : attributeList) {
            TableHead tableHead = new TableHead();
            tableHead.setLabel(entity.getLabel());
            tableHead.setProperty(entity.getName());
            list.add(tableHead);
        }
        return list;
    }

    private void addBjTime(List<TableHead> list){
        TableHead tableHead = new TableHead();
        tableHead.setLabel("录入时间");
        tableHead.setProperty("initialBjTime");

        list.add(tableHead);
    }

    public class TableHead {
        /**
         * 文本
         */
        private String label;
        /**
         * 属性
         */
        private String property;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getProperty() {
            return property;
        }

        public void setProperty(String property) {
            this.property = property;
        }
    }
}
