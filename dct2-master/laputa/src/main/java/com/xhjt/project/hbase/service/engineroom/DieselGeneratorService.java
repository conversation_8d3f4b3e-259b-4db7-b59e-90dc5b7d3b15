package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.DieselGenerator;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.DieselGeneratorVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * DieselGenerator 操作类
 */
@Service
@Transactional(readOnly = true)
public class DieselGeneratorService {

    private Logger logger = LoggerFactory.getLogger(DieselGeneratorService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 保存dieselGenerator
     *
     * @param dieselGenerator
     * @param timeStamp
     */
    @Transactional(rollbackFor = Exception.class)
    public DieselGeneratorVo save(String sn, DieselGenerator dieselGenerator, Long timeStamp, String tableName) {

        DieselGeneratorVo dieselGeneratorVo = new DieselGeneratorVo();

        dieselGeneratorVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        dieselGeneratorVo.setSpeed(dieselGenerator.getSpeed());
        dieselGeneratorVo.setLoad(dieselGenerator.getLoad());
        dieselGeneratorVo.setMegTempL(dieselGenerator.getMegTempL());
        dieselGeneratorVo.setMegTempR(dieselGenerator.getMegTempR());
        dieselGeneratorVo.setCwoTemp(dieselGenerator.getCwoTemp());
        dieselGeneratorVo.setActcs(dieselGenerator.getActcs());
        dieselGeneratorVo.setCaths(dieselGenerator.getCaths());
        dieselGeneratorVo.setAirPressure(dieselGenerator.getAirPressure());
        dieselGeneratorVo.setFoiPress(dieselGenerator.getFoiPress());
        dieselGeneratorVo.setFuelRate(dieselGenerator.getFuelRate());
        dieselGeneratorVo.setBv(dieselGenerator.getBv());
        dieselGeneratorVo.setInletPress(dieselGenerator.getInletPress());
        dieselGeneratorVo.setDiffPressure(dieselGenerator.getDiffPressure());
        dieselGeneratorVo.setSwiPress(dieselGenerator.getSwiPress());
        dieselGeneratorVo.setJcwip(dieselGenerator.getJcwip());
        dieselGeneratorVo.setJcwot(dieselGenerator.getJcwot());
        dieselGeneratorVo.setAcTemp(dieselGenerator.getAcTemp());
        dieselGeneratorVo.setBtde(dieselGenerator.getBtde());
        dieselGeneratorVo.setBtnde(dieselGenerator.getBtnde());
        dieselGeneratorVo.setTempU(dieselGenerator.getTempU());
        dieselGeneratorVo.setTempV(dieselGenerator.getTempV());
        dieselGeneratorVo.setTempW(dieselGenerator.getTempW());

        dieselGeneratorVo.setEgTemp1(dieselGenerator.getEgTemp1());
        dieselGeneratorVo.setEgTemp2(dieselGenerator.getEgTemp2());
        dieselGeneratorVo.setEgTemp3(dieselGenerator.getEgTemp3());
        dieselGeneratorVo.setEgTemp4(dieselGenerator.getEgTemp4());
        dieselGeneratorVo.setEgTemp5(dieselGenerator.getEgTemp5());
        dieselGeneratorVo.setEgTemp6(dieselGenerator.getEgTemp6());
        dieselGeneratorVo.setEgTemp7(dieselGenerator.getEgTemp7());
        dieselGeneratorVo.setEgTemp8(dieselGenerator.getEgTemp8());
        dieselGeneratorVo.setEgTemp9(dieselGenerator.getEgTemp9());
        dieselGeneratorVo.setEgTemp10(dieselGenerator.getEgTemp10());
        dieselGeneratorVo.setEgTemp11(dieselGenerator.getEgTemp11());
        dieselGeneratorVo.setEgTemp12(dieselGenerator.getEgTemp12());

        logger.info(" dieselGenerator开始保存到hbase，---{}", JSONObject.toJSONString(dieselGeneratorVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, tableName), dieselGeneratorVo);

        return dieselGeneratorVo;
    }
}
