package com.xhjt.project.ship.mapper;

import com.xhjt.project.ship.domain.ShipEntity;

import java.util.List;

/**
 * 船只管理 数据层
 *
 * <AUTHOR>
 */
public interface ShipMapper {

    /**
     * 根据条件判断
     */
    List<ShipEntity> selectNewShipByFactor(ShipEntity ship);


    /**
     * 查询船只信息
     *
     * @param shipEntity 船只信息
     * @return 船只信息
     */
    ShipEntity selectNewShip(ShipEntity shipEntity);

    /**
     * 查询船只列表
     *
     * @param ship 船只信息
     * @return 船只集合
     */
    List<ShipEntity> selectNewShipList(ShipEntity ship);
    /**
     * 新增船只
     *
     * @param ship 船只信息
     * @return 结果
     */
    int addNewShip(ShipEntity ship);

    /**
     * 修改船只
     *
     * @param ship 船只信息
     * @return 结果
     */
    int updateNewShip(ShipEntity ship);

    /**
     * 删除船只
     *
     * @param shipId 参数ID
     * @return 结果
     */
    int deleteNewShipById(Long shipId);

    List<ShipEntity> selectShipByName(ShipEntity ship1);

    ShipEntity selectNewShipByShipName(String  shipName);

    List<ShipEntity> selectAllNewShip(ShipEntity ship);
}
