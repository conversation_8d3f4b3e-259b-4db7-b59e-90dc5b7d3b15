package com.xhjt.project.view.domain;


import com.xhjt.dctcore.commoncore.domain.BaseEntity;

import java.util.List;

/**
 * 视图方案信息
 *
 * <AUTHOR>
 */
public class ViewSchemeEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 船只sn
     */
    private String sn;

    /**
     * 模版
     */
    private String templateCode;

    /**
     * 页面类型
     */
    private String pageType;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 站点标题
     */
    private String siteTitle;

    /**
     * 版权信息
     */
    private String copyright;

    /**
     * 模块配置
     */
    private List<ModuleConfigEntity> moduleConfigList;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSiteTitle() {
        return siteTitle;
    }

    public void setSiteTitle(String siteTitle) {
        this.siteTitle = siteTitle;
    }

    public String getCopyright() {
        return copyright;
    }

    public void setCopyright(String copyright) {
        this.copyright = copyright;
    }

    public List<ModuleConfigEntity> getModuleConfigList() {
        return moduleConfigList;
    }

    public void setModuleConfigList(List<ModuleConfigEntity> moduleConfigList) {
        this.moduleConfigList = moduleConfigList;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }
}
