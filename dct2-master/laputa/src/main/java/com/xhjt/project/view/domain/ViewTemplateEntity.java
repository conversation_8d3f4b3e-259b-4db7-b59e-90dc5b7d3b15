package com.xhjt.project.view.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;

import java.util.List;

/**
 * 模版
 *
 * <AUTHOR>
 */
public class ViewTemplateEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 模版名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 页面类型
     */
    private String pageType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 模块
     */
    private String moduleJson;

    private List<ModuleVo> moduleList;


    public static class ModuleVo {
        private String code;
        private String label;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getModuleJson() {
        return moduleJson;
    }

    public void setModuleJson(String moduleJson) {
        this.moduleJson = moduleJson;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<ModuleVo> getModuleList() {
        return moduleList;
    }

    public void setModuleList(List<ModuleVo> moduleList) {
        this.moduleList = moduleList;
    }
}
