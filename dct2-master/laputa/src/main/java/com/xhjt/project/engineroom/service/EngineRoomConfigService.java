package com.xhjt.project.engineroom.service;

import com.xhjt.common.utils.ListUtil;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.engineroom.domain.EngineRoomConfigEntity;
import com.xhjt.project.engineroom.mapper.EngineRoomConfigMapper;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 船只管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class EngineRoomConfigService {
    private Logger logger = LoggerFactory.getLogger(EngineRoomConfigService.class);
    @Autowired
    private EngineRoomConfigMapper engineRoomConfigMapper;

    @Autowired
    private RedisTemplate redisTemplate;


    @Transactional(rollbackFor = Exception.class)
    public void renewConfig2Db(String sn, String str) throws Exception {

        List<EngineRoomConfigEntity> newList = convertConfig(sn, str);
        if (newList.size() == 0) {
            return;
        }

        EngineRoomConfigEntity queryEntity = new EngineRoomConfigEntity();
        queryEntity.setSn(sn);
        List<EngineRoomConfigEntity> list = selectList(queryEntity);
        List<String> codeStrList = ListUtil.fetchFieldValueList(list, "codeStr");

        for (EngineRoomConfigEntity entity : newList) {
            if (codeStrList.contains(entity.getCodeStr())) {
                continue;
            }

            add(entity);
        }
    }

    /**
     * 转换字符串
     *
     * @param configListStr
     * @return
     */
    private List<EngineRoomConfigEntity> convertConfig(String sn, String configListStr) {

        List<EngineRoomConfigEntity> list = Lists.newArrayList();

        if (StringUtils.isBlank(configListStr) || !configListStr.contains("|")) {
            return list;
        }

        String[] configListArr = configListStr.split("\\|");

        EngineRoomConfigEntity engineRoomConfigEntity;
        for (String configStr : configListArr) {
            if (StringUtils.isBlank(configStr) || !configStr.contains(",")) {
                continue;
            }
            String[] configArr = configStr.split(",");
            if (configArr.length != 4) {
                continue;
            }
            engineRoomConfigEntity = new EngineRoomConfigEntity();
            engineRoomConfigEntity.setSn(sn);
            engineRoomConfigEntity.setCode(Integer.valueOf(configArr[0]));
            engineRoomConfigEntity.setCodeStr(configArr[1]);
            engineRoomConfigEntity.setName(configArr[2]);
            engineRoomConfigEntity.setSymbol(configArr[3]);

            list.add(engineRoomConfigEntity);
        }

        return list;
    }

    public void renewConfig2Redis(String sn) {
        Map<Integer, EngineRoomConfigEntity> map = getConfigMap(sn, 0);

        ValueOperations<String, Map<Integer, EngineRoomConfigEntity>> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(RedisParameter.ENGINE_ROOM_CONFIG + sn, map);
    }

    /**
     * 获取结构Map
     *
     * @return
     * @Param type 类型，0：直接从DB获取，1：先从redis获取
     */
    public Map<Integer, EngineRoomConfigEntity> getConfigMap(String sn, int type) {
        Map<Integer, EngineRoomConfigEntity> map = new HashMap<>();
        ValueOperations<String, Map<Integer, EngineRoomConfigEntity>> valueOperations = redisTemplate.opsForValue();
        if (type == 1) {
            map = valueOperations.get(RedisParameter.ENGINE_ROOM_CONFIG + sn);
        }

        if (CollectionUtils.isEmpty(map)) {
            map = new HashMap<>();
            EngineRoomConfigEntity queryEntity = new EngineRoomConfigEntity();
            queryEntity.setSn(sn);
            List<EngineRoomConfigEntity> list = selectList(queryEntity);

            for (EngineRoomConfigEntity entity : list) {
                if (entity == null) {
                    continue;
                }
                map.put(entity.getCode(), entity);
            }

            valueOperations.set(RedisParameter.ENGINE_ROOM_CONFIG + sn, map);
        }

        return map;
    }

    /**
     * 查询
     *
     * @return
     */
    public EngineRoomConfigEntity selectById(Long id) {
        EngineRoomConfigEntity engineRoomConfigEntity = new EngineRoomConfigEntity();
        engineRoomConfigEntity.setId(id);
        return engineRoomConfigMapper.selectConfig(engineRoomConfigEntity);
    }

    /**
     * 查询列表
     */
    public List<EngineRoomConfigEntity> selectList(EngineRoomConfigEntity engineRoomConfigEntity) {
        return engineRoomConfigMapper.selectList(engineRoomConfigEntity);
    }

    /**
     * 新增
     */
    public int add(EngineRoomConfigEntity engineRoomConfigEntity) {
        return engineRoomConfigMapper.add(engineRoomConfigEntity);
    }

    /**
     * 修改
     */
    public int update(EngineRoomConfigEntity engineRoomConfigEntity) {
        return engineRoomConfigMapper.update(engineRoomConfigEntity);
    }

    /**
     * 删除
     */
    public int deleteById(Long id) {
        return engineRoomConfigMapper.deleteById(id);
    }

    /**
     * 批量删除参数信息
     *
     * @return 结果
     */
    public int deleteByIds(Long[] ids) {
        return engineRoomConfigMapper.deleteByIds(ids);
    }

}
