package com.xhjt.project.snapshot.service;

import com.xhjt.common.utils.ZipMultiFileUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.framework.config.ProjectConfig;
import com.xhjt.project.common.WebSocketServer;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.snapshot.domain.SnapshotVideoEntity;
import com.xhjt.project.snapshot.mapper.SnapshotLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 快照截图记录 实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotLogService {
    public final static Logger logger = LoggerFactory.getLogger(SnapshotLogService.class);
    @Value("${project.snapshotPath}")
    private String snapshotPath;

    @Autowired
    private SnapshotLogMapper snapshotLogMapper;
    @Autowired
    private ShipService shipService;


    @Transactional(rollbackFor = Exception.class)
    @DataScope(deptAlias = "d")
    public List<SnapshotLogEntity> selectSnapshotLogList(SnapshotLogEntity snapshotLog) {
        List<SnapshotLogEntity> listOld = snapshotLogMapper.selectSnapshotLogList(snapshotLog);
        return listOld;
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotLogEntity selectSnapshotLogById(Long id) {
        SnapshotLogEntity snapshotLog = new SnapshotLogEntity();
        snapshotLog.setId(id);
        return snapshotLogMapper.selectSnapshotLog(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotLogEntity> selectSnapshotLogByChannel(SnapshotLogEntity snapshotLog) {
         return snapshotLogMapper.selectSnapshotLogList(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public int addSnapshotLog(SnapshotLogEntity snapshotLog) {
        return snapshotLogMapper.addSnapshotLog(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteSnapshotLogById(Long id) {
        return snapshotLogMapper.deleteSnapshotLogById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int addLogByTransfer(TransferPackage transferPackage) {
        SnapshotLogEntity snapshotLog = new SnapshotLogEntity();

        ShipEntity shipEntity = shipService.selectShipBySn(transferPackage.getSn());
        snapshotLog.setDeptId(shipEntity.getDeptId());

        snapshotLog.setSn(transferPackage.getSn());
        snapshotLog.setChannelCode(transferPackage.getDeviceCode());
        snapshotLog.setOperateTime(transferPackage.getTime());
        snapshotLog.setFileName(transferPackage.getTime() + ".jpg");
        snapshotLog.setDirectory(getFinalPath(transferPackage));
         //这边加入快照新增-修改预览websocket开关
        logger.info("快照实时预览回来1--{}--{}",snapshotLog,transferPackage.getDeviceCode());
        if (transferPackage.getDeviceCode().trim().equalsIgnoreCase("XHJT")){
            logger.info("快照实时预览回来2--{}--{}",snapshotLog,transferPackage.getDeviceCode());
            String jsonResult = snapshotLog.getDirectory()+"/"+snapshotLog.getFileName();
                 //得到数据传输到前端进行展示
            WebSocketServer.sendRealTimeData(jsonResult, transferPackage.getDeviceCode(),transferPackage.getSn());
            logger.info("快照实时预览回来3--{}--{}",jsonResult,transferPackage.getDeviceCode());
        }
        return addSnapshotLog(snapshotLog);
    }

    /**
     * 获取图片的保持文件目录
     * @param transferPackage
     * @return
     */
    private String getFinalPath(TransferPackage transferPackage) {
        return snapshotPath
                + transferPackage.getSn() + "/"
                + transferPackage.getDeviceCode() + "/"
                + DateUtils.getDateToString(transferPackage.getTime(), DateUtils.DATE_PATTERN_NONE);
    }

    public List<SnapshotLogEntity> selectSnapshotVideo(SnapshotVideoEntity snapshotVideoEntity) {
        return snapshotLogMapper.selectSnapshotVideo(snapshotVideoEntity);
    }

    /**
     * 根据条件查找到所有图片并压缩输出
     */
    public String zipwordDownAction(List<SnapshotLogEntity> listOld){
        List<SnapshotLogEntity> list= listOld.stream().filter(distinctByKey(SnapshotLogEntity::getFileName)).collect(Collectors.toList());
        String dirPath = ProjectConfig.getProfile();
        File dir = new File(dirPath);
        String filePath = dirPath + "/img" + System.currentTimeMillis() + ".zip";
        File zipFile = new File(filePath);
        try {
            if (!dir.exists()){
                dir.mkdirs();
            }
            if (!zipFile.exists()) {
                zipFile.createNewFile();
            }else {
                zipFile.delete();
                zipFile.createNewFile();
            }

        } catch (Exception e) {
            logger.error("写入文件失败");
        }
        List<File> srcFiles = new ArrayList<File>();
        for (int i = 0; i < list.size(); i++) {
            String fileName = list.get(i).getDirectory()+"/"+list.get(i).getFileName();
            srcFiles.add(new File(fileName));
        }


        // 调用压缩方法
      ZipMultiFileUtil.zipFiles(srcFiles, zipFile);
//        try {
//            TimeUnit.SECONDS.sleep(10);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }finally {
            return zipFile.getName();
//        }


        //将项目名称的文件夹 压缩为zip
//        ZipMultiFileUtil.fileToZip(dirPath, dirPath, dirPath + ".zip");
    }

    /**
     * 去重重写方法
     * @param keyExtractor
     * @param <T>
     * @return
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
}
