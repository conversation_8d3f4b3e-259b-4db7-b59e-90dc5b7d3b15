package com.xhjt.project.netty.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.JsonUtil;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.common.WebSocketServer;
import com.xhjt.project.device.domain.AcquisitionMessage;
import com.xhjt.project.device.domain.AcquisitionMiddleware;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.AcquisitionMiddlewareService;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.snapshot.service.SnapshotLogService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * class
 *
 * <AUTHOR>
 */
@Component
public class ConsumerService {
    public final static Logger logger = LoggerFactory.getLogger(ConsumerService.class);

    @Autowired
    private StoreService storeService;
    @Autowired
    private SnapshotLogService snapshotLogService;
    @Resource
    private ValueOperations<String, Object> valueOperations;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private AcquisitionMiddlewareService acquisitionMiddlewareService;
    /**
     * 接收topic消息，并把数据存入redis中
     *
     * @param record
     */
    @KafkaListener(topics = {"DEVICE_DATA_TOPIC"}, groupId = "laputa_v1")
    public void sourceDataConsumer(ConsumerRecord<?, ?> record) {
        Optional<?> sourceMessage = Optional.ofNullable(record.value());
        if (sourceMessage.isPresent()) {
            Object message = sourceMessage.get();
            logger.info("接收设备数据---{}", message.toString());
            TransferPackage transferPackage = JsonUtil.string2Obj(message.toString(), TransferPackage.class);

            //websocket实时预览数据
            if (WebSocketServer.flag) {
                //当websocket开启时才进来调用
                websocketSendCacheData(transferPackage);
            }

            TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
            threadPool.execute(() -> {
                // 保存到hbase
                storeService.handleMessage(transferPackage);
            });
        }
    }

    /**
     * websocket实时预览数据--取redis里面最新一条记录
     * @param transferPackage
     */
    private void websocketSendCacheData(TransferPackage transferPackage) {
        String objectKey = RedisParameter.LATEST_DATA + transferPackage.getSn() + "_" + transferPackage.getDeviceCode();
        Object object = valueOperations.get(objectKey);//最新一条数据
        if (object != null){
            String jsonResult = JSONObject.toJSONString(object);
            //获取对应设备对象用于获取对应得设备编码
            DeviceEntity deviceEntity = deviceService.selectByCodeAndSn(transferPackage.getSn(),transferPackage.getDeviceCode());
            //返回给前端进行显示
            String mac = "";
            if (deviceEntity.getConnectType()==1){
                mac = deviceEntity.getSerialPort()+deviceEntity.getCode();
            }else{
                mac = deviceEntity.getMac()+deviceEntity.getCode();
            }
            WebSocketServer.sendRealTimeData(jsonResult, mac,transferPackage.getSn());
        }
    }

    /**
     * 接收topic消息，并把数据存入redis中
     *
     * @param record
     */
    @KafkaListener(topics = {"SNAPSHOT_LATEST_TOPIC"}, groupId = "laputa_v1")
    public void snapshotConsumer(ConsumerRecord<?, ?> record) {
        Optional<?> sourceMessage = Optional.ofNullable(record.value());
        if (sourceMessage.isPresent()) {
            Object message = sourceMessage.get();
            TransferPackage transferPackage = JsonUtil.string2Obj(message.toString(), TransferPackage.class);

           int row = snapshotLogService.addLogByTransfer(transferPackage);

        }
    }

    /**
     * 接收topic消息，并把原始数据存入redis中
     *
     * @param record
     */
    @KafkaListener(topics = {"RAW_DATA_TOPIC"}, groupId = "laputa_v1")
    public void rawDataConsumer(ConsumerRecord<?, ?> record) {
        Optional<?> sourceMessage = Optional.ofNullable(record.value());
        if (sourceMessage.isPresent()) {
            Object message = sourceMessage.get();
            logger.info("接收原始数据---{}", message.toString());
            TransferPackage transferPackage = JsonUtil.string2Obj(message.toString(), TransferPackage.class);
            //保存到redis最新一条数据
            TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
            threadPool.execute(() -> {
                // 保存到redis
                AcquisitionMessage acquisitionMessage = JsonUtil.string2Obj(transferPackage.getMessage().toString(), AcquisitionMessage.class);
                logger.info("接收原始数据---{}-----{}", acquisitionMessage.getContent(),acquisitionMessage.getMac());
                storeService.save2Redis(transferPackage.getSn(),acquisitionMessage.getMac(),transferPackage.getTime(),acquisitionMessage.getContent());
            });

            //websocket实时预览原始数据
            if (WebSocketServer.flag) {
                //当websocket开启时才进来调用
                websocketSendRawCacheData(transferPackage);
            }
        }
    }
    /**
     * websocket实时预览数据--取redis里面最新一条记录 原始数据
     * ---后台管理显示做在采集终端列表的预览功能上
     * @param transferPackage
     */
    private void websocketSendRawCacheData(TransferPackage transferPackage) {
        //根据最新的redis数据通过websocket传输到后台管理系统前端进行显示
        AcquisitionMessage acquisitionMessage = JsonUtil.string2Obj(transferPackage.getMessage().toString(), AcquisitionMessage.class);
        String objectKey = RedisParameter.LATEST_DATA + transferPackage.getSn() + "_" + acquisitionMessage.getMac();
        Object object = valueOperations.get(objectKey);//最新一条数据
        if (object != null){
            String jsonResult = object.toString();
            //获取对应设备对象用于获取对应得设备编码
            AcquisitionMiddleware acquisitionMiddleware = acquisitionMiddlewareService.selectAcqMiddlewareBySnAndMac(acquisitionMessage.getMac(),transferPackage.getSn());
            //返回给前端进行显示
            if (acquisitionMiddleware!=null){
                WebSocketServer.sendRealTimeData(jsonResult, acquisitionMessage.getMac(),transferPackage.getSn());
            }else{
                //异常暂时不处理，留待后续根据业务实际情况来操作
            }

        }
    }
}
