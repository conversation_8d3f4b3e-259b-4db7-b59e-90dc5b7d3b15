package com.xhjt.project.snapshot.service.Impl;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.project.snapshot.domain.SnapshotRealEntity;
import com.xhjt.project.snapshot.mapper.SnapshotRealMapper;
import com.xhjt.project.snapshot.service.SnapshotRealService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SnapshotRealServiceImpl implements SnapshotRealService {

    @Autowired
    private SnapshotRealMapper snapshotRealMapper;

    @Override
    public List<SnapshotRealEntity> selectSnapshotRealList(SnapshotRealEntity snapshotRealEntity) {
        return snapshotRealMapper.selectChannelRealList(snapshotRealEntity);
    }

    @Override
    public int addSnapshotReal(SnapshotRealEntity snapshotRealEntity) {
        return snapshotRealMapper.addSnapshotReal(snapshotRealEntity);
    }

    @Override
    public int editSnapshotReal(SnapshotRealEntity snapshotRealEntity) {
        return snapshotRealMapper.editSnapshotReal(snapshotRealEntity);
    }

    @Override
    public SnapshotRealEntity selectNewest(SnapshotRealEntity snapshotRealEntity) {
        return snapshotRealMapper.selectNewest(snapshotRealEntity);
    }

    @Override
    public SnapshotRealEntity selectSnapshotReal(SnapshotRealEntity snapshotRealEntity) {
        return snapshotRealMapper.selectSnapshotReal(snapshotRealEntity);
    }
}
