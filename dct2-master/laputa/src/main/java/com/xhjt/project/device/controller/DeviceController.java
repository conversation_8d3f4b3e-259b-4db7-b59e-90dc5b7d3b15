package com.xhjt.project.device.controller;

import com.xhjt.common.constant.UserConstants;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.device.TransferAttributeEntity;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.data.service.DeviceDataService;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import com.xhjt.project.device.service.TransferAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/device")
public class DeviceController extends BaseController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private DeviceDataService deviceDataService;

    /**
     * 获取设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceEntity device) {
        startPage();
        List<DeviceEntity> list = deviceService.selectDeviceList(device);
        return getDataTable(list);
    }

    /**
     * 获取设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/queryListBySn/{sn}")
    public AjaxResult queryBySn(@PathVariable String sn) {
        return AjaxResult.success(deviceService.queryListBySn(sn));
    }

    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('device:info:export')")
    @GetMapping("/export")
    public AjaxResult export(DeviceEntity device) {
        List<DeviceEntity> list = deviceService.selectDeviceList(device);
        ExcelUtil<DeviceEntity> util = new ExcelUtil<DeviceEntity>(DeviceEntity.class);
        return util.exportExcel(list, "设备数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:query')")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable Long deviceId) {
        return AjaxResult.success(deviceService.selectDeviceById(deviceId));
    }

    /**
     * 新增设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:add')")
    @Log(title = "设备新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DeviceEntity device) {
        if (UserConstants.NOT_UNIQUE.equals(deviceService.checkCodeUnique(device))) {
            return AjaxResult.error("新增设备'" + device.getName() + "'失败，设备编码已存在");
        }
        device.setCreateBy(SecurityUtils.getUsername());
        return toAjax(deviceService.addDevice(device));
    }

    /**
     * 修改设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @Log(title = "设备修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DeviceEntity device) {
        if (UserConstants.NOT_UNIQUE.equals(deviceService.checkCodeUnique(device))) {
            return AjaxResult.error("修改设备'" + device.getName() + "'失败，设备编码已存在");
        }
        device.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(deviceService.updateDevice(device));
    }

    /**
     * 删除设备信息
     */
    @PreAuthorize("@ss.hasPermi('device:info:remove')")
    @Log(title = "设备删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable Long[] deviceIds) {
        return toAjax(deviceService.deleteDeviceByIds(deviceIds));
    }

    /**
     * 启用
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @PostMapping(value = "/enable/{id}")
    public AjaxResult enable(@PathVariable Long id) {
        DeviceEntity deviceEntity = deviceService.selectDeviceById(id);
        List<TransferAttributeEntity> attributeEntities = transferAttributeService.selectListBySnAndCode(deviceEntity.getSn(), deviceEntity.getCode());
        if (attributeEntities.size() == 0) {
            return AjaxResult.error("设备'" + deviceEntity.getName() + "'启用失败，未配置传输属性");
        }
        return AjaxResult.success(deviceService.enable(id));
    }

    /**
     * 禁用
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @PostMapping(value = "/disable/{id}")
    public AjaxResult disable(@PathVariable Long id) {
        return AjaxResult.success(deviceService.disable(id));
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('device:info:edit')")
    @PostMapping("/changeTrStatus/{id}/{status}")
    public AjaxResult changeTrStatus(@PathVariable Long id, @PathVariable Integer status) {
        DeviceEntity device = deviceService.changeTrStatus(id,status);
        if (device!=null){
            return toAjax(1);
        }else {
            return toAjax(0);
        }
    }

    /**
     * 采集终端获取表格表头
     */
    @PreAuthorize("@ss.hasPermi('device:info:list')")
    @GetMapping("/getTypeTableHead/{type}")
    public AjaxResult getTypeTableHead(@PathVariable Integer type) {
        return AjaxResult.success(deviceDataService.getTableHeadByType(type));
    }
}
