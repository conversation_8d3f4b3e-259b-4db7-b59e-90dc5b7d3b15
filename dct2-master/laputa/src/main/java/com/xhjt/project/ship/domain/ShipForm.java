package com.xhjt.project.ship.domain;


import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 船只信息
 *
 * <AUTHOR>
 */
public class ShipForm extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 是否必填
     */
    private int isRequired;

    /**
     * 列名称
     */
    private String label;

    /**
     * 列属性
     */
    private String property;

    /**
     * 值
     */
    private String value;

    public int getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(int isRequired) {
        this.isRequired = isRequired;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
