package com.xhjt.project.device.service.impl;

import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.domain.ShipTerminalManage;
import com.xhjt.project.device.mapper.ShipTerminalManageMapper;
import com.xhjt.project.device.service.ShipTerminalManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/1 15:03
 */
@Service
public class ShipTerminalManageServiceImpl implements ShipTerminalManageService {

    @Autowired
    private ShipTerminalManageMapper shipTerminalManageMapperl;

    @Override
    public List<ShipTerminalManage> selectShipTerminalManageList(ShipTerminalManage shipTerminalManage) {
        return shipTerminalManageMapperl.selectShipTerminalManageList(shipTerminalManage);
    }

    @Override
    public void handleBySync(SyncEntity syncEntity, String sn) {

    }
}
