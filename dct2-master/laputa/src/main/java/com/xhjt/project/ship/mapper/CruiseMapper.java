package com.xhjt.project.ship.mapper;

import com.xhjt.project.ship.domain.CruiseEntity;

import java.util.List;

/**
 * 航次管理 数据层
 *
 * <AUTHOR>
 */
public interface CruiseMapper {

    List<CruiseEntity> selectNewCruiseBySn(String sn);

    List<CruiseEntity> selectNewCruiseList(CruiseEntity cruise);

    CruiseEntity selectCruiseByCruiseName(CruiseEntity cruiseName);

    int addNewCruise(CruiseEntity cruise);

    CruiseEntity selectNewCruiseById(CruiseEntity cruise);

    List<CruiseEntity> selectCruiseNewByFactor(CruiseEntity cruise);

    int updateNewCruise(CruiseEntity cruise);

    int deleteNewCruiseById(CruiseEntity cruise);

    List<CruiseEntity> selectAllNewCruise(CruiseEntity cruise);
}
