package com.xhjt.project.netty.controller;

import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.device.domain.RedisObjVo;
import com.xhjt.project.netty.service.SyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sync")
public class SyncApiController extends BaseController {

    @Autowired
    private SyncService syncService;

    /**
     * @param transferPackage
     */
    @PostMapping("/updateBySync")
    @ResponseBody
    public void updateBySync(@RequestBody TransferPackage transferPackage) {
        syncService.handleMessage(transferPackage);
    }
    /**
     * 获取redis对象
     *
     * @param selectObj
     * @return
     */
    @PostMapping("/getRedisObj")
    @ResponseBody
    public RedisObjVo getRedisObj(@RequestBody RedisObjVo selectObj){
       return syncService.selectRedisObj(selectObj);
    }
}
