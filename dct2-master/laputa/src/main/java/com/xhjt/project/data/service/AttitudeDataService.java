package com.xhjt.project.data.service;

import com.xhjt.common.constant.HttpStatus;
import com.xhjt.dctcore.commoncore.domain.hbase.AttitudeHbaseVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.web.page.TableDataInfo;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * attitude数据信息 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class AttitudeDataService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    public List<AttitudeHbaseVo> selectAttitudeList(Long startTime, Long endTime, String tableName) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new AttitudeHbaseVo(), tableName, rowList);
    }

    public TableDataInfo queryAttitudePage(Long startTime, Long endTime, String tableName, int currentPage, int pageSize, String sort) {
        List<AttitudeHbaseVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new AttitudeHbaseVo(), tableName, list, rowList, currentPage, pageSize, sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }

}
