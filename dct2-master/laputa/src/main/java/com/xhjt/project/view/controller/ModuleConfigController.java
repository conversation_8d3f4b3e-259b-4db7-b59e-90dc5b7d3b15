package com.xhjt.project.view.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.view.domain.ModuleConfigEntity;
import com.xhjt.project.view.service.ModuleConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模块配置信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/moduleConfig")
public class ModuleConfigController extends BaseController {

    @Autowired
    private ModuleConfigService moduleConfigService;

    /**
     * 获取模块配置信息列表
     */
    @PreAuthorize("@ss.hasPermi('view:moduleConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(ModuleConfigEntity moduleConfig) {
        startPage();
        List<ModuleConfigEntity> list = moduleConfigService.selectModuleConfigList(moduleConfig);
        return getDataTable(list);
    }

    @Log(title = "模块配置管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('view:moduleConfig:export')")
    @GetMapping("/export")
    public AjaxResult export(ModuleConfigEntity moduleConfig) {
        List<ModuleConfigEntity> list = moduleConfigService.selectModuleConfigList(moduleConfig);
        ExcelUtil<ModuleConfigEntity> util = new ExcelUtil<ModuleConfigEntity>(ModuleConfigEntity.class);
        return util.exportExcel(list, "模块配置数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('view:moduleConfig:query')")
    @GetMapping(value = "/{moduleConfigId}")
    public AjaxResult getInfo(@PathVariable Long moduleConfigId) {
        return AjaxResult.success(moduleConfigService.selectModuleConfigById(moduleConfigId));
    }

    /**
     * 新增模块配置信息
     */
    @PreAuthorize("@ss.hasPermi('view:moduleConfig:add')")
    @Log(title = "模块配置新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ModuleConfigEntity moduleConfig) {
        moduleConfig.setCreateBy(SecurityUtils.getUsername());
        return toAjax(moduleConfigService.addModuleConfig(moduleConfig));
    }

    /**
     * 修改模块配置信息
     */
    @PreAuthorize("@ss.hasPermi('view:moduleConfig:edit')")
    @Log(title = "模块配置修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ModuleConfigEntity moduleConfig) {
        moduleConfig.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(moduleConfigService.updateModuleConfig(moduleConfig));
    }

    /**
     * 删除模块配置信息
     */
    @PreAuthorize("@ss.hasPermi('view:moduleConfig:remove')")
    @Log(title = "模块配置删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{moduleConfigIds}")
    public AjaxResult remove(@PathVariable Long[] moduleConfigIds) {
        return toAjax(moduleConfigService.deleteModuleConfigByIds(moduleConfigIds));
    }

}
