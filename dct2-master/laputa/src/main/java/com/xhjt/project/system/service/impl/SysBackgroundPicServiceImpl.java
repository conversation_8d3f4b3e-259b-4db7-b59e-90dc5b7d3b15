package com.xhjt.project.system.service.impl;


import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.system.domain.SysBackgroundPic;
import com.xhjt.project.system.mapper.SysBackgroundPicMapper;
import com.xhjt.project.system.service.IBackgroundPicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @program: laputa
 * @description: 图片管理 实现类
 * @author: Mrs.<PERSON>
 * @create: 2021-12-17 16:09
 **/
@Service
public class SysBackgroundPicServiceImpl implements IBackgroundPicService{
    @Autowired
    private SysBackgroundPicMapper backgroundPicMapper;
    @Override
    public SysBackgroundPic selectById(Long Id) {
        return backgroundPicMapper.selectById(Id);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<SysBackgroundPic> selectBackPicList(SysBackgroundPic sysBackgroundPic) {
        return backgroundPicMapper.selectBackPicList(sysBackgroundPic);
    }

    @Override
    public List<SysBackgroundPic> selectBackPicListView(SysBackgroundPic sysBackgroundPic) {
        return backgroundPicMapper.selectBackPicList(sysBackgroundPic);
    }

    @Override
    public int insertBackgroundPic(SysBackgroundPic sysBackgroundPic) {
        sysBackgroundPic.setCreateTime(new Date());
        sysBackgroundPic.setUpdateTime(new Date());
        return backgroundPicMapper.insertSelective(sysBackgroundPic);
    }

    @Override
    public int updateBackgroudPic(SysBackgroundPic sysBackgroundPic) {
        sysBackgroundPic.setUpdateTime(new Date());
        return backgroundPicMapper.updateByPrimaryKeySelective(sysBackgroundPic);
    }

    @Override
    public int deteleBackgroundPicId(Long Id) {
        return backgroundPicMapper.deleteByPrimaryKey(Id);
    }

    @Override
    public int deteleBackgroundPicIds(Long[] Ids) {
        return backgroundPicMapper.deteleBackgroundPicIds(Ids);
    }


}

