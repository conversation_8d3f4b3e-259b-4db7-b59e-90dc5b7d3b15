package com.xhjt.project.snapshot.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.snapshot.domain.TransferSnapshotVo;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 快照传输管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/transfer")
public class SnapshotTransferController extends BaseController {

    @Autowired
    private SnapshotTransferService snapshotTransferService;
    @Autowired
    private SnapshotChannelService snapshotChannelService;

    @PreAuthorize("@ss.hasPermi('snapshot:transfer:list')")
    @RequestMapping("/list")
    public TableDataInfo list(SnapshotTransferEntity snapshotTransfer) {
        startPage();
        List<SnapshotTransferEntity> list = snapshotTransferService.selectSnapshotTransferList(snapshotTransfer);
        return getDataTable(list);
    }

    @Log(title = "快照传输", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:export')")
    @GetMapping("/export")
    public AjaxResult export(SnapshotTransferEntity snapshotTransfer) {
        List<SnapshotTransferEntity> list = snapshotTransferService.selectSnapshotTransferList(snapshotTransfer);
        ExcelUtil<SnapshotTransferEntity> util = new ExcelUtil<SnapshotTransferEntity>(SnapshotTransferEntity.class);
        return util.exportExcel(list, "快照传输");
    }

    /**
     * 根据id获取配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotTransferService.selectSnapshotTransferById(id));
    }

    /**
     * 新增
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SnapshotTransferEntity snapshotTransfer) {
        snapshotTransfer.setCreateBy(SecurityUtils.getUsername());
        int rows = snapshotTransferService.addSnapshotTransfer(snapshotTransfer);
        if (rows > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelBySnAndCode(snapshotTransfer.getSn(),snapshotTransfer.getChannelCode());
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 1);
        }
        return rows > 0 ? AjaxResult.success("操作成功") : AjaxResult.error("通道已使用");
    }

    /**
     * 修改配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SnapshotTransferEntity snapshotTransfer) {
        snapshotTransfer.setUpdateBy(SecurityUtils.getUsername());
        int rows = snapshotTransferService.updateSnapshotTransfer(snapshotTransfer);
        if (rows > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelBySnAndCode(snapshotTransfer.getSn(),snapshotTransfer.getChannelCode());
            TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
            transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
            transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
            snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
        }

        return toAjax(rows);
    }

    /**
     * 删除传输管理
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:remove')")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(id);
        int rows = snapshotTransferService.deleteSnapshotTransferById(id);
        if (rows > 0) {
            SnapshotUtil.addAllInfo();
            snapshotTransferService.sync2Ship(snapshotTransfer, 3);
        }
        return toAjax(rows);
    }

    /**
     * 定时任务状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PostMapping("/changeStatus/{id}/{status}")
    public AjaxResult changeStatus(@PathVariable Long id, @PathVariable Integer status) throws SchedulerException {
        int row = snapshotTransferService.changeStatus(id, status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(id);
            snapshotTransferService.sync2Ship(snapshotTransfer, 2);
        }
        return toAjax(row);
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PostMapping("/changeTrStatus/{id}/{status}")
    public AjaxResult changeTrStatus(@PathVariable Long id, @PathVariable Integer status) throws SchedulerException {
        int row = snapshotTransferService.changeTrStatus(id, status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(id);
            snapshotTransferService.sync2Ship(snapshotTransfer, 2);
        }
        return toAjax(row);
    }

    /**
     * 获取截屏图片
     */
    @PreAuthorize("@ss.hasPermi('snapshot:transfer:edit')")
    @PostMapping("/screenshot")
    public AjaxResult screenshot(@Validated @RequestBody SnapshotTransferEntity snapshotTransferEntity) {
        SnapshotChannelEntity channelEntity = snapshotChannelService.selectSnapshotChannelBySnAndCode(snapshotTransferEntity.getSn(),snapshotTransferEntity.getChannelCode());

        channelEntity.setCode("XHJT");

        channelEntity.setResolvingPower(snapshotTransferEntity.getResolvingPower());
        channelEntity.setCompartment(snapshotTransferEntity.getCompartment());
        channelEntity.setCost(snapshotTransferEntity.getCost());
        //发送kafka到船端
        snapshotChannelService.sync2ShipView(channelEntity,2);
        return AjaxResult.success(1);
    }
}
