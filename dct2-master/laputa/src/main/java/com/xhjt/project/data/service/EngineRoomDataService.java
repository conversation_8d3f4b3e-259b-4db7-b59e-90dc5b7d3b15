package com.xhjt.project.data.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.constant.HttpStatus;
import com.xhjt.dctcore.commoncore.domain.engineroom.EngineroomData;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.EngineroomDataVo;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.*;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.framework.web.page.TableDataInfo;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机舱数据信息 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class EngineRoomDataService {

    protected Logger logger = LoggerFactory.getLogger(EngineRoomDataService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 查询机舱完整数据
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @return
     */
    public List<EngineroomDataVo> queryByTime(Long startTime, Long endTime, Long shipId) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new EngineroomDataVo(), "ns1:engineroom", rowList);
    }

    /**
     * 机舱数据 字符串转为实体类
     *
     * @param engineroomDataVos
     * @return
     */
    public Map<String, List<EngineroomData>> analysisEngineRoomData(List<EngineroomDataVo> engineroomDataVos) {
        Map<String, List<EngineroomData>> map = new HashMap<String, List<EngineroomData>>();

        List<EngineroomData> list;
        for (EngineroomDataVo dataVo : engineroomDataVos) {
            list = Lists.newArrayList();
            String[] dataArr = dataVo.getInfo().split("\\|");
            EngineroomData engineroomData;
            for (String str : dataArr) {
                engineroomData = new EngineroomData();
                engineroomData.dataAnalysis(str);
                list.add(engineroomData);
            }

            map.put(dataVo.getId(), list);
        }

        return map;
    }


    /**
     * 查询 油舱液位数据
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @return
     */
    public List<TankLevelVo> selectTankLevelList(Long startTime, Long endTime, Long shipId) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new TankLevelVo(), "ns1:tank_level", rowList);
    }

    /**
     * 油舱液位数据 列表
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @param currentPage
     * @param pageSize
     * @return
     */
    public TableDataInfo queryTankLevelPage(Long startTime, Long endTime, Long shipId, Integer currentPage, Integer pageSize,String sort) {
        List<TankLevelVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new TankLevelVo(), "ns1:tank_level", list, rowList, currentPage, pageSize,sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }

    /**
     * 查询主推侧推数据
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @return
     */
    public List<MainSideThrustVo> selectMainSideThrustList(Long startTime, Long endTime, Long shipId) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new MainSideThrustVo(), "ns1:main_side_thrust", rowList);
    }

    /**
     * 主推侧推数据 列表
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @param currentPage
     * @param pageSize
     * @return
     */
    public TableDataInfo queryMainSideThrustPage(Long startTime, Long endTime, Long shipId, Integer currentPage, Integer pageSize,String sort) {
        List<MainSideThrustVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new MainSideThrustVo(), "ns1:main_side_thrust", list, rowList, currentPage, pageSize,sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }

    /**
     * 查询站位距离数据
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @return
     */
    public List<StationDistanceVo> selectStationDistanceList(Long startTime, Long endTime, Long shipId) {
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new StationDistanceVo(), "ns1:s_station_distance_aa0001", rowList);
    }

    /**
     * 站位距离数据 列表
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @param currentPage
     * @param pageSize
     * @return
     */
    public TableDataInfo queryStationDistancePage(Long startTime, Long endTime, Long shipId, Integer currentPage, Integer pageSize,String sort) {
        List<StationDistanceVo> list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = hBaseDaoUtil.scanByStartStop4PageDesc(new StationDistanceVo(), "ns1:s_station_distance_aa0001", list, rowList, currentPage, pageSize,sort);

        return new TableDataInfo(list, size, HttpStatus.SUCCESS);
    }


    /**
     * 机舱数据 列表
     *
     * @param startTime
     * @param endTime
     * @param shipId
     * @param currentPage
     * @param pageSize
     * @return
     */
    public TableDataInfo queryEngineRoomDataPage(String engineRoomName, Long startTime, Long endTime, Long shipId, Integer currentPage, Integer pageSize,String sort) throws Exception{

        List list = Lists.newArrayList();
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        int size = 0;
        switch (engineRoomName) {
            case "ballast_water":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new BallastWaterVo(), "ns1:ballast_water", list, rowList, currentPage, pageSize,sort);
                break;
            case "boiler_feed_water":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new BoilerFeedWaterVo(), "ns1:boiler_feedWater", list, rowList, currentPage, pageSize,sort);
                break;
            case "compressed_air":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new CompressedAirVo(), "ns1:compressed_air", list, rowList, currentPage, pageSize,sort);
                break;
            case "diesel_generator1":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new DieselGeneratorVo(), "ns1:diesel_generator1", list, rowList, currentPage, pageSize,sort);
                break;
            case "diesel_generator2":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new DieselGeneratorVo(), "ns1:diesel_generator2", list, rowList, currentPage, pageSize,sort);
                break;
            case "diesel_generator3":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new DieselGeneratorVo(), "ns1:diesel_generator3", list, rowList, currentPage, pageSize,sort);
                break;
            case "emergency_generator":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new EmergencyGeneratorVo(), "ns1:emergency_generator", list, rowList, currentPage, pageSize,sort);
                break;
            case "fw_generator":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new FwGeneratorVo(), "ns1:fw_generator", list, rowList, currentPage, pageSize,sort);
                break;
            case "fuel_oil":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new FuelOilVo(), "ns1:fuel_oil", list, rowList, currentPage, pageSize,sort);
                break;
            case "tunnel_thruster":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new TunnelThrusterVo(), "ns1:tunnel_thruster", list, rowList, currentPage, pageSize,sort);
                break;
            case "sw_cool":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new SwCoolingVo(), "ns1:sw_cool", list, rowList, currentPage, pageSize,sort);
                break;
            case "power_manage":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new PowerManageVo(), "ns1:power_manage", list, rowList, currentPage, pageSize,sort);
                break;
            case "propulsion":
                size = hBaseDaoUtil.scanByStartStop4PageDesc(new PropulsionVo(), "ns1:propulsion", list, rowList, currentPage, pageSize,sort);
                break;
            default:
        }
        return new TableDataInfo(convert(list), size, HttpStatus.SUCCESS);
    }

    /**
     * 查询压载水数据
     */
    public List<BallastWaterVo> selectBallastWaterList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new BallastWaterVo(), "ns1:ballast_water", rowList);
    }

    /**
     * 查询应急发电机数据
     */
    public List<EmergencyGeneratorVo> selectEmergencyGeneratorList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new EmergencyGeneratorVo(), "ns1:emergency_generator", rowList);
    }

    /**
     * 查询锅炉给水数据
     */
    public List<BoilerFeedWaterVo> selectBoilerFeedWaterList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new BoilerFeedWaterVo(), "ns1:boiler_feedWater", rowList);
    }

    /**
     * 查询压缩空气数据
     */
    public List<CompressedAirVo> selectCompressedAirVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new CompressedAirVo(), "ns1:compressed_air", rowList);
    }

    /**
     * 查询造水机数据
     */
    public List<FwGeneratorVo> selectFwGeneratorVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new FwGeneratorVo(), "ns1:fw_generator", rowList);
    }

    /**
     * 查询燃油系统数据
     */
    public List<FuelOilVo> selectFuelOilVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new FuelOilVo(), "ns1:fuel_oil", rowList);
    }

    /**
     * 查询侧推数据
     */
    public List<TunnelThrusterVo> selectTunnelThrusterVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new TunnelThrusterVo(), "ns1:tunnel_thruster", rowList);
    }

    /**
     * 查询海水冷却数据
     */
    public List<SwCoolingVo> selectSwCoolingVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new SwCoolingVo(), "ns1:sw_cool", rowList);
    }

    /**
     * 查询主推轴承数据
     */
    public List<PropulsionVo> selectPropulsionVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new PropulsionVo(), "ns1:propulsion", rowList);
    }

    /**
     * 查询电力管理数据
     */
    public List<PowerManageVo> selectPowerManageVoList(Long startTime, Long endTime, Long shipId){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new PowerManageVo(), "ns1:power_manage", rowList);
    }

    /**
     * 查询发电机数据
     */
    public List<DieselGeneratorVo> selectDieselGeneratorVoList(Long startTime, Long endTime, Long shipId,String tableName){
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new DieselGeneratorVo(), "ns1:"+tableName, rowList);
    }


    /**
     * 数据转换
     *
     * @param obj
     * @return
     */
    public List convert(List<Object> obj) throws Exception {
        for (Object object : obj) {
            Class<?> clazz = object.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                String fieldName = field.getName();
                if("id".equals(fieldName)||"time".equals(fieldName)||"bjTime".equals(fieldName)){
                    continue;
                }
                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                String getMethodName = "get" + firstLetter + fieldName.substring(1);
                String dataStr = "";
                try{
                     dataStr = clazz.getMethod(getMethodName).invoke(object).toString();
                }catch (NullPointerException e){
                    e.printStackTrace();
                }
                if (dataStr.contains("|")) {
                    field.setAccessible(true);
                    String[] dataArr = dataStr.split("\\|");
                    field.set(object, dataArr[dataArr.length - 1]);
                }
            }
        }
        return obj;
    }

}
