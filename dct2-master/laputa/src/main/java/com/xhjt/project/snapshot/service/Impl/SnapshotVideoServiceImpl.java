package com.xhjt.project.snapshot.service.Impl;

import com.xhjt.project.snapshot.domain.SnapshotVideoEntity;
import com.xhjt.project.snapshot.mapper.SnapshotVideoMapper;
import com.xhjt.project.snapshot.service.SnapshotVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SnapshotVideoServiceImpl implements SnapshotVideoService {

    @Autowired
    protected SnapshotVideoMapper snapshotVideoMapper;

    @Override
    public SnapshotVideoEntity selectSnapshotVideo(SnapshotVideoEntity snapshotVideoEntity) {
        return snapshotVideoMapper.selectSnapshotVideo(snapshotVideoEntity);
    }

    @Override
    public int updateSnapshotVideo(SnapshotVideoEntity snapshotVideoEntity) {
        return snapshotVideoMapper.updateSnapshotVideo(snapshotVideoEntity);
    }

    @Override
    public int insertSnapshotVideo(SnapshotVideoEntity snapshotVideoEntity) {
        return snapshotVideoMapper.insertSnapshotVideo(snapshotVideoEntity);
    }
}
