package com.xhjt.project.snapshot.mapper;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;

import java.util.List;

/**
 * 通道配置
 * <AUTHOR>
 */
public interface SnapshotChannelMapper {

    /**
     * 查询通道配置列表
     *
     * @param snapshotChannel
     * @return 通道配置集合
     */
    public List<SnapshotChannelEntity> selectSnapshotChannelList(SnapshotChannelEntity snapshotChannel);

    /**
     *查询通道配置信息
     * @param snapshotChannel 通道配置信息
     * @return 通道配置信息
     */
    public  SnapshotChannelEntity  selectSnapshotChannel(SnapshotChannelEntity snapshotChannel);

    /**
     * 修改配置信息
     *
     * @param snapshotChannel 配置信息
     * @return 结果
     */
    public int updateSnapshotChannel(SnapshotChannelEntity snapshotChannel);

    /**
     * 添加配置信息
     * @param snapshotChannel 配置信息
     * @return 结果
     */
    public int addSnapshotChannel(SnapshotChannelEntity snapshotChannel);

    /**
     * 删除配置信息
     * @param id
     * @return 结果
     */
    public int deleteSnapshotChannelById(Long id);

    /**
     * 删除
     *
     * @param code
     * @return 结果
     */
    public int deleteSnapshotChannelByCode(String code);
}
