package com.xhjt.project.ship.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.mapper.CruiseMapper;
import com.xhjt.project.ship.mapper.ShipMapper;
import com.xhjt.project.ship.service.CruiseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 航次管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CruiseServiceImpl implements CruiseService {
    @Autowired
    private CruiseMapper cruiseMapper;

    @Autowired
    private ShipMapper shipMapper;

    @Override
    public int deleteNewCruiseById(Long cruiseId) {
        CruiseEntity cruise = new CruiseEntity();
        cruise.setCruiseId(cruiseId);
        return cruiseMapper.deleteNewCruiseById(cruise);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<CruiseEntity> selectAllNewCruise(CruiseEntity cruise) {
        return cruiseMapper.selectAllNewCruise(cruise);
    }

    @Override
    public CruiseEntity selectNewCruiseById(Long cruiseId) {
        CruiseEntity cruise = new CruiseEntity();
        cruise.setCruiseId(cruiseId);
        return cruiseMapper.selectNewCruiseById(cruise);
    }

    @Override
    public List<CruiseEntity> selectNewCruiseBySn(String sn) {
        return cruiseMapper.selectNewCruiseBySn(sn);
    }


    @Override
    public CruiseEntity queryCurrentCruiseBySn(String sn) {
        List<CruiseEntity> list = cruiseMapper.selectNewCruiseBySn(sn);

        if (list.size() == 0) {
            return null;
        }
        long nt = System.currentTimeMillis();
        for (CruiseEntity cruiseEntity : list) {
            if (Long.parseLong(cruiseEntity.getStartTime()) <= nt) {
                return cruiseEntity;
            }
        }
        return null;
    }


    /**
     * 新增航次
     */
    @Override
    public int addCruise(JSONObject cruiseEntity) {
//        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
//            cruiseEntity.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
//        }
        CruiseEntity cruise = new CruiseEntity();
        String name = "";
        List<HashMap> editRequiredForm = (List<HashMap>) cruiseEntity.get("editRequiredForm");
        for(HashMap hashMap : editRequiredForm){
            if(hashMap.get("property").equals("shipName")){
                name = hashMap.get("value")+"";
                break;
            }
        }
        ShipEntity shipEntity = new ShipEntity();
        shipEntity.setName(name);
        shipEntity.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<ShipEntity> ships =  shipMapper.selectShipByName(shipEntity);
        if (ships==null || ships.size()<=0){
            throw new CustomException("sn数据不存在");
        }
        String sn = ships.get(0).getSn();
        cruise.setSn(sn);
        cruise.setCode(cruiseEntity.get("code")+"");
        List<HashMap> list = (List) cruiseEntity.get("editNoRequiredForm");
        List<HashMap> list2 = (List) cruiseEntity.get("editRequiredForm");
        for(int i = 0;i < list2.size();i++){
            list.add(list2.get(i));
        }

        List<HashMap> res = new ArrayList<>();
        for(int i = 0;i < list.size();i++){
            HashMap<String,Object> map = new HashMap();
            if(list.get(i).get("value")!=null && !list.get(i).get("value").equals("null") && !list.get(i).get("value").equals("")){
                map.put("property",list.get(i).get("property")+"");
                map.put("value",list.get(i).get("value"));
                res.add(map); //moduleJSon(List<Map>)
            }
        }

        String createBy = SecurityUtils.getUsername();

        Map map = res.stream().collect(Collectors.toMap(p->p.get("property"), p->p.get("value")));//moduleJSon(Map)
        map.put("deptId",SecurityUtils.getLoginUser().getUser().getDeptId());
        map.put("createBy",createBy);
        map.put("createTime",new Date());
        map.put("sn",sn);
        cruise.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        cruise.setCode(map.get("code")+"");
        String s = JSONObject.toJSONString(map);
        cruise.setModuleJson(s);

        return cruiseMapper.addNewCruise(cruise);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<CruiseEntity> selectNewCruiseList(CruiseEntity cruise) {
        return cruiseMapper.selectNewCruiseList(cruise);
    }

    @Override
    public CruiseEntity selectCruiseByCruiseName(CruiseEntity cruiseName) {
        return cruiseMapper.selectCruiseByCruiseName(cruiseName);
    }

    @Override
    public List<CruiseEntity> selectCruiseNewByFactor(CruiseEntity cruise) {
        return cruiseMapper.selectCruiseNewByFactor(cruise);
    }

    @Override
    public int updateNewCruise(CruiseEntity cruise) {
        return cruiseMapper.updateNewCruise(cruise);
    }
}
