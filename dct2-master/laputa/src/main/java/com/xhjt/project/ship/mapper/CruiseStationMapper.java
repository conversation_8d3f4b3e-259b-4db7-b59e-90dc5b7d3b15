package com.xhjt.project.ship.mapper;

import com.xhjt.project.ship.domain.CruiseStationEntity;

import java.util.List;

/**
 * 航次站位管理 数据层
 *
 * <AUTHOR>
 */
public interface CruiseStationMapper {

    List<CruiseStationEntity> selectNewCruiseStationList(CruiseStationEntity cruiseStation);

    int addNewCruiseStation(CruiseStationEntity cruiseStation);

    int updateNewCruiseStation(CruiseStationEntity cruiseStation);

    int deleteNewCruiseStationById(CruiseStationEntity cruiseStation);

    List<CruiseStationEntity> selectCruiseStationNewByFactor(CruiseStationEntity cruiseStation);

    CruiseStationEntity selectNewCruiseStationById(CruiseStationEntity cruiseStation);

    CruiseStationEntity selectNewCruiseStationByName(CruiseStationEntity cruiseStation);
}
