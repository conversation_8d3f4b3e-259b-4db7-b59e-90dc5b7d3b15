package com.xhjt.project.view.service;

import com.xhjt.project.view.domain.ViewSchemeEntity;

import java.util.List;

/**
 * 视图方案管理 服务层
 *
 * <AUTHOR>
 */
public interface ViewSchemeService {
    /**
     * 查询视图方案信息
     *
     * @param viewSchemeId 视图方案ID
     * @return 视图方案信息
     */
    public ViewSchemeEntity selectViewSchemeById(Long viewSchemeId);

    /**
     * 查询视图方案列表
     *
     * @param viewScheme 视图方案信息
     * @return 视图方案集合
     */
    public List<ViewSchemeEntity> selectViewSchemeList(ViewSchemeEntity viewScheme);

    /**
     * 新增视图方案
     *
     * @param viewScheme 视图方案信息
     * @return 结果
     */
    public int addViewScheme(ViewSchemeEntity viewScheme);

    /**
     * 修改视图方案
     *
     * @param viewScheme 视图方案信息
     * @return 结果
     */
    public int updateViewScheme(ViewSchemeEntity viewScheme);

    /**
     * 启用
     *
     * @param id
     * @return
     */
    public int enable(Long id);

    /**
     * 停用
     *
     * @param id
     * @return
     */
    public int disable(Long id);
    /**
     * 删除视图方案信息
     *
     * @param viewSchemeId 参数ID
     * @return 结果
     */
    public int deleteViewSchemeById(Long viewSchemeId);

    /**
     * 批量删除参数信息
     *
     * @param viewSchemeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteViewSchemeByIds(Long[] viewSchemeIds);

}
