package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.MainSideThrustVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * 主推侧推数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class MainSideThrustService {

    private Logger logger = LoggerFactory.getLogger(MainSideThrustService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * @param mainSideThrustVo
     */
    @Transactional(rollbackFor = Exception.class)
    public MainSideThrustVo save(String sn, MainSideThrustVo mainSideThrustVo, Long timeStamp) {
        mainSideThrustVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        logger.info("mainSideThrustVo开始保存到hbase，---{}", JSONObject.toJSONString(mainSideThrustVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "main_side_thrust"), mainSideThrustVo);

        return mainSideThrustVo;
    }
}
