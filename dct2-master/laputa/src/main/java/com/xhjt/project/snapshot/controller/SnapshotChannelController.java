package com.xhjt.project.snapshot.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotTransferEntity;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.snapshot.domain.TransferSnapshotVo;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotTransferService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通道配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/snapshot/channel")
public class SnapshotChannelController extends BaseController {

    @Autowired
    private SnapshotChannelService snapshotChannelService;
    @Autowired
    private SnapshotTransferService snapshotTransferService;

    @PreAuthorize("@ss.hasPermi('snapshot:channel:list')")
    @RequestMapping("/list")
    public TableDataInfo list(SnapshotChannelEntity snapshotChannelEntity) {
        startPage();
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(snapshotChannelEntity);
        return getDataTable(list);
    }

    @Log(title = "快照通道", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('snapshot:channel:export')")
    @GetMapping("/export")
    public AjaxResult export(SnapshotChannelEntity snapshotChannelEntity) {
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(snapshotChannelEntity);
        ExcelUtil<SnapshotChannelEntity> util = new ExcelUtil<SnapshotChannelEntity>(SnapshotChannelEntity.class);
        return util.exportExcel(list, "快照通道");
    }

    /**
     * 根据通道号获取配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotChannelService.selectSnapshotChannelById(id));
    }

    /**
     * 新增
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SnapshotChannelEntity snapshotChannel) throws Exception {
        snapshotChannel.setCreateBy(SecurityUtils.getUsername());
        SnapshotChannelEntity snapshotChannelEntity = snapshotChannelService.addSnapshotChannel(snapshotChannel);
        int row = 0;
        if (snapshotChannelEntity != null) {
            //同时新增快照传输
            SnapshotTransferEntity snapshotTransfer = new SnapshotTransferEntity();
            BeanUtils.copyProperties(snapshotChannel, snapshotTransfer);
            snapshotTransfer.setStatus(1);//默认开启存储通道
            snapshotTransfer.setTransferStatus(1);//默认开启传输通道
            snapshotTransfer.setChannelCode(snapshotChannelEntity.getCode());//通道编号
            snapshotTransfer.setChannelName(snapshotChannelEntity.getName());
            snapshotTransfer.setConnectStatus("1");//默认连接上，具体状态等待船端返回做修改
            int rowTransfer = snapshotTransferService.addSnapshotTransfer(snapshotTransfer);
            if (rowTransfer > 0) {
                SnapshotUtil.addAllInfo();
                //这个逻辑在船端那边接收到后处理
//                logger.info("第一次添加通道，直接去获取摄像头截屏。。。。{}", System.currentTimeMillis());
//                snapshotChannelService.screenshot(snapshotChannelEntity.getCode());
                //同步信息到船端
                TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
                transferSnapshotVo.setSnapshotChannelEntity(snapshotChannelEntity);
                transferSnapshotVo.setSnapshotTransferEntity(snapshotTransfer);
                snapshotChannelService.syncNewShore(transferSnapshotVo, 1);
            }
            row = 1;
        }
        return row > 0 ? AjaxResult.success("操作成功") : AjaxResult.error("通道名已存在");
    }

    /**
     * 修改配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SnapshotChannelEntity snapshotChannel) {
        snapshotChannel.setUpdateBy(SecurityUtils.getUsername());
        int rows = snapshotChannelService.updateSnapshotChannel(snapshotChannel);
        if (rows > 0) {
            SnapshotTransferEntity snapshotTransferEntity = snapshotTransferService.selectBySnAndChannelCode(snapshotChannel.getSn(), snapshotChannel.getCode());
            Long id = snapshotTransferEntity.getId();
            //同时更新快照传输
            BeanUtils.copyProperties(snapshotChannel, snapshotTransferEntity);
            snapshotTransferEntity.setChannelCode(snapshotChannel.getCode());//通道编号
            snapshotTransferEntity.setStatus(snapshotChannel.getTrStatus());//存储状态
            snapshotTransferEntity.setChannelName(snapshotChannel.getName());//通道名称
            snapshotTransferEntity.setId(id);
            int rowTransfer = snapshotTransferService.updateSnapshotTransfer(snapshotTransferEntity);
            if (rowTransfer > 0) {
                SnapshotUtil.addAllInfo();
                TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
                transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
                transferSnapshotVo.setSnapshotTransferEntity(snapshotTransferEntity);
                snapshotChannelService.syncNewShore(transferSnapshotVo, 2);
            }
        }

        return toAjax(rows);
    }

    /**
     * 删除配置信息
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:remove')")
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        SnapshotChannelEntity snapshotChannel = snapshotChannelService.selectSnapshotChannelById(id);
        int rows = snapshotChannelService.deleteSnapshotChannelById(id);
        if (rows > 0) {
            //删除对应传输属性记录
            SnapshotTransferEntity snapshotTransferEntity = snapshotTransferService.selectBySnAndChannelCode(snapshotChannel.getSn(), snapshotChannel.getCode());
            if (snapshotTransferEntity != null) {
                int row = snapshotTransferService.deleteSnapshotTransferById(snapshotTransferEntity.getId());
                if (row > 0) {
                    SnapshotUtil.addAllInfo();
                    TransferSnapshotVo transferSnapshotVo = new TransferSnapshotVo();
                    transferSnapshotVo.setSnapshotChannelEntity(snapshotChannel);
                    transferSnapshotVo.setSnapshotTransferEntity(snapshotTransferEntity);
                    snapshotChannelService.syncNewShore(transferSnapshotVo, 3);
                }
            }

        }
        return toAjax(rows);
    }

    /**
     * 获取信息列表
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:list')")
    @GetMapping("/allList/{sn}")
    public AjaxResult allList(@PathVariable String sn) {
        SnapshotChannelEntity channelEntity = new SnapshotChannelEntity();
        channelEntity.setSn(sn);
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(channelEntity);
        return AjaxResult.success(list);
    }

    /**
     * 获取信息列表
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:list')")
    @GetMapping("/unboundList/{sn}")
    public AjaxResult unboundList(@PathVariable String sn) {
        SnapshotChannelEntity channelEntity = new SnapshotChannelEntity();
        channelEntity.setSn(sn);
        List<SnapshotChannelEntity> list = snapshotChannelService.queryUnboundList(channelEntity);
        return AjaxResult.success(list);
    }

    /**
     * 获取截屏图片-修改界面预览
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PostMapping("/screenshot")
    public AjaxResult screenshot(@Validated @RequestBody SnapshotChannelEntity snapshotChannelEntity) {
        SnapshotChannelEntity channelEntity = new SnapshotChannelEntity();
        channelEntity.setSn(snapshotChannelEntity.getSn());
        channelEntity.setStatus("1");
        channelEntity.setTrStatus(1);
        channelEntity.setTransferStatus(1);
        channelEntity.setAddress(snapshotChannelEntity.getAddress());
//        if (StringUtils.isNotBlank(code)){
//            channelEntity.setCode(code);
//        }else {
            //暂时定为统一得编码，以便区分数据来源
            channelEntity.setCode("XHJT");
//        }

        channelEntity.setResolvingPower(snapshotChannelEntity.getResolvingPower());
        channelEntity.setCompartment(snapshotChannelEntity.getCompartment());
        channelEntity.setCost(snapshotChannelEntity.getCost());
        //发送kafka到船端
        snapshotChannelService.sync2ShipView(channelEntity,2);
        return AjaxResult.success(1);
    }


    /**
     * 定时任务-存储状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PostMapping("/changeStatus/{code}/{status}/{sn}")
    public AjaxResult changeStatus(@PathVariable String code, @PathVariable Integer status, @PathVariable String sn) throws SchedulerException {
        SnapshotTransferEntity transferEntity = snapshotTransferService.selectBySnAndChannelCode(sn, code);
        int row = snapshotTransferService.changeStatus(transferEntity.getId(), status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(transferEntity.getId());
            snapshotTransferService.sync2Ship(snapshotTransfer, 2);
        }
        return toAjax(row);
    }

    /**
     * 定时任务-传输状态修改
     */
    @PreAuthorize("@ss.hasPermi('snapshot:channel:edit')")
    @PostMapping("/changeTrStatus/{code}/{status}/{sn}")
    public AjaxResult changeTrStatus(@PathVariable String code, @PathVariable Integer status, @PathVariable String sn) throws SchedulerException {
        SnapshotTransferEntity transferEntity = snapshotTransferService.selectBySnAndChannelCode(sn, code);
        int row = snapshotTransferService.changeTrStatus(transferEntity.getId(), status);
        if (row > 0) {
            SnapshotUtil.addAllInfo();
            SnapshotTransferEntity snapshotTransfer = snapshotTransferService.selectSnapshotTransferById(transferEntity.getId());
            snapshotTransferService.sync2Ship(snapshotTransfer, 2);
        }
        return toAjax(row);
    }
}
