package com.xhjt.project.ship.domain;

import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.annotation.Excel;
import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 航次信息
 *
 * <AUTHOR>
 */
public class CruiseEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long cruiseId;

    /**
     * 航次名称
     */
    @Excel(name = "航次名称")
    private String cruiseName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 关联的船只
     */
    private String sn;

    /**
     * 船只名称
     */
    @Excel(name = "船只名称")
    private String shipName;

    /**
     * 船长
     */
    private String captain;

    /**
     * 航次编号
     */
    @Excel(name = "航次编号")
    private String code;

    /**
     * 开始时间
     */
    @Excel(name = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @Excel(name = "结束时间")
    private String finishTime;

    /**
     * 计划开始时间
     */
    @Excel(name = "计划开始时间")
    private String planStartTime;

    /**
     * 计划结束时间
     */
    @Excel(name = "计划结束时间")
    private String planEndTime;

    /**
     * 总天数
     */
    private Integer totalDays;

    /**
     * 历史里程
     */
    private Double historyMileage;

    /**
     * 起始地
     */
    private String startPort;

    /**
     * 目的地
     */
    private String endPort;

    /**
     * 目标海域
     */
    @Excel(name = "目标海域")
    private String seaArea;

    /**
     * 编辑时间
     */
    @Excel(name = "编辑时间")
    private String editTime;

    /**
     * moduleJson
     */
    private String moduleJson;

    public String getCruiseName() {
        return cruiseName;
    }

    public void setCruiseName(String cruiseName) {
        this.cruiseName = cruiseName;
    }

    public Long getCruiseId() {
        return cruiseId;
    }

    public void setCruiseId(Long cruiseId) {
        this.cruiseId = cruiseId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getCaptain() {
        return captain;
    }

    public void setCaptain(String captain) {
        this.captain = captain;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getTotalDays() {
        return totalDays;
    }

    public void setTotalDays(Integer totalDays) {
        this.totalDays = totalDays;
    }

    public Double getHistoryMileage() {
        return historyMileage;
    }

    public void setHistoryMileage(Double historyMileage) {
        this.historyMileage = historyMileage;
    }

    public String getStartPort() {
        return startPort;
    }

    public void setStartPort(String startPort) {
        this.startPort = startPort;
    }

    public String getEndPort() {
        return endPort;
    }

    public void setEndPort(String endPort) {
        this.endPort = endPort;
    }

    public String getSeaArea() {
        return seaArea;
    }

    public void setSeaArea(String seaArea) {
        this.seaArea = seaArea;
    }

    public String getPlanStartTime() {
        return planStartTime;
    }

    public void setPlanStartTime(String planStartTime) {
        this.planStartTime = planStartTime;
    }

    public String getPlanEndTime() {
        return planEndTime;
    }

    public void setPlanEndTime(String planEndTime) {
        this.planEndTime = planEndTime;
    }

    public String getEditTime() {
        return editTime;
    }

    public void setEditTime(String editTime) {
        this.editTime = editTime;
    }

    public String getModuleJson() {
        return moduleJson;
    }

    public void setModuleJson(String moduleJson) {
        this.moduleJson = moduleJson;
    }
}
