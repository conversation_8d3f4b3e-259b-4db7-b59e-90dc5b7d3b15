package com.xhjt.project.netty.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.hbase.*;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.device.service.TransferAttributeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * UDP消息存储处理服务类
 *
 * <AUTHOR>
 */
@Service
public class StoreService {
    public final static Logger logger = LoggerFactory.getLogger(StoreService.class);

    @Value("${project.sourceDataPath}")
    private String sourceDataPath;

    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private ValueOperations<String, Object> valueOperations;
    @Autowired
    private TransferAttributeService transferAttributeService;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 抽稀的分钟集合
     */
    private static final Integer[] INTERVALS = {0, 1, 5, 15};


    void handleMessage(TransferPackage transferPackage) {
        try {
            Class cl = getClassByType(transferPackage.getDeviceType());
            if (cl == null) {
                return;
            }

            Object hbaseVo = analysisMessage(transferPackage, cl);
            if (hbaseVo == null) {
                return;
            }
            Arrays.stream(INTERVALS).forEach(i -> save2Hbase(transferPackage, hbaseVo, cl, i));

            save2Redis(transferPackage.getSn(), transferPackage.getDeviceCode(), transferPackage.getTime(), hbaseVo);
        } catch (Exception e) {
            logger.error("数据异常---{}", e);
        }
    }

    /**
     * 根据类型获取 类的Class
     *
     * @param type
     * @return
     */
    private Class getClassByType(Integer type) {
        switch (DeviceTypeEnum.getByValue(type)) {
            case GPS: {
                return GpsHbaseVo.class;
            }
            case AWS: {
                return AwsHbaseVo.class;
            }
            case SBE21: {
                return Sbe21HbaseVo.class;
            }
            case GO8050: {
                return Co2HbaseVo.class;
            }
            case COMPASS: {
                return CompassHbaseVo.class;
            }
            case LOG: {
                return LogHbaseVo.class;
            }
            case RA_CDU1: {
                return RacduHbaseVo.class;
            }
            case RA_CDU2: {
                return RacduHbaseVo.class;
            }
            case ATTITUDE: {
                return AttitudeHbaseVo.class;
            }
            case EA600: {
                return Ea600HbaseVo.class;
            }
            case ECHO: {
                return DetectorHbaseVo.class;
            }
            case WIND: {
                return WindHbaseVo.class;
            }
            case GPS_IN: {
                return GpsInHbaseVo.class;
            }
            case COMPASS_IN: {
                return CompassInHbaseVo.class;
            }
            default: {
                return null;
            }
        }
    }

    /**
     * 保存到hbase中 判断数据是否需要抽稀，并保存
     *
     * @param message
     * @param hbaseVo
     * @param interval 时间间隔
     */
    private void save2Hbase(TransferPackage message, Object hbaseVo, Class cl, Integer interval) {
        // Co2不做一分钟的抽稀
        if (DeviceTypeEnum.GO8050.getValue().equals(message.getDeviceType()) && interval == 1) {
            return;
        }

        Long nTime = message.getTime();
        // 把时间转换为往前最近的整n分钟
        if (interval != 0) {
            nTime = DateUtils.fetchCompleteNMinute(message.getTime(), interval, 0);
        }

        String rowKey = hBaseDaoUtil.getRowKey(nTime);

        String typeStr = DeviceTypeEnum.getByValue(message.getDeviceType()).getAlias();

        String key = message.getSn() + "-" + message.getDeviceCode() + "-VACUUMING-" + interval + "-" + rowKey;
        if (valueOperations.get(key) != null) {
            return;
        }

        setValue2Vo(hbaseVo, cl, "id", rowKey);
        setValue2Vo(hbaseVo, cl, "initialTime", String.valueOf(nTime));
        setValue2Vo(hbaseVo, cl, "initialBjTime", DateUtils.getDateToString(nTime));

        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(message.getSn(), typeStr, message.getDeviceCode(), interval), hbaseVo);

        // 设置过期时间为3天
        valueOperations.set(key, nTime, 3 * 24, TimeUnit.HOURS);

        logger.info("hbase保存成功----{}", JSONObject.toJSONString(hbaseVo));
    }


    private void setValue2Vo(Object hbaseVo, Class cl, String fieldName, String value) {
        if (StringUtils.isBlank(value)) {
            return;
        }
        Field field;
        try {
            field = cl.getDeclaredField(fieldName);
            field.setAccessible(true);

            field.set(hbaseVo, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            logger.error("字段设置错误---{}", e);
        }
    }

    private Object analysisMessage(TransferPackage message, Class cl) throws IllegalAccessException, InstantiationException {
        List<String> attributes = transferAttributeService.queryAttributesByCode(message.getSn(), message.getDeviceCode());
        if (attributes == null || attributes.size() == 0) {
            return null;
        }

        String[] values = message.getMessage().split(",", -1);
        if (values.length != attributes.size()) {
            return null;
        }

        Object hbaseVo = cl.newInstance();

        Stream.iterate(0, i -> i + 1).limit(attributes.size()).forEach(i -> setValue2Vo(hbaseVo, cl, attributes.get(i), values[i]));

        return hbaseVo;
    }


    /**
     * 保存到redis中
     *
     * @param dataTime
     * @param object
     */
    public void save2Redis(String sn, String code, Long dataTime, Object object) {
        String dateKey = RedisParameter.LATEST_DATE + sn + "_" + code;
        String objectKey = RedisParameter.LATEST_DATA + sn + "_" + code;
        //判断redis数据库是否有数据
        if (redisTemplate.hasKey(dateKey)) {
            long timestamp = Long.valueOf(String.valueOf(valueOperations.get(dateKey)));
            if (dataTime > timestamp) {
                valueOperations.set(objectKey, JSONObject.toJSONString(object));
                valueOperations.set(dateKey, dataTime);
            }
        } else {
            valueOperations.set(objectKey, JSONObject.toJSONString(object));
            valueOperations.set(dateKey, dataTime);
        }
    }
}
