package com.xhjt.project.netty.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.SyncEntity;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.device.domain.RedisObjVo;
import com.xhjt.project.monitor.domain.Ctll;
import com.xhjt.project.netty.service.SyncService;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 传输属性 信息操作处理
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/foreign/sync")
public class SyncCronController extends BaseController {

    @Autowired
    private SnapshotChannelService snapshotChannelService;

    /**
     * 控制船端机器重启
     */
    @GetMapping("/controller/{sn}")
    public Ctll controller(@PathVariable("sn") String sn) throws SchedulerException {
        Ctll ctll = new Ctll();
        ctll.setName("reboot");
        ctll.setCode("boot");
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(ctll), 1, "controller");
        snapshotChannelService.sync2ShipController(syncEntity,ctll.getCode(),sn);
        return ctll;
//        return AjaxResult.success("操作成功，请等待。。。");
    }

    /**
     * 修改船端redis参数值
     * sn:sn号
     * name:redis key
     * number:具体数量值
     */
    @GetMapping("/controller/{sn}/{name}/{number}")
    public Ctll controller(@PathVariable("sn") String sn,@PathVariable("name") String name,@PathVariable("number") Integer number) throws SchedulerException {
        Ctll ctll = new Ctll();
        ctll.setName(name);
        ctll.setCode(number+"");
        SyncEntity syncEntity = new SyncEntity(JSONObject.toJSONString(ctll), 1, "updateRedis");
        snapshotChannelService.sync2ShipController(syncEntity,"uprs",sn);
        return ctll;
//        return AjaxResult.success("操作成功，请等待。。。");
    }
}
