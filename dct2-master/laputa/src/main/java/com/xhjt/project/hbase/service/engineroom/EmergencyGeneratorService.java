package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.EmergencyGenerator;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.EmergencyGeneratorVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * emergencyGenerator 操作类
 */
@Service
@Transactional(readOnly = true)
public class EmergencyGeneratorService {

    private Logger logger = LoggerFactory.getLogger(EmergencyGeneratorService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 保存emergencyGenerator
     *
     * @param emergencyGenerator
     */
    @Transactional(rollbackFor = Exception.class)
    public EmergencyGeneratorVo save(String sn, EmergencyGenerator emergencyGenerator, Long timeStamp) {

        EmergencyGeneratorVo emergencyGeneratorVo = new EmergencyGeneratorVo();

        emergencyGeneratorVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        emergencyGeneratorVo.setSpeed(emergencyGenerator.getSpeed());
        emergencyGeneratorVo.setPower(emergencyGenerator.getPower());
        emergencyGeneratorVo.setPowerFactor(emergencyGenerator.getPowerFactor());
        emergencyGeneratorVo.setVoltageL1L2(emergencyGenerator.getVoltageL1L2());
        emergencyGeneratorVo.setVoltageL2L3(emergencyGenerator.getVoltageL2L3());
        emergencyGeneratorVo.setVoltageL3L1(emergencyGenerator.getVoltageL3L1());
        emergencyGeneratorVo.setCurrentL1(emergencyGenerator.getCurrentL1());
        emergencyGeneratorVo.setCurrentL2(emergencyGenerator.getCurrentL2());
        emergencyGeneratorVo.setCurrentL3(emergencyGenerator.getCurrentL3());
        emergencyGeneratorVo.setFrequencyL1(emergencyGenerator.getFrequencyL1());
        emergencyGeneratorVo.setFrequencyL2(emergencyGenerator.getFrequencyL2());
        emergencyGeneratorVo.setFrequencyL3(emergencyGenerator.getFrequencyL3());
        emergencyGeneratorVo.setTempU(emergencyGenerator.getTempU());
        emergencyGeneratorVo.setTempV(emergencyGenerator.getTempV());
        emergencyGeneratorVo.setTempW(emergencyGenerator.getTempW());

        logger.info(" emergencyGenerator开始保存到hbase，---{}", JSONObject.toJSONString(emergencyGeneratorVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "emergency_generator"), emergencyGeneratorVo);

        return emergencyGeneratorVo;
    }
}
