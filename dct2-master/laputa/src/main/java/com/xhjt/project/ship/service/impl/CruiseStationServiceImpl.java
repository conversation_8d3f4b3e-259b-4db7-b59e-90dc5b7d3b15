package com.xhjt.project.ship.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.exception.CustomException;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.mapper.CruiseMapper;
import com.xhjt.project.ship.mapper.CruiseStationMapper;
import com.xhjt.project.ship.mapper.ShipMapper;
import com.xhjt.project.ship.service.CruiseStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 航次站位管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CruiseStationServiceImpl implements CruiseStationService {

    @Autowired
    private CruiseStationMapper cruiseStationMapper;

    @Autowired
    private CruiseMapper cruiseMapper;

    @Autowired
    private ShipMapper shipMapper;


    @Override
    @DataScope(deptAlias = "d")
    public List<CruiseStationEntity> selectNewCruiseStationList(CruiseStationEntity cruiseStation) {
        return cruiseStationMapper.selectNewCruiseStationList(cruiseStation);
    }

    @Override
    public int addNewCruise(JSONObject cruiseStationEntity) {
        CruiseStationEntity cruiseStation = new CruiseStationEntity();
        String shipName = "";
        String cruiseName = "";
        List<HashMap> editRequiredForm = (List<HashMap>) cruiseStationEntity.get("editRequiredForm");
        for(HashMap hashMap : editRequiredForm){
            if(hashMap.get("property").equals("shipName")){
                shipName = hashMap.get("value")+"";
            }
            else if(hashMap.get("property").equals("cruiseName")){
                cruiseName = hashMap.get("value")+"";
             }
        }
        List<HashMap> editNoRequiredForm = (List<HashMap>) cruiseStationEntity.get("editNoRequiredForm");
        for(HashMap hashMap : editNoRequiredForm) {
            if(hashMap.get("property").equals("shipName")){
                shipName = hashMap.get("value")+"";
            }
            else if(hashMap.get("property").equals("cruiseName")){
                cruiseName = hashMap.get("value")+"";
            }
        }
        ShipEntity shipEntity = new ShipEntity();
        shipEntity.setName(shipName);
        shipEntity.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<ShipEntity> ships =  shipMapper.selectShipByName(shipEntity);
        if (ships==null || ships.size()<=0){
            throw new CustomException("sn数据不存在");
        }
        String sn = ships.get(0).getSn();
        cruiseStation.setSn(sn);

        CruiseEntity cruise1 = new CruiseEntity();
        cruise1.setCruiseName(cruiseName);
        cruise1.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<CruiseEntity> cruises = cruiseMapper.selectAllNewCruise(cruise1);
        if (cruises==null || cruises.size()<=0){
            throw new CustomException("cruises数据不存在");
        }
        Long cruiseId = cruises.get(0).getCruiseId();
        cruiseStation.setCruiseId(cruiseId);

        cruiseStation.setStationCode(cruiseStationEntity.get("code")+"");
        List<HashMap> list = (List) cruiseStationEntity.get("editNoRequiredForm");
        List<HashMap> list2 = (List) cruiseStationEntity.get("editRequiredForm");
        for(int i = 0;i < list2.size();i++){
            list.add(list2.get(i));
        }

        List<HashMap> res = new ArrayList<>();
        for(int i = 0;i < list.size();i++){
            HashMap<String,Object> map = new HashMap();
            if(list.get(i).get("value")!=null && !list.get(i).get("value").equals("null") && !list.get(i).get("value").equals("")){
                map.put("property",list.get(i).get("property")+"");
                map.put("value",list.get(i).get("value"));
                res.add(map); //moduleJSon(List<Map>)
            }
        }

        String createBy = SecurityUtils.getUsername();

        Map map = res.stream().collect(Collectors.toMap(p->p.get("property"), p->p.get("value")));//moduleJSon(Map)
        map.put("deptId",SecurityUtils.getLoginUser().getUser().getDeptId());
        map.put("createBy",createBy);
        map.put("createTime",new Date());
        map.put("sn",sn);
        map.put("cruiseCode",cruises.get(0).getCode());
        map.put("cruiseId",cruiseId);
        cruiseStation.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        cruiseStation.setStationCode(map.get("stationCode")+"");
        String s = JSONObject.toJSONString(map);
        cruiseStation.setModuleJson(s);
        return cruiseStationMapper.addNewCruiseStation(cruiseStation);
    }

    @Override
    public int deleteNewCruiseStationById(Long id) {
        CruiseStationEntity cruiseStation = new CruiseStationEntity();
        cruiseStation.setId(id);
        return cruiseStationMapper.deleteNewCruiseStationById(cruiseStation);
    }

    @Override
    public CruiseStationEntity selectNewCruiseStationById(Long id) {
        CruiseStationEntity cruiseStation = new CruiseStationEntity();
        cruiseStation.setId(id);
        return cruiseStationMapper.selectNewCruiseStationById(cruiseStation);
    }

    @Override
    public int updateNewCruiseStation(CruiseStationEntity cruiseStation) {
        return cruiseStationMapper.updateNewCruiseStation(cruiseStation);
    }

    @Override
    public CruiseStationEntity selectNewCruiseStationByName(CruiseStationEntity cruiseStation) {
        return cruiseStationMapper.selectNewCruiseStationByName(cruiseStation);
    }
}
