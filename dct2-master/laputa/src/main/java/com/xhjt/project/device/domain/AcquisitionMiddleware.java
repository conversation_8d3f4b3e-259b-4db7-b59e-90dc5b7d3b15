package com.xhjt.project.device.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;


/**
 * 采集中间件
 *
 * <AUTHOR>
 */
public class AcquisitionMiddleware extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 机器MAC地址
     */
    private String mac;

    /**
     * 设备外网IP
     */
    private String ip;

    /**
     * 设备开放端口
     */
    private Integer port;

    /**
     * TCP连接状态
     */
    private Integer connectStatus;

    /**
     * 是否启用，来自设备
     */
    private Integer enable;

    /**
     * 快照传输状态
     */
    private Integer transferStatus;

    /**
     * 模块型号
     */
    private String moduleModel;

    /**
     * 绑定设备的标识
     * @return
     */
    private Integer deviceStatus;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 船只名称
     */
    private String shipName;

    /**
     * 船只
     */
    private String sn;

    private Long deptId;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getDeviceStatus() {
        return deviceStatus;
    }

    public void setDeviceStatus(Integer deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public String getModuleModel() {
        return moduleModel;
    }

    public void setModuleModel(String moduleModel) {
        this.moduleModel = moduleModel;
    }

    public Integer getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(Integer transferStatus) {
        this.transferStatus = transferStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Integer getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(Integer connectStatus) {
        this.connectStatus = connectStatus;
    }

    public Integer getEnable() {
        return enable == null ? 2 : enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }
}
