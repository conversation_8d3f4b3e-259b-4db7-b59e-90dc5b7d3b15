package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.CompressedAir;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.CompressedAirVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * CompressedAir 操作类
 */
@Service
@Transactional(readOnly = true)
public class CompressedAirService {

    private Logger logger = LoggerFactory.getLogger(CompressedAirService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 保存compressedAir
     *
     * @param compressedAir
     */
    @Transactional(rollbackFor = Exception.class)
    public CompressedAirVo save(String sn, CompressedAir compressedAir, Long timeStamp) {

        CompressedAirVo compressedAirVo = new CompressedAirVo();

        compressedAirVo.setId(hBaseDaoUtil.getRowKey(timeStamp));

        compressedAirVo.setCaPress(compressedAir.getCaPress());
        compressedAirVo.setMaPress1(compressedAir.getMaPress1());
        compressedAirVo.setMaPress2(compressedAir.getMaPress2());
        compressedAirVo.setAaPress(compressedAir.getAaPress());
        compressedAirVo.setAirPressure1(compressedAir.getAirPressure1());
        compressedAirVo.setAirPressure2(compressedAir.getAirPressure2());
        compressedAirVo.setAirPressure3(compressedAir.getAirPressure3());

        logger.info(" compressedAir开始保存到hbase，---{}", JSONObject.toJSONString(compressedAirVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "compressed_air"), compressedAirVo);

        return compressedAirVo;
    }
}
