package com.xhjt.project.monitor.controller;

import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.monitor.domain.ReceiveLog;
import com.xhjt.project.monitor.service.IReceiveLogService;
import com.xhjt.project.ship.service.ShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 接收数据记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/receiveLog")
public class ReceiveLogController extends BaseController {
    @Autowired
    private IReceiveLogService receiveLogService;

    @Autowired
    private ShipService shipService;

    @PreAuthorize("@ss.hasPermi('monitor:receiveLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReceiveLog receiveLog) {
        startPage();
        List<ReceiveLog> list = receiveLogService.selectList(receiveLog);
        return getDataTable(list);
    }

    @Log(title = "接收数据记录", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:receiveLog:export')")
    @GetMapping("/export")
    public AjaxResult export(ReceiveLog receiveLog) {
        List<ReceiveLog> list = receiveLogService.selectList(receiveLog);
        ExcelUtil<ReceiveLog> util = new ExcelUtil<ReceiveLog>(ReceiveLog.class);
        return util.exportExcel(list, "接收数据记录");
    }

    @PreAuthorize("@ss.hasPermi('monitor:receiveLog:remove')")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(receiveLogService.deleteByIds(ids));
    }


    @PreAuthorize("@ss.hasPermi('monitor:receiveLog:query')")
    @GetMapping("/query/{startTime}/{endTime}/{sn}")
    public TableDataInfo query(@PathVariable("startTime") Long startTime, @PathVariable("endTime") Long endTime,
                               @PathVariable("sn") String sn) {

        List<ReceiveLog> list = receiveLogService.selectListByRecordTime(startTime, endTime, sn);
        return getDataTable(list);
    }


    @PreAuthorize("@ss.hasPermi('monitor:receiveLog:inquiry')")
    @GetMapping("/inquiry/{sn}")
    public TableDataInfo inquiry(@PathVariable("sn") String sn) {

        List<Map<Long, Integer>> list = receiveLogService.selectListForMinutes(sn);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('monitor:receiveLog:inquiry')")
    @GetMapping("/select")
    public TableDataInfo select() {
        List<Map<Long, Integer>> list = receiveLogService.queryEngineRoomForMinutes();
        return getDataTable(list);
    }


}
