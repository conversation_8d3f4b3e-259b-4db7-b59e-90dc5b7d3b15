package com.xhjt.project.device.controller;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.dctcore.commoncore.domain.device.DeviceAttributeEntity;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.device.service.DeviceAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备属性 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/deviceAttribute")
public class DeviceAttributeController extends BaseController {

    @Autowired
    private DeviceAttributeService deviceAttributeService;

    /**
     * 获取设备属性列表
     */
    @PreAuthorize("@ss.hasPermi('device:attribute:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceAttributeEntity deviceAttribute) {
        startPage();
        List<DeviceAttributeEntity> list = deviceAttributeService.selectDeviceAttributeList(deviceAttribute);
        return getDataTable(list);
    }

    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('device:attribute:export')")
    @GetMapping("/export")
    public AjaxResult export(DeviceAttributeEntity deviceAttribute) {
        List<DeviceAttributeEntity> list = deviceAttributeService.selectDeviceAttributeList(deviceAttribute);
        ExcelUtil<DeviceAttributeEntity> util = new ExcelUtil<DeviceAttributeEntity>(DeviceAttributeEntity.class);
        return util.exportExcel(list, "设备数据");
    }

    /**
     * 获取设备属性列表
     */
    @PreAuthorize("@ss.hasPermi('device:attribute:list')")
    @GetMapping("/listByType")
    public AjaxResult listByType(DeviceAttributeEntity deviceAttribute) {
        List<DeviceAttributeEntity> list = deviceAttributeService.selectDeviceAttributeList(deviceAttribute);
        return AjaxResult.success(list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('device:attribute:query')")
    @GetMapping(value = "/{deviceAttributeId}")
    public AjaxResult getInfo(@PathVariable Long deviceAttributeId) {
        return AjaxResult.success(deviceAttributeService.selectDeviceAttributeById(deviceAttributeId));
    }

    /**
     * 新增设备属性
     */
    @PreAuthorize("@ss.hasPermi('device:attribute:add')")
    @Log(title = "设备新增", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DeviceAttributeEntity deviceAttribute) {
        deviceAttribute.setCreateBy(SecurityUtils.getUsername());
        return toAjax(deviceAttributeService.addDeviceAttribute(deviceAttribute));
    }

    /**
     * 修改设备属性
     */
    @PreAuthorize("@ss.hasPermi('device:attribute:edit')")
    @Log(title = "设备修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DeviceAttributeEntity deviceAttribute) {
        deviceAttribute.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(deviceAttributeService.updateDeviceAttribute(deviceAttribute));
    }

    /**
     * 删除设备属性
     */
    @PreAuthorize("@ss.hasPermi('device:attribute:remove')")
    @Log(title = "设备删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceAttributeIds}")
    public AjaxResult remove(@PathVariable Long[] deviceAttributeIds) {
        return toAjax(deviceAttributeService.deleteDeviceAttributeByIds(deviceAttributeIds));
    }

}
