package com.xhjt.project.hbase.service.engineroom;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.engineroom.vo.TunnelThrusterVo;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * 侧推数据 操作类
 */
@Service
@Transactional(readOnly = true)
public class TunnelThrusterService {
    private Logger logger = LoggerFactory.getLogger(FuelOilService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * @param tunnelThrusterVo
     */
    @Transactional(rollbackFor = Exception.class)
    public TunnelThrusterVo save(String sn, TunnelThrusterVo tunnelThrusterVo, Long timeStamp) {
        tunnelThrusterVo.setId(hBaseDaoUtil.getRowKey(timeStamp));
        logger.info("tunnelThrusterVo开始保存到hbase，---{}", JSONObject.toJSONString(tunnelThrusterVo));
        hBaseDaoUtil.save(hBaseDaoUtil.getTableName(sn, "tunnel_thruster"), tunnelThrusterVo);
        return tunnelThrusterVo;
    }
}
