package com.xhjt.framework.init;

import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.project.device.service.TransferAttributeService;
import com.xhjt.project.ship.service.ShipService;
import com.xhjt.project.snapshot.utils.SnapshotUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/5 23:55
 */
@Component
@Order(0)
public class SystemLoad implements CommandLineRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void run(String... args) throws Exception {
        logger.info("系统基础功能初始化开始...");

        // 更新 船只信息
        ShipService shipService = SpringUtils.getBean(ShipService.class);
        shipService.renewShip4Redis();

        // 更新 传输属性
        TransferAttributeService transferAttributeService = SpringUtils.getBean(TransferAttributeService.class);
        transferAttributeService.renewAll();

        // 缓存所有的快照信息
        SnapshotUtil.addAllInfo();

        logger.info("系统基础功能初始化结束...");
    }
}
