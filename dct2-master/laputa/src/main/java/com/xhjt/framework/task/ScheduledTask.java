package com.xhjt.framework.task;

import com.xhjt.project.typhoon.service.impl.TyphoonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("scheduledTask")
public class ScheduledTask {

    private Logger logger = LoggerFactory.getLogger(ScheduledTask.class);

    @Autowired
    private TyphoonService typhoonService;

    public void multipleParams(String s, <PERSON><PERSON><PERSON> b, Long l, Double d, Integer i) {
        logger.info("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i);
    }

    public void haveParams(String params) {
        logger.info("执行有参方法：{}", params);
    }

    public void noParams() {
        logger.info("执行无参方法");
    }

    /**
     * 检查传输数据是否有中断
     */
    public void testingGpsData() throws Exception {
        logger.info("执行检查传输数据的方法");
        int[] checkTime = {60, 30, 10};
    }

    /**
     * 检查视频快照是否有中断
     */
    public void testingVideo() {
        logger.info("执行检查视频快照的方法");
    }

    /**
     * 获取台风信息
     */
    public void obtainTyphoon() {
        logger.info("执行获取台风信息的方法");
        typhoonService.obtainTyphoon();
    }

}
