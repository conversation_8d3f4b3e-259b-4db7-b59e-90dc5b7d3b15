<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.snapshot.mapper.SnapshotChannelMapper">

    <resultMap type="SnapshotChannelEntity" id="SnapshotChannelResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="shipName" column="ship_name"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="operateTime" column="operate_time"/>
        <result property="directory" column="directory"/>
        <result property="resolvingPower" column="resolving_power"/>
        <result property="compartment" column="compartment"/>
        <result property="cost" column="cost"/>
        <result property="trStatus" column="tr_status"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="fileName" column="file_name"/>
        <result property="storageTime" column="storage_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectSnapshotChannelVo">
        SELECT DISTINCT
            c.id AS id,
            c.dept_id,
            c.sn,
            s.name ship_name,
            c.CODE AS CODE,
            c.NAME AS NAME,
            c.address AS address,
            /*快照时间*/
            l.operate_time AS operate_time,
            /*快照图片路径*/
            l.DIRECTORY AS DIRECTORY,
            /*快照存储状态*/
            tr.STATUS AS tr_status,
            /*快照传输状态*/
            tr.transfer_status AS transfer_status,
            /*分辨率*/
            tr.resolving_power AS resolving_power,
            /*传输间隔*/
            tr.compartment AS compartment,
            /*优先级*/
            tr.cost AS cost,
            /*快照图片名称*/
            l.file_name AS file_name,
            c.storage_time AS storage_time,
            c.create_by AS create_by,
            c.create_time AS create_time,
            c.update_by AS update_by,
            c.update_time AS update_time,
            tr.connect_status AS status
        FROM
            snapshot_channel c
            LEFT JOIN ship s ON c.sn = s.sn
            LEFT JOIN sys_dept d ON c.dept_id = d.dept_id
            LEFT JOIN snapshot_log l ON c.CODE = l.channel_code
            LEFT JOIN snapshot_transfer tr ON c.`code` = tr.channel_code
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            (
            ( l.operate_time, c.CODE ) IN ( SELECT max( operate_time ), channel_code FROM snapshot_log GROUP BY channel_code )
            OR l.channel_code IS NULL
            )
            <if test="sn !=null and sn != ''">
                AND c.sn = #{sn}
            </if>
            <if test="id != null">
                AND c.id = #{id}
            </if>
            <if test="code !=null and code != ''">
                AND c.code = #{code}
            </if>
            <if test="name !=null and name != ''">
                AND c.name = #{name}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (sc.dept_id = #{deptId} OR sc.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
        </where>
    </sql>

    <select id="selectSnapshotChannel" parameterType="SnapshotChannelEntity" resultMap="SnapshotChannelResult">
        <include refid="selectSnapshotChannelVo"/>
        <include refid="sqlwhereSearch"/>
    </select>


    <select id="selectSnapshotChannelList" parameterType="SnapshotChannelEntity" resultMap="SnapshotChannelResult">
        <include refid="selectSnapshotChannelVo"/>
        <where>
            (
            ( l.operate_time, c.CODE ) IN ( SELECT max( operate_time ), channel_code FROM snapshot_log GROUP BY channel_code )
            OR l.channel_code IS NULL
            )
            <if test="deptId != null and deptId != 0">
                AND (c.dept_id = #{deptId} OR c.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                AND c.sn = #{sn}
            </if>
            <if test="code !=null and code != ''">
                AND c.code = #{code}
            </if>
            <if test="name != null and name != ''">
                AND c.name like concat('%', #{name}, '%')
            </if>
            <if test="address != null and address != ''">
                AND c.address like concat('%', #{address}, '%')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addSnapshotChannel" parameterType="SnapshotChannelEntity">
        insert into snapshot_channel (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="code != null and code != ''">code,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="address != null and address != '' ">address,</if>
        <if test="storageTime != null ">storage_time,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="name != null and name != '' ">#{name},</if>
        <if test="address != null and address != '' ">#{address},</if>
        <if test="storageTime != null ">#{storageTime},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateSnapshotChannel" parameterType="SnapshotChannelEntity">
        update snapshot_channel
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="storageTime != null ">storage_time = #{storageTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteSnapshotChannelById" parameterType="Long">
        delete from snapshot_channel where id = #{id}
    </delete>

    <delete id="deleteSnapshotChannelByCode" parameterType="String">
        delete from snapshot_channel where code = #{code}
    </delete>

</mapper>
