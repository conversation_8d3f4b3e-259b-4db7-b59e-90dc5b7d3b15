<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.snapshot.mapper.SnapshotVideoMapper">

    <resultMap type="SnapshotVideoEntity" id="ChannelVideoResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="channelCode" column="channel_code"/>
    </resultMap>

    <sql id="selectChannelVo">
        select id, dept_id, sn, channel_code
        from snapshot_video
    </sql>

    <select id="selectSnapshotVideo" parameterType="SnapshotVideoEntity" resultMap="ChannelVideoResult">
        <include refid="selectChannelVo"/>
        <where>
            <if test="deptId != null">dept_id = #{deptId}</if>
        </where>
    </select>

    <insert id="insertSnapshotVideo" parameterType="SnapshotVideoEntity">
        insert into snapshot_video (
        <if test="deptId != null">dept_id,</if>
        <if test="sn != null and sn != ''">sn,</if>
        <if test="channelCode != null and channelCode != ''">channel_code,</if>
        )values(
        <if test="deptId != null">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
        )
    </insert>

    <update id="updateSnapshotVideo" parameterType="SnapshotVideoEntity">
        update snapshot_video
        <set>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
        </set>
        <where>
            <if test="deptId != null">and dept_id = #{deptId} </if>
        </where>
    </update>

</mapper>
