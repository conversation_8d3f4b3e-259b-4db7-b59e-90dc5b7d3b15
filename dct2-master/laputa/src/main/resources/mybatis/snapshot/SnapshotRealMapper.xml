<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.snapshot.mapper.SnapshotRealMapper">

    <resultMap type="SnapshotRealEntity" id="ChannelRealResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="type" column="type"/>
        <result property="mark" column="mark"/>
        <result property="status" column="status"/>
        <result property="channelCode" column="channel_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectChannelVo">
        select distinct(sr.id), sr.dept_id, sr.sn, sr.mark, sr.channel_code, sc.name, sr.type, sr.status, sr.create_by, sr.create_time, sr.update_by, sr.update_time
        from snapshot_real sr
	      left join ship s on sr.sn = s.sn
	      left join snapshot_channel sc on sr.channel_code = sc.code
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="sn !=null and sn != ''">
                AND sr.sn = #{sn}
            </if>
            <if test="dept_id != null">
                AND sr.dept_id = #{deptId}
            </if>
            <if test="type !=null">
                AND sr.type = #{type}
            </if>
        </where>
    </sql>

    <select id="selectChannelReal" parameterType="SnapshotRealEntity" resultMap="ChannelRealResult">
        <include refid="selectChannelVo"/>
        <include refid="sqlwhereSearch"/>
    </select>

    <select id="selectChannelRealList" parameterType="SnapshotRealEntity" resultMap="ChannelRealResult">
        <include refid="selectChannelVo"/>
        <where>
            <if test="deptId != null">
                AND sr.dept_id = #{deptId}
            </if>
            <if test="type !=null">
                AND sr.type = #{type}
            </if>
            <if test="mark !=null">
                AND sr.mark = #{mark}
            </if>
            <if test="sn !=null and sn != ''">
                AND sr.sn = #{sn}
            </if>
            <if test="channelCode !=null and channelCode != ''">
                AND sr.channel_code = #{channelCode}
            </if>
            AND sr.status = 1
        </where>
        order by sr.mark
    </select>

    <insert id="addSnapshotReal" parameterType="SnapshotRealEntity">
        insert into snapshot_real (
        <if test="deptId != null">dept_id,</if>
        <if test="sn != null and sn != ''">sn,</if>
        <if test="channelCode != null and channelCode != ''">channel_code,</if>
        <if test="type != null">type,</if>
        <if test="mark != null">mark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="status != null">status,</if>
        create_time
        )values(
        <if test="deptId != null">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
        <if test="type != null ">#{type},</if>
        <if test="mark != null ">#{mark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="status != null">#{status},</if>
        sysdate()
        )
    </insert>

    <update id="editSnapshotReal" parameterType="SnapshotRealEntity">
        update snapshot_real
        <set>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = sysdate(),</if>
        </set>
        <where>
            <if test="deptId != null">and dept_id = #{deptId} </if>
            <if test="type != null">and type = #{type} </if>
            <if test="mark != null">and mark = #{mark} </if>
        </where>
    </update>


    <select id="selectNewest" parameterType="SnapshotRealEntity" resultType="com.xhjt.project.snapshot.domain.SnapshotRealEntity">
        select l.sn, l.channel_code, sc.name channel_name, CONCAT(l.directory,'/',l.file_name) path,l.create_time
        from snapshot_log l
        left join snapshot_channel sc on l.channel_code = sc.code
        left join snapshot_transfer st on l.channel_code = st.channel_code
        <where>
            <if test="sn != null and sn != ''">

                AND l.sn = #{sn}
            </if>
            <if test="deptId != null">
                AND (l.dept_id = #{deptId} OR l.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="channelCode != null and channelCode != ''">
                AND l.channel_code = #{channelCode}
            </if>
        </where>
        and st.status = 1
        order by l.channel_code,l.create_time desc
        limit 1
    </select>

    <select id="selectSnapshotReal" parameterType="SnapshotRealEntity" resultMap="ChannelRealResult">
        <include refid="selectChannelVo"/>
        <where>
            <if test="deptId != null">
                AND sr.dept_id = #{deptId}
            </if>
            <if test="type !=null">
                AND sr.type = #{type}
            </if>
            <if test="mark !=null">
                AND sr.mark = #{mark}
            </if>
            AND sr.status = 1
        </where>
    </select>
</mapper>
