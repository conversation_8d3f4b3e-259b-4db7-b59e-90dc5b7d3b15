<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.snapshot.mapper.SnapshotLogMapper">

    <resultMap type="SnapshotLogEntity" id="SnapshotLogResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="shipName" column="ship_name"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="operateTime" column="operate_time"/>
        <result property="fileName" column="file_name"/>
        <result property="directory" column="directory"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="SnapshotVideoEntity" id="SnapshotVideoResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="channelCode" column="channel_code"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <sql id="selectSnapshotLogVo">
        select l.id, l.dept_id, l.sn, s.name ship_name, l.channel_code, sc.name channel_name, l.operate_time, l.file_name, l.directory
        , l.create_by, l.create_time, l.update_by, l.update_time
        from snapshot_log l
        left join ship s on l.sn = s.sn
        left join snapshot_channel sc on l.channel_code = sc.code
	    left join sys_dept d on l.dept_id = d.dept_id
    </sql>

    <select id="selectSnapshotLog" parameterType="SnapshotLogEntity" resultMap="SnapshotLogResult">
        <include refid="selectSnapshotLogVo"/>
        <where>
            <if test="id !=null">
                and l.id = #{id}
            </if>
        </where>
    </select>


    <select id="selectSnapshotLogList" parameterType="SnapshotLogEntity" resultMap="SnapshotLogResult">
        <include refid="selectSnapshotLogVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (l.dept_id = #{deptId} OR l.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                AND l.sn = #{sn}
            </if>
            <if test="channelCode !=null and channelCode != ''">
                and l.channel_code = #{channelCode}
            </if>
            <if test="fileName !=null and fileName != ''">
                and l.file_name = #{fileName}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(l.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(l.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <![CDATA[  and l.channel_code<>'XHJT' ]]>

            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
        order by l.create_time desc
    </select>

    <insert id="addSnapshotLog" parameterType="SnapshotLogEntity">
        insert into snapshot_log (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="channelCode != null and channelCode != ''">channel_code,</if>
        <if test="operateTime != null and operateTime != '' ">operate_time,</if>
        <if test="fileName != null and fileName != '' ">file_name,</if>
        <if test="directory != null and directory != '' ">directory,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
        <if test="operateTime != null and operateTime != '' ">#{operateTime},</if>
        <if test="fileName != null and fileName != '' ">#{fileName},</if>
        <if test="directory != null and directory != ''">#{directory},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateSnapshotLog" parameterType="SnapshotLogEntity">
        update snapshot_log
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="operateTime != null and operateTime != ''">operate_time = #{operateTime},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="directory != null and directory != ''">directory = #{directory},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteSnapshotLogById" parameterType="Long">
        delete from snapshot_log where id = #{id}
    </delete>

    <select id="selectSnapshotVideo" parameterType="com.xhjt.project.snapshot.domain.SnapshotVideoEntity" resultMap="SnapshotLogResult">
        SELECT *
        from snapshot_log
        <where>
            <if test="sn != null and sn != ''">
                AND sn = #{sn}
            </if>
            <if test="deptId != null and deptId != ''">
                AND (dept_id = #{deptId} OR dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="channelCode != null and channelCode != ''">
                AND channel_code = #{channelCode}
            </if>
            <if test="startTime != null and startTime != ''">
                AND concat(UNIX_TIMESTAMP(create_time),'000') >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND #{endTime} >= concat(UNIX_TIMESTAMP(create_time),'000')
            </if>
        </where>
    </select>
</mapper>
