<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.view.mapper.ViewSchemeMapper">

    <resultMap type="ViewSchemeEntity" id="ViewSchemeResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="templateCode" column="template_code"/>
        <result property="pageType" column="page_type"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="siteTitle" column="site_title"/>
        <result property="copyright" column="copyright"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectViewSchemeVo">
        select v.id, v.dept_id, v.sn, v.template_code, v.page_type, v.name, v.status, v.site_title, v.copyright, v.create_by, v.create_time, v.update_by, v.update_time
		from view_scheme v
	      left join sys_dept d on v.dept_id = d.dept_id
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
        </where>
    </sql>

    <select id="selectViewScheme" parameterType="ViewSchemeEntity" resultMap="ViewSchemeResult">
        <include refid="selectViewSchemeVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectViewSchemeList" parameterType="ViewSchemeEntity" resultMap="ViewSchemeResult">
        <include refid="selectViewSchemeVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                and v.sn = #{sn}
            </if>
            <if test="name != null and name != ''">
                AND v.name like concat('%', #{name}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(v.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(v.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="templateCode !=null and templateCode != ''">
                and v.template_code = #{templateCode}
            </if>
            <if test="pageType !=null and pageType != '' ">
                and v.page_type = #{pageType}
            </if>
            <if test="status !=null ">
                and v.status = #{status}
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addViewScheme" parameterType="ViewSchemeEntity" useGeneratedKeys="true" keyProperty="id">
        insert into view_scheme (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="templateCode != null and templateCode != '' ">template_code,</if>
        <if test="pageType != null and pageType != '' ">page_type,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="status != null and status != '' ">status,</if>
        <if test="siteTitle != null and siteTitle != '' ">site_title,</if>
        <if test="copyright != null and copyright != '' ">copyright,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="templateCode != null and templateCode != ''">#{templateCode},</if>
        <if test="pageType != null and pageType != ''">#{pageType},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="siteTitle != null and siteTitle != ''">#{siteTitle},</if>
        <if test="copyright != null and copyright != ''">#{copyright},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateViewScheme" parameterType="ViewSchemeEntity">
        update view_scheme
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="templateCode != null and templateCode != ''">template_code = #{templateCode},</if>
            <if test="pageType != null and pageType != '' ">page_type = #{pageType},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="siteTitle != null and siteTitle != ''">site_title = #{siteTitle},</if>
            <if test="copyright != null and copyright != ''">copyright = #{copyright},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteViewSchemeById" parameterType="Long">
        delete from view_scheme where id = #{id}
    </delete>

    <delete id="deleteViewSchemeByIds" parameterType="Long">
        delete from view_scheme where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>