<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.view.mapper.ViewTemplateMapper">

    <resultMap type="ViewTemplateEntity" id="TemplateResult">
        <id property="id" column="id"/>
        <result property="pageType" column="page_type"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="status" column="status"/>
        <result property="moduleJson" column="module_json"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTemplateVo">
        select id, page_type, name, code, status, module_json, create_by, create_time, update_by, update_time
		from view_template
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
        </where>
    </sql>

    <select id="selectTemplate" parameterType="ViewTemplateEntity" resultMap="TemplateResult">
        <include refid="selectTemplateVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectTemplateList" parameterType="ViewTemplateEntity" resultMap="TemplateResult">
        <include refid="selectTemplateVo"/>
        <where>
            <if test="pageType !=null and pageType != '' ">
                and page_type = #{pageType}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
            <if test="status !=null ">
                and status = #{status}
            </if>
            <if test="moduleJson != null and moduleJson != ''">
                AND module_json like concat('%', #{moduleJson}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <insert id="addTemplate" parameterType="ViewTemplateEntity" useGeneratedKeys="true" keyProperty="id">
        insert into view_template (
        <if test="pageType != null and pageType != '' ">page_type,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="code != null and code != '' ">code,</if>
        <if test="status != null ">status,</if>
        <if test="moduleJson != null and moduleJson != '' ">module_json,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="pageType != null and pageType != '' ">#{pageType},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="status != null">#{status},</if>
        <if test="moduleJson != null and moduleJson != ''">#{moduleJson},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateTemplate" parameterType="ViewTemplateEntity">
        update view_template
        <set>
            <if test="pageType != null and pageType != '' ">page_type = #{pageType},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="status != null ">status = #{status},</if>
            <if test="moduleJson != null and moduleJson != ''">module_json = #{moduleJson},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteTemplateById" parameterType="Long">
        delete from view_template where id = #{id}
    </delete>

    <delete id="deleteTemplateByIds" parameterType="Long">
        delete from view_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>