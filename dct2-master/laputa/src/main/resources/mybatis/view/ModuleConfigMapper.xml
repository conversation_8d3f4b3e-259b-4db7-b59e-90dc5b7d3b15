<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.view.mapper.ModuleConfigMapper">

    <resultMap type="ModuleConfigEntity" id="ModuleConfigResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="schemeId" column="scheme_id"/>
        <result property="moduleCode" column="module_code"/>
        <result property="moduleName" column="module_name"/>
        <result property="mode" column="mode"/>
        <result property="deviceCode" column="device_code"/>
        <result property="attribute" column="attribute"/>
        <result property="grading" column="grading"/>
        <result property="timeFrame" column="time_frame"/>

        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectModuleConfigVo">
        select m.id, m.dept_id, m.scheme_id, m.module_code, m.module_name, m.mode, m.device_code, m.attribute, m.grading, m.time_frame, m.create_by, m.create_time, m.update_by, m.update_time
		from module_config m
	      left join sys_dept d on m.dept_id = d.dept_id
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and m.id = #{id}
            </if>
        </where>
    </sql>

    <select id="selectModuleConfig" parameterType="ModuleConfigEntity" resultMap="ModuleConfigResult">
        <include refid="selectModuleConfigVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectModuleConfigList" parameterType="ModuleConfigEntity" resultMap="ModuleConfigResult">
        <include refid="selectModuleConfigVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (m.dept_id = #{deptId} OR m.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="schemeId !=null and schemeId != ''">
                and m.scheme_id = #{schemeId}
            </if>
            <if test="moduleCode !=null and moduleCode != ''">
                and m.module_code = #{moduleCode}
            </if>
            <if test="mode !=null ">
                and m.mode = #{mode}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(m.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(m.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addModuleConfig" parameterType="ModuleConfigEntity">
        insert into module_config (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="schemeId != null and schemeId != '' ">scheme_id,</if>
        <if test="moduleCode != null and moduleCode != '' ">module_code,</if>
        <if test="moduleName != null and moduleName != '' ">module_name,</if>
        <if test="mode != null and mode != '' ">mode,</if>
        <if test="deviceCode != null and deviceCode != '' ">device_code,</if>
        <if test="attribute != null and attribute != '' ">attribute,</if>
        <if test="grading != null and grading != '' ">grading,</if>
        <if test="timeFrame != null and timeFrame != '' ">time_frame,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="schemeId != null and schemeId != ''">#{schemeId},</if>
        <if test="moduleCode != null and moduleCode != ''">#{moduleCode},</if>
        <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
        <if test="mode != null and mode != ''">#{mode},</if>
        <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
        <if test="attribute != null and attribute != ''">#{attribute},</if>
        <if test="grading != null and grading != ''">#{attribute},</if>
        <if test="timeFrame != null and timeFrame != ''">#{timeFrame},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateModuleConfig" parameterType="ModuleConfigEntity">
        update module_config
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="schemeId != null and schemeId != ''">scheme_id = #{schemeId},</if>
            <if test="moduleCode != null and moduleCode != ''">module_code = #{moduleCode},</if>
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="mode != null and mode != ''">mode = #{mode},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="attribute != null and attribute != ''">attribute = #{attribute},</if>
            <if test="grading != null and grading != ''">grading = #{grading},</if>
            <if test="timeFrame != null and timeFrame != ''">time_frame = #{timeFrame},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteModuleConfigById" parameterType="Long">
        delete from module_config where id = #{id}
    </delete>

    <delete id="deleteModuleConfigByIds" parameterType="Long">
        delete from module_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>