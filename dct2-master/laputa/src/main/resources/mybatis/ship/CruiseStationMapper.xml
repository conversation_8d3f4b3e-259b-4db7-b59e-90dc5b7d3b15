<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.ship.mapper.CruiseStationMapper">

    <resultMap type="CruiseStationEntity" id="CruiseStationResult">
        <id property="id" column="id"/>
        <result property="shipName" column="ship_name"/>
        <result property="sn" column="sn"/>
        <result property="cruiseId" column="cruise_id"/>
        <result property="cruiseCode" column="cruise_code"/>
        <result property="deptId" column="dept_id"/>
        <result property="stationCode" column="station_code"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="moduleJson" column="module_json"/>
    </resultMap>

    <sql id="selectNewCruiseStationVo">
        select cs.id, cs.cruise_id, cs.dept_id, cs.sn, cs.station_code, cs.module_json
		from cruise_station cs
		  left join ship s on cs.sn = s.sn
		  left join cruise c on cs.cruise_id = c.cruise_id
	      left join sys_dept d on cs.dept_id = d.dept_id
    </sql>

    <select id="selectNewCruiseStationList" parameterType="com.xhjt.project.ship.domain.CruiseStationEntity" resultMap="CruiseStationResult">
        <include refid="selectNewCruiseStationVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (cs.dept_id = #{deptId} OR cs.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="cruiseCode != null and cruiseCode != ''">
                AND json_extract(cs.module_json,"$.cruiseCode") = #{cruiseCode}
            </if>
            <if test="sn != null and sn != ''">
                AND cs.sn = #{sn}
            </if>
            <if test="shipName != null and shipName != ''">
                AND json_extract(cs.module_json,"$.shipName") = #{shipName}
            </if>
            <if test="cruiseName !=null and cruiseName != ''">
                AND json_extract(cs.module_json,"$.cruiseName") = #{cruiseName}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                <![CDATA[AND ((FROM_UNIXTIME(json_extract(cs.module_json,"$.planArrivalTime")/1000,'%Y-%m-%d') >= #{beginTime} or FROM_UNIXTIME(json_extract(cs.module_json,"$.planDepartureTime")/1000,'%Y-%m-%d') >= #{beginTime})]]>
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
               <![CDATA[ AND (FROM_UNIXTIME(json_extract(cs.module_json,"$.planArrivalTime")/1000,'%Y-%m-%d') <= #{endTime} or FROM_UNIXTIME(json_extract(cs.module_json,"$.planDepartureTime")/1000,'%Y-%m-%d') <= #{endTime}))]]>
            </if>
        </where>
        order by json_extract(c.module_json,"$.startTime") desc
    </select>

    <select id="selectNewCruiseStationByName" parameterType="CruiseStationEntity" resultMap="CruiseStationResult">
        <include refid="selectNewCruiseStationVo"/>
        <where>
            <if test="stationName != null and stationName != ''">
                json_extract(cs.module_json,"$.stationName") = #{stationName}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (cs.dept_id = #{deptId} OR cs.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
        </where>
    </select>

    <select id="selectCruiseStationNewByFactor" parameterType="CruiseStationEntity" resultMap="CruiseStationResult">
        <include refid="selectNewCruiseStationVo"/>
        <where>
            <if test="id != null and id != ''">
                and cs.id = #{id}
            </if>
            <if test="stationCode != null and stationCode != ''">
                and cs.stationCode = #{stationCode}
            </if>
        </where>
    </select>

    <select id="selectNewCruiseStationById" parameterType="CruiseStationEntity" resultMap="CruiseStationResult">
        <include refid="selectNewCruiseStationVo"/>
        <where>
            <if test="id != null and id != ''">
                and cs.id = #{id}
            </if>
        </where>
    </select>

    <insert id="addNewCruiseStation" parameterType="CruiseStationEntity">
        insert into cruise_station (
        <if test="cruiseId != null ">cruise_id,</if>
        <if test="deptId != null ">dept_id,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="stationCode != null and stationCode != '' ">station_code,</if>
        <if test="moduleJson != null and moduleJson != ''">module_json</if>
        )values(
        <if test="cruiseId != null ">#{cruiseId},</if>
        <if test="deptId != null ">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="stationCode != null and stationCode != ''">#{stationCode},</if>
        <if test="moduleJson != null and moduleJson != ''">#{moduleJson}</if>
        )
    </insert>

    <update id="updateNewCruiseStation" parameterType="CruiseStationEntity">
        update cruise_station
        <set>
            <if test="cruiseId != null and cruiseId != ''">cruise_id = #{cruiseId},</if>
            <if test="stationCode != null and stationCode != ''">station_code = #{stationCode},</if>
            <if test="moduleJson != null and moduleJson != ''">module_json = #{moduleJson},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteNewCruiseStationById" parameterType="Long">
        delete from cruise_station where id = #{id}
    </delete>

</mapper>
