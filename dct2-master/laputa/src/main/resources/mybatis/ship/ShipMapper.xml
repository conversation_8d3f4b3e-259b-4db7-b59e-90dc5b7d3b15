<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.ship.mapper.ShipMapper">

    <resultMap type="ShipEntity" id="ShipResult">
        <id property="shipId" column="ship_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="name" column="name"/>
        <result property="mmsi" column="mmsi"/>
        <result property="callSign" column="call_sign"/>
        <result property="imo" column="imo"/>
        <result property="sn" column="sn"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="moduleJson" column="module_json"/>
    </resultMap>

    <sql id="selectNewShipVo">
        select s.ship_id, s.dept_id, s.name, s.sn, s.status, s.module_json
		from ship s left join sys_dept d on s.dept_id = d.dept_id
    </sql>

    <select id="selectNewShipList" parameterType="ShipEntity" resultMap="ShipResult">
        <include refid="selectNewShipVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="label !=null and label != ''">
                AND (json_extract(module_json,"$.name") like CONCAT('%',#{label},'%') or
                json_extract(module_json,"$.mmsi") like CONCAT('%',#{label},'%') or
                json_extract(module_json,"$.callSign") like CONCAT('%',#{label},'%') or
                json_extract(module_json,"$.imo") like CONCAT('%',#{label},'%'))
            </if>
        </where>

    </select>

    <insert id="addNewShip" parameterType="ShipEntity" >
        insert into ship (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="status != null ">status,</if>
        <if test="moduleJson != null and moduleJson != ''">module_json</if>
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="sn != null and sn != '' ">#{sn},</if>
        <if test="status != null ">#{status},</if>
        <if test="moduleJson != null and moduleJson != ''">#{moduleJson}</if>
        )
    </insert>

    <select id="selectNewShip" parameterType="ShipEntity" resultMap="ShipResult">
        <include refid="selectNewShipVo"/>
        <where>
            <if test="shipId != null">
                and s.ship_id = #{shipId}
            </if>
            <if test="sn !=null and sn != ''">
                and s.sn = #{sn}
            </if>
        </where>
    </select>

    <delete id="deleteNewShipById" parameterType="Long">
        delete from ship where ship_id = #{shipId}
    </delete>

    <update id="updateNewShip" parameterType="ShipEntity">
        update ship
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null and status != '-1'">status = #{status},</if>
            <if test="moduleJson != null and moduleJson != ''">module_json = #{moduleJson},</if>
        </set>
        where ship_id = #{shipId}
    </update>

    <select id="selectNewShipByFactor" parameterType="ShipEntity" resultMap="ShipResult">
        <include refid="selectNewShipVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="name !=null and name != ''">
                or s.name = #{name}
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
            <if test="shipId !=null and shipId != ''">
                having s.ship_id != #{shipId}
            </if>
        </where>
    </select>

    <select id="selectShipByName" parameterType="ShipEntity" resultMap="ShipResult">
        <include refid="selectNewShipVo"/>
        <where>
            <if test="name != null and name != ''">
                and s.name = #{name}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
        </where>
    </select>

    <select id="selectNewShipByShipName" parameterType="string" resultMap="ShipResult">
        <include refid="selectNewShipVo"/>
        where name = #{shipName}
    </select>

    <select id="selectAllNewShip" parameterType="ShipEntity" resultMap="ShipResult">
        <include refid="selectNewShipVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
        </where>
    </select>
</mapper>
