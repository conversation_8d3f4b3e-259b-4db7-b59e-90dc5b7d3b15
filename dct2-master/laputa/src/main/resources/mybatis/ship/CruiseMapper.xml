<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.ship.mapper.CruiseMapper">

    <resultMap type="CruiseEntity" id="CruiseResult">
        <id property="cruiseId" column="cruise_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="shipName" column="ship_name"/>
        <result property="sn" column="sn"/>
        <result property="captain" column="captain"/>
        <result property="code" column="code"/>
        <result property="startTime" column="start_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="totalDays" column="total_days"/>
        <result property="historyMileage" column="history_mileage"/>
        <result property="startPort" column="start_port"/>
        <result property="endPort" column="end_port"/>
        <result property="seaArea" column="sea_area"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="moduleJson" column="module_json"/>
    </resultMap>

    <sql id="selectNewCruiseVo">
        select c.cruise_id, c.dept_id, c.sn, c.code, c.module_json
		from cruise c
		  left join ship s on c.sn = s.sn
	      left join sys_dept d on c.dept_id = d.dept_id
    </sql>


    <select id="selectNewCruiseList" parameterType="com.xhjt.project.ship.domain.CruiseEntity" resultMap="CruiseResult">
        <include refid="selectNewCruiseVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (c.dept_id = #{deptId} OR c.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                AND c.sn = #{sn}
            </if>
            <if test="code !=null and code != ''">
                AND c.code = #{code}
            </if>
            <if test="cruiseName !=null and cruiseName != ''">
                AND json_extract(c.module_json,"$.cruiseName") = #{cruiseName}
            </if>
            <if test="shipName !=null and shipName != ''">
                AND json_extract(c.module_json,"$.shipName") = #{shipName}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and FROM_UNIXTIME(json_extract(c.module_json,"$.startTime")/1000,'%Y-%m-%d') &gt;= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and FROM_UNIXTIME(json_extract(c.module_json,"$.endTime")/1000,'%Y-%m-%d') &lt;= #{endTime}
            </if>
        </where>
        order by json_extract(c.module_json,"$.startTime") desc
    </select>

    <select id="selectCruiseByCruiseName" parameterType="CruiseEntity" resultMap="CruiseResult">
        <include refid="selectNewCruiseVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (c.dept_id = #{deptId} OR c.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="cruiseName != null and cruiseName != ''">
                and json_extract(c.module_json,"$.cruiseName") = #{cruiseName}
            </if>
            <if test="shipName != null and shipName != ''">
                and json_extract(c.module_json,"$.shipName") = #{shipName}
            </if>
        </where>
    </select>

    <select id="selectNewCruiseBySn" parameterType="String" resultMap="CruiseResult">
        select c.cruise_id, c.dept_id, c.sn,  c.code,
        json_extract(c.module_json,"$.shipName") ship_name,
        json_extract(c.module_json,"$.startTime") start_time,
        json_extract(c.module_json,"$.endTime") finish_time,
        json_extract(c.module_json,"$.seaArea") sea_area,
        json_extract(c.module_json,"$.createBy") create_by,
        json_extract(c.module_json,"$.createTime") create_time,
        json_extract(c.module_json,"$.updateBy") update_by,
        json_extract(c.module_json,"$.updateTime") update_time
		from cruise c
		  left join ship s on c.sn = s.sn
		  where s.sn = #{sn}
		  order by json_extract(c.module_json,"$.startTime") desc
    </select>

    <select id="selectNewCruiseById" parameterType="com.xhjt.project.ship.domain.CruiseEntity" resultMap="CruiseResult">
        <include refid="selectNewCruiseVo"/>
        <where>
            <if test="cruiseId != null and cruiseId != ''">
                and c.cruise_id = #{cruiseId}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (c.dept_id = #{deptId} OR c.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                and c.sn = #{sn}
            </if>
            <if test="code !=null and code != ''">
                and c.code = #{code}
            </if>
        </where>
    </select>

    <select id="selectAllNewCruise" parameterType="com.xhjt.project.ship.domain.CruiseEntity" resultMap="CruiseResult">
        <include refid="selectNewCruiseVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (c.dept_id = #{deptId} OR c.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="shipName !=null and shipName != ''">
                AND json_extract(c.module_json,"$.shipName") = #{shipName}
            </if>
            <if test="cruiseName !=null and cruiseName != ''">
                AND json_extract(c.module_json,"$.cruiseName") = #{cruiseName}
            </if>
        </where>
    </select>

    <select id="selectCruiseNewByFactor" parameterType="CruiseEntity" resultMap="CruiseResult">
        <include refid="selectNewCruiseVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (c.dept_id = #{deptId} OR c.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="cruiseId != null and cruiseId != ''">
                and c.cruise_id = #{cruiseId}
            </if>
            <if test="sn != null and sn != ''">
                and c.sn = #{sn}
            </if>
            <if test="code != null and code != ''">
                and c.code = #{code}
            </if>
            <if test="shipName !=null and shipName != ''">
                AND json_extract(c.module_json,"$.shipName") = #{shipName}
            </if>
            <if test="cruiseName !=null and cruiseName != ''">
                AND json_extract(c.module_json,"$.cruiseName") = #{cruiseName}
            </if>
        </where>
    </select>

    <insert id="addNewCruise" parameterType="CruiseEntity">
        insert into cruise(
            <if test="code != null and code != ''">code,</if>
            <if test="deptId != null and deptId != 0">dept_id,</if>
            <if test="sn != null and sn != ''">sn,</if>
            <if test="moduleJson !=null and moduleJson != ''">module_json</if>
        )values(
        <if test="code != null and code != ''">#{code},</if>
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="moduleJson !=null and moduleJson != ''">#{moduleJson}</if>
        )
    </insert>

    <update id="updateNewCruise" parameterType="CruiseEntity">
        update cruise
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="sn != null and sn != '-1'">sn = #{sn},</if>
            <if test="moduleJson != null and moduleJson != ''">module_json = #{moduleJson}</if>
        </set>
        where cruise_id = #{cruiseId}
    </update>

    <delete id="deleteNewCruiseById" parameterType="CruiseEntity">
         delete from cruise where cruise_id = #{cruiseId}
    </delete>

</mapper>
