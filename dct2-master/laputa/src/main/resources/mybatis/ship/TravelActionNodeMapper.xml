<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.ship.mapper.TravelActionNodeMapper">

    <resultMap type="TravelActionNodeEntity" id="TravelActionNodeResult">
        <id property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="cruiseId" column="cruise_id"/>
        <result property="time" column="time"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="groundRate" column="ground_rate"/>
        <result property="mt4MtrSpeed" column="mt4_mtr_speed"/>
        <result property="mt5MtrSpeed" column="mt5_mtr_speed"/>
        <result property="relationStation" column="relation_station"/>
        <result property="relationStationDistance" column="relation_station_distance"/>
        <result property="actionType" column="action_type"/>

        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTravelActionNodeVo">
        select t.id, t.sn, s.name ship_name, t.cruise_id, c.code cruise_code, t.time, t.longitude, t.latitude,
        t.ground_rate, t.mt4_mtr_speed, t.mt5_mtr_speed,t.relation_station,t.relation_station_distance,t.action_type,
        t.create_by,t.create_time,t.update_by,t.update_time
		from travel_action_node t
		  left join ship s on t.sn = s.sn
		  left join cruise c on t.cruise_id = c.cruise_id
	      left join sys_dept d on s.dept_id = d.dept_id
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="cruiseId !=null">
                and cruise_id = #{cruiseId}
            </if>
            <if test="sn !=null">
                and sn = #{sn}
            </if>
            <if test="relationStation !=null and relationStation != ''">
                and t.relation_station = #{relationStation}
            </if>
        </where>
    </sql>

    <select id="selectTravelActionNode" parameterType="TravelActionNodeEntity" resultMap="TravelActionNodeResult">
        <include refid="selectTravelActionNodeVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectTravelActionNodeList" parameterType="TravelActionNodeEntity" resultMap="TravelActionNodeResult">
        <include refid="selectTravelActionNodeVo"/>
        <where>
            <if test="cruiseId !=null and cruiseId != ''">
                AND t.cruise_id = #{cruiseId}
            </if>
            <if test="sn !=null and sn != ''">
                AND t.sn = #{sn}
            </if>
            <if test="relationStation !=null and relationStation != ''">
                and t.relation_station = #{relationStation}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(t.time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(t.time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
        order by time desc
    </select>

    <insert id="addTravelActionNode" parameterType="TravelActionNodeEntity">
        insert into travel_action_node (
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="cruiseId != null and cruiseId != 0">cruise_id,</if>
        <if test="time != null ">time,</if>
        <if test="longitude != null and longitude != '' ">longitude,</if>
        <if test="latitude != null and latitude != '' ">latitude,</if>
        <if test="groundRate != null ">ground_rate,</if>
        <if test="mt4MtrSpeed != null ">mt4_mtr_speed,</if>
        <if test="mt5MtrSpeed != null ">mt5_mtr_speed,</if>
        <if test="relationStation != null and relationStation != '' ">relation_station,</if>
        <if test="relationStationDistance != null ">relation_station_distance,</if>
        <if test="actionType != null ">action_type,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="sn != null and sn != '' ">#{sn},</if>
        <if test="cruiseId != null and cruiseId != 0">#{cruiseId},</if>
        <if test="time != null ">#{time},</if>
        <if test="longitude != null and longitude != ''">#{longitude},</if>
        <if test="latitude != null and latitude != ''">#{latitude},</if>
        <if test="groundRate != null ">#{groundRate},</if>
        <if test="mt4MtrSpeed != null ">#{mt4MtrSpeed},</if>
        <if test="mt5MtrSpeed != null ">#{mt5MtrSpeed},</if>
        <if test="relationStation != null and relationStation != ''">#{relationStation},</if>
        <if test="relationStationDistance != null ">#{relationStationDistance},</if>
        <if test="actionType != null ">#{actionType},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateTravelActionNode" parameterType="TravelActionNodeEntity">
        update travel_action_node
        <set>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="groundRate != null">ground_rate = #{groundRate},</if>
            <if test="mt4MtrSpeed != null">mt4_mtr_speed = #{mt4MtrSpeed},</if>
            <if test="mt5MtrSpeed != null">mt5_mtr_speed = #{mt5MtrSpeed},</if>
            <if test="relationStation != null and relationStation != ''">relation_station = #{relationStation},</if>
            <if test="relationStationDistance != null">relation_station_distance =
                #{relationStationDistance},
            </if>
            <if test="actionType != null and actionType != ''">action_type = #{actionType},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteTravelActionNodeById" parameterType="Long">
        delete from travel_action_node where id = #{id}
    </delete>

    <delete id="deleteTravelActionNodeByIds" parameterType="Long">
        delete from travel_action_node where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>