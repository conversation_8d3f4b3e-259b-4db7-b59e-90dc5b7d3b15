<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhjt.project.system.mapper.SysBackgroundPicMapper" >
<resultMap id="BackgroundPicResult" type="com.xhjt.project.system.domain.SysBackgroundPic" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="bg_status" property="bgStatus" jdbcType="INTEGER" />
    <result column="sort_num" property="sortNum" jdbcType="BIGINT" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="dept_id" property="deptId" jdbcType="BIGINT" />
</resultMap>
<sql id="selectBackgroundPic" >
    select   d.id, d.type, d.name, d.create_time, d.update_time, d.bg_status, d.sort_num, d.file_name, d.directory,
    d.dept_id from background_pic d
  </sql>
<select id="selectById" parameterType="Long" resultMap="BackgroundPicResult">
    <include refid="selectBackgroundPic"/>
    where id = #{id}
</select>

<select id="selectBackPicList" resultMap="BackgroundPicResult" parameterType="com.xhjt.project.system.domain.SysBackgroundPic">
    <include refid="selectBackgroundPic"/>
    <where>

        <if test="name != null and name != ''">
            AND `name` like concat('%', #{name}, '%')
        </if>
        <if test="type != null">
            AND `type`=#{type}
        </if>
        <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
            and date_format(d.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            and date_format(d.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="bgStatus != null">
            AND bg_status=#{bgStatus}
        </if>
        <if test="sortNum != null">
            AND sort_num like concat('%', #{sortNum}, '%')
        </if>
        <if test="fileName != null and fileName != ''">
            AND fileName like concat('%', #{filename}, '%')
        </if>
        <if test="directory != null and directory != ''">
            AND directory like concat('%', #{directory}, '%')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (dept_id = #{deptId} OR dept_id IN (SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
            (#{deptId},ancestors) ))
        </if>
        ${dataScope}
    </where>
    order by d.sort_num asc
</select>

<select id="selectByPrimaryKey" resultMap="BackgroundPicResult" parameterType="java.lang.Long" >
    select
    <include refid="selectBackgroundPic" />
    from background_pic
    where id = #{id,jdbcType=BIGINT}
</select>
<delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from background_pic
    where id = #{id,jdbcType=BIGINT}
  </delete>
<insert id="insert" parameterType="com.xhjt.project.system.domain.SysBackgroundPic" >
    insert into background_pic (id, type, name,
      create_time, update_time, bg_status,
      sort_num, file_name, directory,
      dept_id)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{bgStatus,jdbcType=INTEGER},
      #{sortNum,jdbcType=BIGINT}, #{fileName,jdbcType=VARCHAR}, #{directory,jdbcType=VARCHAR},
      #{deptId,jdbcType=BIGINT})
  </insert>
<insert id="insertSelective" parameterType="com.xhjt.project.system.domain.SysBackgroundPic" >
    insert into background_pic
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="id != null" >
            id,
        </if>
        <if test="type != null" >
            type,
        </if>
        <if test="name != null" >
            name,
        </if>
        <if test="createTime != null" >
            create_time,
        </if>
        <if test="updateTime != null" >
            update_time,
        </if>
        <if test="bgStatus != null" >
            bg_status,
        </if>
        <if test="sortNum != null" >
            sort_num,
        </if>
        <if test="fileName != null" >
            file_name,
        </if>
        <if test="directory != null" >
            directory,
        </if>
        <if test="deptId != null and deptId != 0" >
            dept_id,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="id != null" >
            #{id,jdbcType=BIGINT},
        </if>
        <if test="type != null" >
            #{type,jdbcType=INTEGER},
        </if>
        <if test="name != null" >
            #{name,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null" >
            #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null" >
            #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="bgStatus != null" >
            #{bgStatus,jdbcType=INTEGER},
        </if>
        <if test="sortNum != null" >
            #{sortNum,jdbcType=BIGINT},
        </if>
        <if test="fileName != null" >
            #{fileName,jdbcType=VARCHAR},
        </if>
        <if test="directory != null" >
            #{directory,jdbcType=VARCHAR},
        </if>
        <if test="deptId != null and deptId!=0" >
            #{deptId,jdbcType=BIGINT},
        </if>
    </trim>
</insert>
<update id="updateByPrimaryKeySelective" parameterType="com.xhjt.project.system.domain.SysBackgroundPic" >
    update background_pic
    <set >
        <if test="type != null" >
            type = #{type,jdbcType=INTEGER},
        </if>
        <if test="name != null" >
            name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null" >
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null" >
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="bgStatus != null" >
            bg_status = #{bgStatus,jdbcType=INTEGER},
        </if>
        <if test="sortNum != null" >
            sort_num = #{sortNum,jdbcType=BIGINT},
        </if>
        <if test="fileName != null" >
            file_name = #{fileName,jdbcType=VARCHAR},
        </if>
        <if test="directory != null" >
            directory = #{directory,jdbcType=VARCHAR},
        </if>
        <if test="deptId != null and deptId != 0" >
            dept_id = #{deptId,jdbcType=BIGINT},
        </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
</update>
<update id="updateByPrimaryKey" parameterType="com.xhjt.project.system.domain.SysBackgroundPic" >
    update background_pic
    set type = #{type,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      bg_status = #{bgStatus,jdbcType=INTEGER},
      sort_num = #{sortNum,jdbcType=BIGINT},
      file_name = #{fileName,jdbcType=VARCHAR},
      directory = #{directory,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <delete id="deteleBackgroundPicIds" parameterType="Long">
        delete from background_pic where id in
        <foreach item="picId" collection="array" open="(" separator="," close=")">
            #{picId}
        </foreach>
    </delete>
</mapper>