<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.system.mapper.SysModuleMapper">

	<resultMap type="SysModule" id="SysModuleResult">
		<id     property="id"     column="id"     />
		<result property="label"   column="label"   />
		<result property="property"   column="property"   />
		<result property="createBy"     column="create_by"      />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="type"   column="type"   />
		<result property="fieldType"   column="field_type"   />
		<result property="tableShow"   column="table_show"   />
		<result property="formShow"   column="form_show"   />
		<result property="characterType"   column="character_type"   />
		<result property="sortNum" column="sort_num" />
		<result property="isRequired" column="is_required" />
		<result property="comId" column="com_id"/>
		<result property="deptName" column="dept_name"/>
		<result property="deptId" column="dept_id"/>
	</resultMap>

	<sql id="selectModuleTypeVo">
        select distinct(t.id),t.label, t.property,t.create_by, t.create_time,t.update_by,t.update_time,t.type,t.field_type,t.table_show,t.form_show,t.character_type,t.sort_num,t.is_required,t.com_id,t.dept_id
		from template_config t left  join sys_dept d on  t.com_id = d.parent_id
    </sql>

	<insert id="insertModule" parameterType="com.xhjt.project.system.domain.SysModule">
		insert into template_config(
		<if test="label != null and label != ''">label,</if>
		<if test="property != null and property != ''">property,</if>
		<if test="createBy != null and createBy != ''">create_by,</if>
		<if test="type != null ">type,</if>
		<if test="characterType != null ">character_type,</if>
		<if test="tableShow != null">table_show,</if>
		<if test="formShow != null">form_show,</if>
		<if test="sortNum != null">sort_num,</if>
		<if test="isRequired != null">is_required,</if>
		<if test="comId != null">com_id,</if>
		<if test="deptId != null">dept_id,</if>
		create_time,
		field_type
		)values(
		<if test="label != null and label != ''">#{label},</if>
		<if test="property != null and property != ''">#{property},</if>
		<if test="createBy != null and createBy != ''">#{createBy},</if>
		<if test="type != null">#{type},</if>
		<if test="characterType != null ">#{characterType},</if>
		<if test="tableShow != null ">#{tableShow},</if>
		<if test="formShow != null">#{formShow},</if>
		<if test="sortNum != null">#{sortNum},</if>
		<if test="isRequired != null">#{isRequired},</if>
		<if test="comId != null">#{comId},</if>
		<if test="deptId != null">#{deptId},</if>
		sysdate(),1
		)
	</insert>

	<update id="updateModule">
		update template_config
		<set>
			<if test="label != null and label != ''">label = #{label},</if>
			<if test="property != null and property != ''">property = #{property},</if>
			<if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
			<if test="type != null">type = #{type},</if>
			<if test="characterType != null ">character_type = #{characterType},</if>
			<if test="tableShow != null ">table_show = #{tableShow},</if>
			<if test="formShow != null ">form_show = #{formShow},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="sortNum != null">sort_num = #{sortNum},</if>
			<if test="isRequired != null">is_required = #{isRequired},</if>
			<if test="comId != null">com_id = #{comId},</if>
			<if test="deptId != null">dept_id = #{deptId},</if>
			update_time = sysdate()
		</set>
		<where>
			id = #{id}
		</where>
	</update>

	<delete id="deleteModuleById" parameterType="com.xhjt.project.system.domain.SysModule">
		delete from template_config
		where id = #{id}
	</delete>

	<select id="checkModuleLabelUnique" parameterType="com.xhjt.project.system.domain.SysModule" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<where>
			<if test="label != null and label != ''">
				AND t.label = #{label}
			</if>
			<if test="type != null">
				AND t.type = #{type}
			</if>
			<if test="comIds != null ">
				AND find_in_set(t.com_id,#{comIds})
			</if>
		</where>
	</select>

	<select id="checkModulePropertyUnique" parameterType="com.xhjt.project.system.domain.SysModule" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<where>
			<if test="property != null and property != ''">
				AND t.property = #{property}
			</if>
			<if test="type != null">
				AND t.type = #{type}
			</if>
			<if test="comIds != null ">
				AND find_in_set(t.com_id,#{comIds})
			</if>
		</where>
	</select>

	<select id="getModuleShow" resultType="map">
		SELECT t.com_id comId,t.type,max( t.create_time ) createTime,max( dept.dept_name ) comName,
		(case when t.com_id != 100
			then
			concat((select GROUP_CONCAT( ts.label ) from template_config ts where ts.com_id=100  and ts.type = t.type ),',', GROUP_CONCAT( t.label ) )
			else
			GROUP_CONCAT( t.label )
			end) label
		FROM template_config t
		LEFT JOIN sys_dept dept ON t.com_id = dept.dept_id
		<where>
			<if test="type != null">
				t.type = #{type}
			</if>
			<if test="deptId != null">
				AND (t.dept_id = #{deptId} OR t.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
				(#{deptId},ancestors) ))
			</if>
			<if test="comId != null">
				AND t.com_id = #{comId}
			</if>
		</where>
		GROUP BY t.type,t.com_id
	</select>

	<select id="selectModuleListByType" parameterType="com.xhjt.project.system.domain.SysModule" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<where>
			<if test="type != null "> and t.type = #{type} </if>
			<if test="tableShow != null">and t.table_show = #{tableShow} </if>
			<if test="formShow != null ">and t.form_show = #{formShow} </if>
			<if test="isRequired != null ">and t.is_required = #{isRequired} </if>
			<if test="comId !=null ">and t.com_id = #{comId}</if>
		</where>
	</select>

	<select id="selectModuleListByProperty" parameterType="com.xhjt.project.system.domain.SysModule" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<where>
			<if test="type != null "> and t.type = #{type} </if>
			<if test="comId !=null ">and t.com_id = #{comId}</if>
			<if test="property !=null ">and t.property = #{property}</if>
		</where>
	</select>

	<select id="selectModuleListByEdit" parameterType="com.xhjt.project.system.domain.SysModule" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<where>
			<if test="type != null "> and t.type = #{type} </if>
			<if test="tableShow != null">and t.table_show = #{tableShow} </if>
			<if test="formShow != null ">and t.form_show = #{formShow} </if>
			<if test="isRequired != null ">and t.is_required = #{isRequired} </if>
			<if test="comIds !=null ">and find_in_set(t.com_id,#{comIds})</if>
		</where>
	</select>

	<select id="selectModuleListByComIds" parameterType="com.xhjt.project.system.domain.SysModule" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<where>
			and find_in_set(com_id,#{comIds})
			<if test="type != null "> and t.type = #{type} </if>
			<if test="tableShow != null">and t.table_show = #{tableShow} </if>
			<if test="formShow != null ">and t.form_show = #{formShow} </if>
			<if test="isRequired != null ">and t.is_required = #{isRequired} </if>
		</where>
	</select>

	<select id="getAddForm" parameterType="int" resultMap="SysModuleResult">
		<include refid="selectModuleTypeVo"/>
		<if test="type != -1">
			where t.type = #{type}
		</if>
	</select>

	<delete id="deleteModuleByType" parameterType="SysModule">
		delete from template_config
		where type = #{type} and field_type = 1
	</delete>

	<select id="getModuleShowByParam" resultType="map">
		SELECT t.com_id comId,t.type,max( t.create_time ) createTime,max( dept.dept_name ) comName,
		(case when t.com_id != 100
		then
		concat((select GROUP_CONCAT( ts.label ) from template_config ts where ts.com_id=100  and ts.type = t.type ),',', GROUP_CONCAT( t.label ) )
		else
		GROUP_CONCAT( t.label )
		end) label,
		(case when t.com_id != 100
		then
		concat((select GROUP_CONCAT( ts.property ) from template_config ts where ts.com_id=100  and ts.type = t.type ),',', GROUP_CONCAT( t.property ) )
		else
		GROUP_CONCAT( t.property )
		end) property
		FROM template_config t
		LEFT JOIN sys_dept dept ON t.com_id = dept.dept_id
		<where>
			<if test="type != null">
				and t.type = #{type}
			</if>
			<if test="comId != null">
				and t.com_id = #{comId}
			</if>
		</where>
		GROUP BY t.type,t.com_id
	</select>
</mapper>
