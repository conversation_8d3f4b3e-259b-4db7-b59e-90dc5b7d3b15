<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.engineroom.mapper.EngineRoomConfigMapper">

    <resultMap type="EngineRoomConfigEntity" id="EngineRoomConfigResult">
        <id property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="code" column="code"/>
        <result property="codeStr" column="code_str"/>
        <result property="name" column="name"/>
        <result property="symbol" column="symbol"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectConfigVo">
        select id, sn, code, code_str, name,  symbol, create_time, update_time
		from engine_room_config
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="sn !=null and sn != ''">
                and sn = #{sn}
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
            <if test="codeStr !=null and codeStr != ''">
                and code_str = #{codeStr}
            </if>
        </where>
    </sql>

    <select id="selectConfig" parameterType="EngineRoomConfigEntity" resultMap="EngineRoomConfigResult">
        <include refid="selectConfigVo"/>
        <include refid="sqlwhereSearch"/>
    </select>

    <select id="selectList" parameterType="EngineRoomConfigEntity" resultMap="EngineRoomConfigResult">
        <include refid="selectConfigVo"/>
        <where>
            <if test="sn !=null and sn != ''">
                and sn = #{sn}
            </if>
            <if test="code !=null and code != ''">
                and code = #{code}
            </if>
            <if test="codeStr !=null and codeStr != ''">
                and code_str = #{codeStr}
            </if>
        </where>
    </select>

    <insert id="add" parameterType="EngineRoomConfigEntity">
        insert into engine_room_config (
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="code != null and code != '' ">code,</if>
        <if test="codeStr != null and codeStr != '' ">code_str,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="symbol != null and symbol != '' ">symbol,</if>
        create_time
        )values(
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="codeStr != null and codeStr != ''">#{codeStr},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="symbol != null and symbol != ''">#{symbol},</if>
        sysdate()
        )
    </insert>

    <update id="update" parameterType="ShipEntity">
        update engine_room_config
        <set>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="codeStr != null and codeStr != ''">code_str = #{codeStr},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="symbol != null and symbol != ''">symbol = #{symbol},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from engine_room_config where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="Long">
        delete from engine_room_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>