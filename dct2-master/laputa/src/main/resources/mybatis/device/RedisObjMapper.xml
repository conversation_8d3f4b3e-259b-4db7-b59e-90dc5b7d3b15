<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.device.mapper.RedisObjMapper">

    <resultMap type="RedisObjVo" id="RedisObjResult">
        <id property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="updateTime" column="update_time"/>
        <result property="redisKey" column="redis_key"/>
        <result property="valueLength" column="value_length"/>
    </resultMap>

    <sql id="selectRedisObjVo">
        select id,sn, update_time, redis_key, value_length
		from redis_obj
    </sql>
    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
            <if test="redisKey != null and redisKey != ''">
                AND redis_key = #{redisKey}
            </if>
            <if test="sn !=null and sn != ''">
                and sn = #{sn}
            </if>
        </where>
    </sql>
    <select id="selectRedisObj" parameterType="RedisObjVo" resultMap="RedisObjResult">
        <include refid="selectRedisObjVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <insert id="save" parameterType="RedisObjVo" useGeneratedKeys="true" keyProperty="id">
        insert into redis_obj (
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="redisKey != null and redisKey != '' ">redis_key,</if>
        <if test="valueLength != null">value_length,</if>
         update_time
        )values(
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="redisKey != null and redisKey != ''">#{redisKey},</if>
        <if test="valueLength != null ">#{valueLength},</if>
        sysdate()
        )
    </insert>

    <update id="update" parameterType="RedisObjVo">
        update redis_obj
        <set>
            <if test="valueLength != null ">value_length = #{valueLength},</if>
            update_time = sysdate()
        </set>
        where redis_key = #{redisKey}
    </update>


</mapper>