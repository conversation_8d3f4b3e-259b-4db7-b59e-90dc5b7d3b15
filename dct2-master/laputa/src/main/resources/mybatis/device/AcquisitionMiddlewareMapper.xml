<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.AcquisitionMiddlewareMapper">

    <resultMap type="AcquisitionMiddleware" id="AcquisitionMiddlewareResult">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="mac" column="mac"/>
        <result property="ip" column="ip"/>
        <result property="port" column="port"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="moduleModel" column="module_model"/>
        <result property="shipName" column="shipName"/>
        <result property="code" column="code"/>
        <result property="deptId" column="dept_id"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="sn" column="sn"/>
    </resultMap>

    <sql id="selectAcquisitionMiddlewareVo">
        select
            am.id,
            am.type,
            am.name,
            am.mac,
            am.ip,
            am.port,
            am.connect_status,
            am.create_time,
            am.update_time,
            am.module_model,
            s.name shipName,
            am.code,
            am.dept_id,
            am.device_status,
            am.sn
        from acquisition_middleware am
            left join sys_dept d on am.dept_id = d.dept_id
            LEFT JOIN ship s on am.sn = s.sn
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlWhereSearch">
        <where>
            <if test="id !=null">
                and am.id = #{id}
            </if>
            <if test="name != null and name != ''">
                AND am.name like concat('%', #{name}, '%')
            </if>
            <if test="type !=null">
                and am.type = #{type}
            </if>
            <if test="ip !=null and ip != ''">
                and am.ip = #{ip}
            </if>
            <if test="port !=null and port != ''">
                and am.port = #{port}
            </if>
            <if test="mac !=null and mac != ''">
                and am.mac = #{mac}
            </if>
            <if test="sn !=null and sn != ''">
                and am.sn = #{sn}
            </if>
        </where>
    </sql>

    <select id="selectAcquisitionMiddleware" parameterType="AcquisitionMiddleware" resultMap="AcquisitionMiddlewareResult">
        <include refid="selectAcquisitionMiddlewareVo"/>
        <include refid="sqlWhereSearch"/>
    </select>

    <select id="selectAcquisitionMiddlewareList" parameterType="AcquisitionMiddleware" resultMap="AcquisitionMiddlewareResult">
        <include refid="selectAcquisitionMiddlewareVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (am.dept_id = #{deptId} OR am.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="name != null and name != ''">
                AND am.name like concat('%', #{name}, '%')
            </if>
            <if test="type !=null">
                and am.type = #{type}
            </if>
            <if test="ip != null and ip != ''">
                AND am.ip like concat('%', #{ip}, '%')
            </if>
            <if test="port !=null and port != ''">
                AND am.port = #{port}
            </if>
            <if test="connectStatus !=null ">
                AND am.connect_status = #{connectStatus}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(am.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(am.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="sn !=null and sn != ''">
                and am.sn = like concat('%', #{sn}, '%')
            </if>
        </where>
        order by connect_status
    </select>

    <insert id="addAcquisitionMiddleware" parameterType="AcquisitionMiddleware" useGeneratedKeys="true" keyProperty="id">
        insert into acquisition_middleware (
        <if test="type != null and type != '' ">type,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="mac != null and mac != '' ">mac,</if>
        <if test="ip != null and ip != '' ">ip,</if>
        <if test="port != null and port != '' ">port,</if>
        <if test="connectStatus != null">connect_status,</if>
        <if test="code != null and code != ''">code,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="moduleModel != null and moduleModel != ''">module_model,</if>
        <if test="deviceStatus != null ">device_status,</if>
        create_time
        )values(
        <if test="type != null and type != ''">#{type},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="mac != null and mac != ''">#{mac},</if>
        <if test="ip != null and ip != ''">#{ip},</if>
        <if test="port != null and port != ''">#{port},</if>
        <if test="connectStatus != null ">#{connectStatus},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="deptId != null and deptId != 0 ">#{deptId},</if>
        <if test="moduleModel != null and moduleModel != ''">#{moduleModel},</if>
        <if test="deviceStatus != null ">#{deviceStatus},</if>
        sysdate()
        )
    </insert>

    <update id="updateAcquisitionMiddleware" parameterType="AcquisitionMiddleware">
        update acquisition_middleware
        <set>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="port != null and port != ''">port = #{port},</if>
            <if test="connectStatus != null ">connect_status = #{connectStatus},</if>
            <if test="code != null and code != ''">code=#{code},</if>
            <if test="deptId != null and deptId != 0 ">dept_id=#{deptId},</if>
            <if test="moduleModel != null and moduleModel != ''">module_model=#{moduleModel},</if>
            <if test="deviceStatus != null ">device_status=#{deviceStatus},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="connectStatusInvalid" >
        update acquisition_middleware set connect_status = 0
    </update>

    <delete id="deleteAcquisitionMiddlewareById" parameterType="Long">
        delete from acquisition_middleware where id = #{id}
    </delete>

    <delete id="deleteAcquisitionMiddlewareByMac" parameterType="String">
        delete from acquisition_middleware where mac = #{mac}
    </delete>

    <delete id="deleteByIpAndPort" parameterType="AcquisitionMiddleware">
        delete from acquisition_middleware where ip = #{ip} and port = #{port}
    </delete>

    <delete id="delete4Invalid" >
        delete from acquisition_middleware a where a.type is null and a.mac is null
    </delete>

    <delete id="deleteAcquisitionMiddlewareByIds" parameterType="Long">
        delete from acquisition_middleware where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
