<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.ShipTerminalManageMapper">

    <resultMap type="ShipTerminalManage" id="ShipTerminalManageResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="moduleModel" column="module_model"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="serialNum" column="serial_num"/>
        <result property="softVersion" column="soft_version"/>
        <result property="authorInformation" column="author_information"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="connectStatusShow" column="connect_status_show"/>
        <result property="cpu" column="cpu"/>
        <result property="cpuShow" column="cpu_show"/>
        <result property="memory" column="memory"/>
        <result property="memoryShow" column="memory_show"/>
        <result property="hardDisk" column="hard_disk"/>
        <result property="hardDiskShow" column="hard_disk_show"/>
        <result property="wanIp" column="wan_ip"/>
        <result property="lanIp" column="lan_ip"/>
        <result property="runTime" column="run_time"/>
    </resultMap>

    <sql id="selectShipTerminalManageVo">
        select stm.id,
        stm.sn,s.name
        shipName,
        stm.dept_id,
        stm.module_model,
        stm.create_time,
        stm.update_time,
        stm.serial_num,
        stm.soft_version,
        stm.author_information,
        (case stm.connect_status when 1 then '连接' when 0 then '断开' end) connect_status_show,
        concat(stm.cpu,'%') cpu_show,
        concat(stm.memory,'%') memory_show,
        concat(stm.hard_disk,'%') hard_disk_show,
        stm.wan_ip,
        stm.lan_ip,
        stm.run_time
        from ship_terminal_manage stm
        left join ship s on stm.sn = s.sn and stm.dept_id = s.dept_id
    </sql>

    <select id="selectShipTerminalManageList" parameterType="ShipTerminalManage" resultMap="ShipTerminalManageResult">
        <include refid="selectShipTerminalManageVo"/>
        <where>
            <if test="deptId !=null">
                AND (stm.dept_id = #{deptId} OR stm.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="label !=null and label != ''">
                and (stm.sn like concat('%', #{label}, '%') or
                stm.serial_num like concat('%', #{label}, '%') or
                stm.soft_version like concat('%', #{label}, '%') or
                stm.author_information like concat('%', #{label}, '%') or
                stm.wan_ip like concat('%', #{label}, '%') or
                stm.lan_ip like concat('%', #{label}, '%') or
                s.name like concat('%', #{label}, '%'))
            </if>
        </where>
    </select>
</mapper>
