<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.device.mapper.TransferAttributeMapper">

    <resultMap type="TransferAttributeEntity" id="TransferAttributeResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="shipName" column="ship_name"/>
        <result property="deviceCode" column="device_code"/>
        <result property="name" column="name"/>
        <result property="label" column="label"/>
        <result property="orderNum" column="order_num"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTransferAttributeVo">
        select ta.id, ta.dept_id, ta.sn,s.name ship_name, ta.device_code, ta.name, ta.label, ta.order_num, ta.create_by, ta.create_time, ta.update_by, ta.update_time
		from transfer_attribute ta
	      left join ship s on ta.sn = s.sn
	      left join sys_dept d on ta.dept_id = d.dept_id
    </sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="sn !=null and sn != ''">
                AND ta.sn = #{sn}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (ta.dept_id = #{deptId} OR ta.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="deviceCode !=null">
                AND ta.device_code = #{deviceCode}
            </if>
            <if test="name !=null and name != ''">
                AND ta.name = #{name}
            </if>
        </where>
    </sql>

    <select id="selectTransferAttribute" parameterType="TransferAttributeEntity" resultMap="TransferAttributeResult">
        <include refid="selectTransferAttributeVo"/>
        <include refid="sqlwhereSearch"/>
    </select>

    <select id="selectTransferAttributeList" parameterType="TransferAttributeEntity" resultMap="TransferAttributeResult">
        <include refid="selectTransferAttributeVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (ta.dept_id = #{deptId} OR ta.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                AND ta.sn = #{sn}
            </if>
            <if test="deviceCode !=null and deviceCode != ''">
                AND ta.device_code like concat('%', #{deviceCode}, '%')
            </if>
            <if test="name != null and name != ''">
                AND ta.name like concat('%', #{name}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(ta.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(ta.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
        order by ta.device_code, ta.order_num
    </select>

    <insert id="addTransferAttribute" parameterType="TransferAttributeEntity">
        insert into transfer_attribute (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="deviceCode != null and deviceCode != '' ">device_code,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="label != null and label != '' ">label,</if>
        <if test="orderNum != null and orderNum != '' ">order_num,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="deviceCode != null and deviceCode != '' ">#{deviceCode},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="label != null and label != ''">#{label},</if>
        <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateTransferAttribute" parameterType="TransferAttributeEntity">
        update transfer_attribute
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="deviceCode != null and deviceCode != '' ">device_code = #{deviceCode},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="label != null and label != ''">label = #{label},</if>
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteTransferAttributeById" parameterType="Long">
        delete from transfer_attribute where id = #{id}
    </delete>

    <delete id="deleteTransferAttributeByIds" parameterType="Long">
        delete from transfer_attribute where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTransferAttributeByCode" parameterType="TransferAttributeEntity">
        delete from transfer_attribute where sn = #{sn} and device_code = #{deviceCode}
    </delete>

</mapper>