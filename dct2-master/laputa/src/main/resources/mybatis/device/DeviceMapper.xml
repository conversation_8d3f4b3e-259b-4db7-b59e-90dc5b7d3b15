<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.DeviceMapper">

    <resultMap type="DeviceEntity" id="DeviceResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="shipName" column="ship_name"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="code" column="code"/>
        <result property="enable" column="enable"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="compartment" column="compartment"/>
        <result property="connectType" column="connect_type"/>
        <result property="connectStatus" column="connect_status"/>
        <result property="baudRate" column="baud_rate"/>
        <result property="dataBits" column="data_bits"/>
        <result property="stopBits" column="stop_bits"/>
        <result property="parity" column="parity"/>
        <result property="serialPort" column="serial_port"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="mac" column="mac"/>
        <result property="ip" column="ip"/>
        <result property="port" column="port"/>
    </resultMap>

    <sql id="selectDeviceVo">
        select
            de.id,
            de.dept_id,
            de.sn,
            s.name ship_name,
            de.name,
            de.type,
            de.code,
            de.baud_rate,
            de.data_bits,
            de.stop_bits,
            de.parity,
            de.serial_port,
            de.transfer_status,
            de.compartment,
            de.connect_type,
            de.enable,
            de.mac,
            de.remark,
            de.create_by,
            de.create_time,
            de.update_by,
            de.update_time,
            de.connect_status,
            am.ip,
            am.port
        from device de
            left join ship s on de.sn = s.sn
            left join sys_dept d on de.dept_id = d.dept_id
            left join acquisition_middleware am on de.mac = am.mac
    </sql>

    <select id="selectDevice" parameterType="DeviceEntity" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="id !=null">
                and de.id = #{id}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (de.dept_id = #{deptId} OR de.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                and de.sn = #{sn}
            </if>
            <if test="code !=null and code != ''">
                and de.code = #{code}
            </if>
            <if test="mac !=null and mac != ''">
                AND de.mac = #{mac}
            </if>
            <if test="connectType !=null">
                and de.connect_type = #{connectType}
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <select id="selectDeviceList" parameterType="DeviceEntity" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="name != null and name != ''">
                AND de.name like concat('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND de.code like concat('%', #{code}, '%')
            </if>
            <if test="sn !=null and sn != ''">
                AND de.sn = #{sn}
            </if>
            <if test="type !=null ">
                AND de.type = #{type}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(de.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(de.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <insert id="addDevice" parameterType="DeviceEntity">
        insert into device (
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="sn != null and sn != '' ">sn,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="type != null ">type,</if>
        <if test="code != null and code != '' ">code,</if>
        <if test="enable != null ">enable,</if>
        <if test="serialPort != null">serial_port,</if>
        <if test="transferStatus != null">transfer_status,</if>
        <if test="connectType != null ">connect_type,</if>
        <if test="compartment != null ">compartment,</if>
        <if test="baudRate != null ">baud_rate,</if>
        <if test="dataBits != null ">data_bits,</if>
        <if test="stopBits != null">stop_bits,</if>
        <if test="parity != null">parity,</if>
        <if test="remark != null and remark != '' ">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="connectStatus != null">connect_status,</if>
        <if test="mac != null and mac != ''">mac,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="sn != null and sn != ''">#{sn},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="type != null ">#{type},</if>
        <if test="code != null and code != ''">#{code},</if>
        <if test="enable != null ">#{enable},</if>
        <if test="serialPort != null and serialPort != ''">#{serialPort},</if>
        <if test="transferStatus != null ">#{transferStatus},</if>
        <if test="connectType != null ">#{connectType},</if>
        <if test="compartment != null ">#{compartment},</if>
        <if test="baudRate != null ">#{baudRate},</if>
        <if test="dataBits != null ">#{dataBits},</if>
        <if test="stopBits != null">#{stopBits},</if>
        <if test="parity != null">#{parity},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="connectStatus != null ">#{connectStatus},</if>
        <if test="mac != null and mac != ''">#{mac},</if>
        sysdate()
        )
    </insert>

    <update id="updateDevice" parameterType="DeviceEntity">
        update device
        <set>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null ">type = #{type},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="enable != null ">enable = #{enable},</if>
            <if test="serialPort != null and serialPort != '' ">serial_port = #{serialPort},</if>
            <if test="compartment != null ">compartment = #{compartment},</if>
            <if test="connectType != null ">connect_type = #{connectType},</if>
            <if test="transferStatus != null ">transfer_status = #{transferStatus},</if>
            <if test="baudRate != null ">baud_rate=#{baudRate},</if>
            <if test="dataBits != null ">data_bits=#{dataBits},</if>
            <if test="stopBits != null">stop_bits=#{stopBits},</if>
            <if test="parity != null">parity=#{parity},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="connectStatus != null ">connect_status = #{connectStatus},</if>
            <if test="mac != null and mac != ''">mac=#{mac},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteDeviceById" parameterType="Long">
        delete from device where id = #{id}
    </delete>

    <delete id="deleteDeviceByIds" parameterType="Long">
        delete from device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>