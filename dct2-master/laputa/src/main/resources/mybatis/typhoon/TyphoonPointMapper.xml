<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.typhoon.mapper.TyphoonPointMapper">

    <resultMap type="TyphoonPoint" id="TyphoonPointResult">
        <id property="id" column="id"/>
        <result property="tfid" column="tfid"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="movedirection" column="movedirection"/>
        <result property="movespeed" column="movespeed"/>
        <result property="power" column="power"/>
        <result property="pressure" column="pressure"/>
        <result property="radius7" column="radius7"/>
        <result property="radius10" column="radius10"/>
        <result property="radius12" column="radius12"/>
        <result property="speed" column="speed"/>
        <result property="strong" column="strong"/>
        <result property="time" column="time"/>
        <result property="timeStamp" column="time_stamp"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTyphoonPointVo">
        select id, tfid, lat, lng, movedirection, movespeed, power, pressure, radius7, radius10, radius12, speed, strong, time, time_stamp, create_time, update_time
		from typhoon_point
    </sql>

    <select id="selectTyphoonPoint" parameterType="TyphoonPoint" resultMap="TyphoonPointResult">
        <include refid="selectTyphoonPointVo"/>
        <where>
            <if test="id !=null">
                and id = #{id}
            </if>
        </where>
    </select>

    <select id="selectTyphoonPointList" parameterType="TyphoonPoint" resultMap="TyphoonPointResult">
        <include refid="selectTyphoonPointVo"/>
        <where>
            <if test="tfid !=null">
                and tfid = #{tfid}
            </if>
        </where>
    </select>

    <insert id="addTyphoonPoint" parameterType="TyphoonPoint" useGeneratedKeys="true" keyProperty="id">
        insert into typhoon_point (
        <if test="tfid != null and tfid != '' ">tfid,</if>
        <if test="lat != null and lat != '' ">lat,</if>
        <if test="lng != null and lng != '' ">lng,</if>
        <if test="movedirection != null and movedirection != ''">movedirection,</if>
        <if test="movespeed != null and movespeed != ''">movespeed,</if>
        <if test="power != null and power != ''">power,</if>
        <if test="pressure != null and pressure != ''">pressure,</if>
        <if test="radius7 != null and radius7 != ''">radius7,</if>
        <if test="radius10 != null and radius10 != ''">radius10,</if>
        <if test="radius12 != null and radius12 != ''">radius12,</if>
        <if test="speed != null and speed != ''">speed,</if>
        <if test="strong != null and strong != ''">strong,</if>
        <if test="time != null and time != ''">time,</if>
        <if test="timeStamp != null and timeStamp != ''">time_stamp,</if>
        create_time
        )values(
        <if test="tfid != null and tfid != ''">#{tfid},</if>
        <if test="lat != null and lat != ''">#{lat},</if>
        <if test="lng != null and lng != ''">#{lng},</if>
        <if test="movedirection != null and movedirection != ''">#{movedirection},</if>
        <if test="movespeed != null and movespeed != ''">#{movespeed},</if>
        <if test="power != null and power != ''">#{power},</if>
        <if test="pressure != null and pressure != ''">#{pressure},</if>
        <if test="radius7 != null and radius7 != ''">#{radius7},</if>
        <if test="radius10 != null and radius10 != ''">#{radius10},</if>
        <if test="radius12 != null and radius12 != ''">#{radius12},</if>
        <if test="speed != null and speed != ''">#{speed},</if>
        <if test="strong != null and strong != ''">#{strong},</if>
        <if test="time != null and time != ''">#{time},</if>
        <if test="timeStamp != null and timeStamp != ''">#{timeStamp},</if>
        sysdate()
        )
    </insert>

    <update id="updateTyphoonPoint" parameterType="TyphoonPoint">
        update typhoon_point
        <set>
            <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
            <if test="lat != null and lat != ''">lat = #{lat},</if>
            <if test="lng != null and lng != ''">lng = #{lng},</if>
            <if test="movedirection != null and movedirection != ''">movedirection = #{movedirection},</if>
            <if test="movespeed != null and movespeed != ''">movespeed = #{movespeed},</if>
            <if test="power != null and power != ''">power = #{power},</if>
            <if test="pressure != null and pressure != ''">pressure = #{pressure},</if>
            <if test="radius7 != null and radius7 != ''">radius7 = #{radius7},</if>
            <if test="radius10 != null and radius10 != ''">radius10 = #{radius10},</if>
            <if test="radius12 != null and radius12 != ''">radius12 = #{radius12},</if>
            <if test="speed != null and speed != ''">speed = #{speed},</if>
            <if test="strong != null and strong != ''">strong = #{strong},</if>
            <if test="time != null and time != ''">time = #{time},</if>
            <if test="timeStamp != null and timeStamp != ''">time_stamp = #{timeStamp},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

</mapper>