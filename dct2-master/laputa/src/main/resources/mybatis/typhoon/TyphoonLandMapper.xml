<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhjt.project.typhoon.mapper.TyphoonLandMapper">

    <resultMap type="TyphoonLand" id="TyphoonLandResult">
        <id property="id" column="id"/>
        <result property="tfid" column="tfid"/>
        <result property="info" column="info"/>
        <result property="landaddress" column="landaddress"/>
        <result property="landtime" column="landtime"/>
        <result property="landTimeStamp" column="land_time_stamp"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="strong" column="strong"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTyphoonLandVo">
        select id, tfid, info, landaddress, landtime, land_time_stamp, lat, lng, strong, create_time, update_time
		from typhoon_land
    </sql>

    <select id="selectTyphoonLand" parameterType="TyphoonLand" resultMap="TyphoonLandResult">
        <include refid="selectTyphoonLandVo"/>
        <where>
            <if test="tfid !=null">
                and tfid = #{tfid}
            </if>
        </where>
    </select>

    <select id="selectTyphoonLandList" parameterType="TyphoonLand" resultMap="TyphoonLandResult">
        <include refid="selectTyphoonLandVo"/>
        <where>
            <if test="tfid !=null">
                and tfid = #{tfid}
            </if>
            <if test="landaddress != null and landaddress != ''">
                AND landaddress like concat('%', #{landaddress}, '%')
            </if>
        </where>
    </select>

    <insert id="addTyphoonLand" parameterType="TyphoonLand">
        insert into typhoon_land (
        <if test="tfid != null and tfid != '' ">tfid,</if>
        <if test="info != null and info != '' ">info,</if>
        <if test="landaddress != null and landaddress != '' ">landaddress,</if>
        <if test="landtime != null and landtime != ''">landtime,</if>
        <if test="landTimeStamp != null and landTimeStamp != ''">land_time_stamp,</if>
        <if test="lat != null and lat != ''">lat,</if>
        <if test="lng != null and lng != ''">lng,</if>
        <if test="strong != null and strong != ''">strong,</if>
        create_time
        )values(
        <if test="tfid != null and tfid != ''">#{tfid},</if>
        <if test="info != null and info != ''">#{info},</if>
        <if test="landaddress != null and landaddress != ''">#{landaddress},</if>
        <if test="landtime != null and landtime != ''">#{landtime},</if>
        <if test="landTimeStamp != null and landTimeStamp != ''">#{landTimeStamp},</if>
        <if test="lat != null and lat != ''">#{lat},</if>
        <if test="lng != null and lng != ''">#{lng},</if>
        <if test="strong != null and strong != ''">#{strong},</if>
        sysdate()
        )
    </insert>

    <update id="updateTyphoonLand" parameterType="TyphoonLand">
        update typhoon_land
        <set>
            <if test="tfid != null and tfid != ''">tfid = #{tfid},</if>
            <if test="info != null and info != ''">info = #{info},</if>
            <if test="landaddress != null and landaddress != ''">landaddress = #{landaddress},</if>
            <if test="landtime != null and landtime != ''">landtime = #{landtime},</if>
            <if test="landTimeStamp != null and landTimeStamp != ''">land_time_stamp = #{landTimeStamp},</if>
            <if test="lat != null and lat != ''">lat = #{lat},</if>
            <if test="lng != null and lng != ''">lng = #{lng},</if>
            <if test="strong != null and strong != ''">strong = #{strong},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

</mapper>