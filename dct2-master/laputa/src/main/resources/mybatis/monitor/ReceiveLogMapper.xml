<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.monitor.mapper.ReceiveLogMapper">

	<resultMap type="ReceiveLog" id="ReceiveLogResult">
		<id     property="id"         column="id"        />
		<result property="deptId" column="dept_id"/>
		<result property="sn"          column="sn"          />
		<result property="recordTime"   column="record_time"  />
		<result property="totalLength"   column="total_length"         />
		<result property="totalLines"    column="total_lines" />
		<result property="repairLength"   column="repair_length"  />
		<result property="picLength"       column="pic_length"      />
		<result property="createTime"  column="create_time"/>
	</resultMap>

	<sql id="selectVo">
        select r.id, r.dept_id, r.sn, r.record_time, r.total_length, r.total_lines, r.repair_length, r.pic_length
        from receive_log r
	      left join sys_dept d on r.dept_id = d.dept_id
    </sql>
    
	<insert id="insert" parameterType="ReceiveLog">
		insert into receive_log(dept_id, sn, record_time, total_length, total_lines, repair_length, pic_length, create_time)
        values (#{deptId}, #{sn}, #{recordTime}, #{totalLength}, #{totalLines}, #{repairLength}, #{picLength}, sysdate())
	</insert>
	
	<select id="selectList" parameterType="ReceiveLog" resultMap="ReceiveLogResult">
		<include refid="selectVo"/>
		<where>
			<if test="deptId != null and deptId != 0">
				AND (s.dept_id = #{deptId} OR s.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
				(#{deptId},ancestors) ))
			</if>
			<if test="sn != null and sn != ''">
				AND sn = #{sn}
			</if>
			<!-- 数据范围过滤 -->
			${dataScope}
		</where>
		order by record_time desc
	</select>

	<select id="selectReceiveLogList" parameterType="ReceiveLog" resultMap="ReceiveLogResult">
		<include refid="selectVo"/>
		order by record_time desc
	</select>

	<delete id="deleteByIds" parameterType="Long">
 		delete from receive_log where id in
 		<foreach collection="array" item="id" open="(" separator="," close=")">
 			#{id}
        </foreach>
 	</delete>

 	<select id="selectById" parameterType="Long" resultMap="ReceiveLogResult">
		<include refid="selectVo"/>
		where id = #{id}
	</select>

	<select id="selectListByRecordTime" parameterType="ReceiveLog" resultMap="ReceiveLogResult">
		<include refid="selectVo"/>
		<where>
			<![CDATA[ record_time>= #{startTime} ]]> and <![CDATA[ record_time<= #{endTime} ]]> and sn = #{sn} order by record_time
		</where>
	</select>

</mapper> 