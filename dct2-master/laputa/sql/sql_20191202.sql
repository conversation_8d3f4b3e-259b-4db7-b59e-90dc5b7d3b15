
-- ----------------------------
-- 初始化-部门表数据
-- ----------------------------
insert into sys_dept values(100,  0,   '0',          '西海科技',   0, 'XX', '15888888888', '<EMAIL>', '0', '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00');


-- ----------------------------
-- 初始化-用户信息表数据
-- ----------------------------
INSERT INTO `dct`.`sys_user` (`user_id`, `dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `authentication`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `binding_ip`) VALUES ('1', '100', 'master009', '管理员009', '00', '<EMAIL>', '15888888888', '1', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '管理员', NULL);
INSERT INTO `dct`.`sys_user` (`user_id`, `dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `authentication`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `binding_ip`) VALUES ('2', '100', 'admin', '管理员', '00', '<EMAIL>', '15888888888', '1', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '', '2018-03-16 11:33:00', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '管理员', NULL);

-- ----------------------------
-- 初始化-岗位信息表数据
-- ----------------------------
insert into sys_post values(1, 'ceo',  '董事长',    1, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_post values(2, 'se',   '项目经理',  2, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_post values(3, 'hr',   '人力资源',  3, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_post values(4, 'user', '普通员工',  4, '0', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');


-- ----------------------------
-- 初始化-角色信息表数据
-- ----------------------------
INSERT INTO `dct`.`sys_role` (`role_id`, `dept_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('1', '100', '超级管理员', 'admin', '1', '1', '0', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '超级管理员');
INSERT INTO `dct`.`sys_role` (`role_id`, `dept_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('2', '100', '管理员', 'manage', '1', '1', '0', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '管理员');
INSERT INTO `dct`.`sys_role` (`role_id`, `dept_id`, `role_name`, `role_key`, `role_sort`, `data_scope`, `status`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('3', '100', '普通角色', 'common', '2', '2', '0', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '普通角色');

-- ----------------------------
-- 初始化-用户和角色关联表数据
-- ----------------------------
insert into sys_user_role values ('1', '1');
insert into sys_user_role values ('2', '2');

-- ----------------------------
-- 初始化-菜单信息表数据
-- ----------------------------
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '系统管理', 0, 15, 'system', NULL, 1, 'M', '0', '', 'system', 0, 'admin', '2018-03-16 11:33:00', 'admin', '2020-07-27 11:23:59', '系统管理目录');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '系统监控', 0, 16, 'monitor', NULL, 1, 'M', '0', '', 'monitor', 0, 'admin', '2018-03-16 11:33:00', 'admin', '2020-10-27 11:25:12', '系统监控目录');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 'C', '0', 'system:user:list', 'user', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '用户管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 'C', '0', 'system:role:list', 'peoples', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '角色管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 'C', '0', 'system:menu:list', 'tree-table', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '菜单管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, '船队管理', 1, 4, 'dept', 'system/dept/index', 1, 'C', '0', 'system:dept:list', 'tree', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '船队管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', 1, 'C', '0', 'system:post:list', 'post', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '岗位管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', 1, 'C', '0', 'system:dict:list', 'dict', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '字典管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', 1, 'C', '0', 'system:config:list', 'edit', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '参数设置菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', 1, 'C', '0', 'system:notice:list', 'message', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '通知公告菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (108, '日志管理', 2, 5, 'log', 'system/log/index', 1, 'M', '0', '', 'log', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '日志管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', 1, 'C', '0', 'monitor:online:list', 'online', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '在线用户菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', 1, 'C', '0', 'monitor:job:list', 'job', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '定时任务菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', 1, 'C', '0', 'monitor:druid:list', 'druid', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '数据监控菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (112, '模板管理', 1, 9, 'module', 'system/module/index', 1, 'C', '0', 'system:module:list', 'documentation', 0, 'admin', '2021-11-02 15:00:00', 'master009', '2021-12-01 16:16:23', '模板管理菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', 1, 'C', '0', 'monitor:operlog:list', 'form', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '操作日志菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', 1, 'C', '0', 'monitor:logininfor:list', 'logininfor', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '登录日志菜单');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1001, '用户查询', 100, 1, '', '', 1, 'F', '0', 'system:user:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1002, '用户新增', 100, 2, '', '', 1, 'F', '0', 'system:user:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1003, '用户修改', 100, 3, '', '', 1, 'F', '0', 'system:user:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1004, '用户删除', 100, 4, '', '', 1, 'F', '0', 'system:user:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1005, '用户导出', 100, 5, '', '', 1, 'F', '0', 'system:user:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1006, '用户导入', 100, 6, '', '', 1, 'F', '0', 'system:user:import', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1007, '重置密码', 100, 7, '', '', 1, 'F', '0', 'system:user:resetPwd', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1008, '角色查询', 101, 1, '', '', 1, 'F', '0', 'system:role:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1009, '角色新增', 101, 2, '', '', 1, 'F', '0', 'system:role:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1010, '角色修改', 101, 3, '', '', 1, 'F', '0', 'system:role:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1011, '角色删除', 101, 4, '', '', 1, 'F', '0', 'system:role:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1012, '角色导出', 101, 5, '', '', 1, 'F', '0', 'system:role:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1013, '菜单查询', 102, 1, '', '', 1, 'F', '0', 'system:menu:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1014, '菜单新增', 102, 2, '', '', 1, 'F', '0', 'system:menu:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1015, '菜单修改', 102, 3, '', '', 1, 'F', '0', 'system:menu:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1016, '菜单删除', 102, 4, '', '', 1, 'F', '0', 'system:menu:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1017, '部门查询', 103, 1, '', '', 1, 'F', '0', 'system:dept:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1018, '部门新增', 103, 2, '', '', 1, 'F', '0', 'system:dept:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1019, '部门修改', 103, 3, '', '', 1, 'F', '0', 'system:dept:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1020, '部门删除', 103, 4, '', '', 1, 'F', '0', 'system:dept:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1021, '岗位查询', 104, 1, '', '', 1, 'F', '0', 'system:post:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1022, '岗位新增', 104, 2, '', '', 1, 'F', '0', 'system:post:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1023, '岗位修改', 104, 3, '', '', 1, 'F', '0', 'system:post:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1024, '岗位删除', 104, 4, '', '', 1, 'F', '0', 'system:post:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1025, '岗位导出', 104, 5, '', '', 1, 'F', '0', 'system:post:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1026, '字典查询', 105, 1, '#', '', 1, 'F', '0', 'system:dict:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1027, '字典新增', 105, 2, '#', '', 1, 'F', '0', 'system:dict:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1028, '字典修改', 105, 3, '#', '', 1, 'F', '0', 'system:dict:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1029, '字典删除', 105, 4, '#', '', 1, 'F', '0', 'system:dict:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1030, '字典导出', 105, 5, '#', '', 1, 'F', '0', 'system:dict:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1031, '参数查询', 106, 1, '#', '', 1, 'F', '0', 'system:config:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1032, '参数新增', 106, 2, '#', '', 1, 'F', '0', 'system:config:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1033, '参数修改', 106, 3, '#', '', 1, 'F', '0', 'system:config:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1034, '参数删除', 106, 4, '#', '', 1, 'F', '0', 'system:config:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1035, '参数导出', 106, 5, '#', '', 1, 'F', '0', 'system:config:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1036, '公告查询', 107, 1, '#', '', 1, 'F', '0', 'system:notice:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1037, '公告新增', 107, 2, '#', '', 1, 'F', '0', 'system:notice:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1038, '公告修改', 107, 3, '#', '', 1, 'F', '0', 'system:notice:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1039, '公告删除', 107, 4, '#', '', 1, 'F', '0', 'system:notice:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1040, '操作查询', 500, 1, '#', '', 1, 'F', '0', 'monitor:operlog:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1041, '操作删除', 500, 2, '#', '', 1, 'F', '0', 'monitor:operlog:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1042, '日志导出', 500, 4, '#', '', 1, 'F', '0', 'monitor:operlog:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1043, '登录查询', 501, 1, '#', '', 1, 'F', '0', 'monitor:logininfor:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1044, '登录删除', 501, 2, '#', '', 1, 'F', '0', 'monitor:logininfor:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1045, '日志导出', 501, 3, '#', '', 1, 'F', '0', 'monitor:logininfor:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1046, '在线查询', 109, 1, '#', '', 1, 'F', '0', 'monitor:online:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1047, '批量强退', 109, 2, '#', '', 1, 'F', '0', 'monitor:online:batchLogout', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1048, '单条强退', 109, 3, '#', '', 1, 'F', '0', 'monitor:online:forceLogout', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1049, '任务查询', 110, 1, '#', '', 1, 'F', '0', 'monitor:job:query', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1050, '任务新增', 110, 2, '#', '', 1, 'F', '0', 'monitor:job:add', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1051, '任务修改', 110, 3, '#', '', 1, 'F', '0', 'monitor:job:edit', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1052, '任务删除', 110, 4, '#', '', 1, 'F', '0', 'monitor:job:remove', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1053, '状态修改', 110, 5, '#', '', 1, 'F', '0', 'monitor:job:changeStatus', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1054, '任务导出', 110, 7, '#', '', 1, 'F', '0', 'monitor:job:export', '#', 0, 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2011, '船舶管理', 0, 1, 'ship', NULL, 1, 'M', '0', '', 'dict', 0, 'admin', '2020-07-24 09:55:22', 'admin', '2020-07-24 14:34:13', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2012, '船只管理', 2011, 1, 'ship', 'ship/ship', 1, 'C', '0', 'ship:info:query', 'build', 0, 'admin', '2020-07-24 09:56:15', 'jiageng', '2021-04-13 10:32:12', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2013, '航次管理', 2011, 2, 'cruise', 'ship/cruise', 1, 'C', '0', 'cruise:info:query', 'color', 0, 'admin', '2020-07-24 09:56:57', 'jiageng', '2021-04-13 10:32:26', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2014, '列表', 2012, 1, '', NULL, 1, 'F', '0', 'ship:info:list', '#', 0, 'admin', '2020-07-24 14:36:18', 'admin', '2020-07-26 18:45:39', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2015, '新增', 2012, 2, '', NULL, 1, 'F', '0', 'ship:info:add', '#', 0, 'admin', '2020-07-24 14:36:59', 'admin', '2020-07-24 14:37:33', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2016, '修改', 2012, 3, '', NULL, 1, 'F', '0', 'ship:info:edit', '#', 0, 'admin', '2020-07-24 14:37:24', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2017, '删除', 2012, 4, '', NULL, 1, 'F', '0', 'ship:info:remove', '#', 0, 'admin', '2020-07-24 14:38:00', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2018, '导出', 2012, 5, '', NULL, 1, 'F', '0', 'ship:info:export', '#', 0, 'admin', '2020-07-24 14:38:28', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2019, '列表', 2013, 1, '', NULL, 1, 'F', '0', 'cruise:info:list', '#', 0, 'admin', '2020-07-24 14:39:03', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2020, '新增', 2013, 2, '', NULL, 1, 'F', '0', 'cruise:info:add', '#', 0, 'admin', '2020-07-24 14:39:35', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2021, '修改', 2013, 3, '', NULL, 1, 'F', '0', 'cruise:info:edit', '#', 0, 'admin', '2020-07-24 14:39:55', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2022, '删除', 2013, 4, '', NULL, 1, 'F', '0', 'cruise:info:remove', '#', 0, 'admin', '2020-07-24 14:40:17', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2023, '导出', 2013, 5, '', NULL, 1, 'F', '0', 'cruise:info:export', '#', 0, 'admin', '2020-07-24 14:40:33', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2024, '视图管理', 0, 7, 'view', NULL, 1, 'M', '0', '', 'color', 0, 'admin', '2020-07-26 18:43:54', 'admin', '2021-03-29 14:36:35', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2025, '视图方案', 2024, 2, 'view', 'view/scheme/index', 1, 'C', '0', 'view:scheme:query', 'dashboard', 0, 'admin', '2020-07-26 18:44:59', 'jiageng', '2021-04-13 10:33:46', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2026, '列表', 2025, 1, '', NULL, 1, 'F', '0', 'view:scheme:list', '#', 0, 'admin', '2020-07-26 18:46:14', 'admin', '2021-04-11 18:57:48', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2027, '添加', 2025, 2, '', NULL, 1, 'F', '0', 'view:scheme:add', '#', 0, 'admin', '2020-07-26 18:46:40', 'admin', '2021-04-11 18:57:54', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2028, '修改', 2025, 3, '', NULL, 1, 'F', '0', 'view:scheme:edit', '#', 0, 'admin', '2020-07-26 18:47:01', 'admin', '2021-04-11 18:57:59', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2029, '删除', 2025, 4, '', NULL, 1, 'F', '0', 'view:scheme:remove', '#', 0, 'admin', '2020-07-26 18:47:16', 'admin', '2021-04-11 18:58:03', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2030, '导出', 2025, 5, '', NULL, 1, 'F', '0', 'view:scheme:export', '#', 0, 'admin', '2020-07-26 18:47:31', 'admin', '2021-04-11 18:58:12', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2035, '设备管理', 0, 3, 'device', NULL, 1, 'M', '0', NULL, 'date-range', 0, 'admin', '2020-07-27 11:24:52', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2037, '设备信息', 2035, 1, 'device', 'device/info/index', 1, 'C', '0', 'device:info:list', 'build', 0, 'admin', '2020-07-27 11:25:22', 'admin', '2021-03-29 17:54:39', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2038, '列表', 2037, 1, '', NULL, 1, 'F', '0', 'device:info:query', '#', 0, 'admin', '2020-07-27 11:25:43', 'admin', '2020-07-27 11:26:38', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2039, '添加', 2037, 2, '', NULL, 1, 'F', '0', 'device:info:add', '#', 0, 'admin', '2020-07-27 11:25:57', 'admin', '2020-07-27 11:27:08', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2040, '修改', 2037, 3, '', NULL, 1, 'F', '0', 'device:info:edit', '#', 0, 'admin', '2020-07-27 11:26:16', 'admin', '2020-07-27 11:27:18', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2041, '删除', 2037, 4, '', NULL, 1, 'F', '0', 'device:info:remove', '#', 0, 'admin', '2020-07-27 11:27:32', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2042, '导出', 2037, 5, '', NULL, 1, 'F', '0', 'device:info:export', '#', 0, 'admin', '2020-07-27 11:27:44', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2043, '设备数据', 2035, 2, 'deviceData', 'device/data/index', 1, 'C', '0', 'data:device:query', 'education', 0, 'admin', '2020-07-27 11:29:45', 'jiageng', '2021-04-13 10:33:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2044, '列表', 2043, 1, '', NULL, 1, 'F', '0', 'data:device:list', '#', 0, 'admin', '2020-07-27 11:31:01', 'admin', '2021-03-29 11:03:05', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2048, '导出', 2043, 2, '', NULL, 1, 'F', '0', 'data:device:export', '#', 0, 'admin', '2020-07-27 11:32:24', 'admin', '2021-03-29 11:03:20', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2051, '数据传输', 2, 5, 'engineRoom', 'monitor/transmission/engineRoom', 1, 'C', '0', 'monitor:transmission:list', 'chart', 0, 'admin', '2020-12-22 12:13:19', 'admin', '2021-01-18 10:10:51', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2052, '首页', 0, 30, 'index.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:03:53', 'admin', '2020-12-25 18:19:32', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2053, '船舶信息显示系统', 0, 31, 'webConning.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:37:34', 'admin', '2020-12-28 11:01:08', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2054, '机舱数据', 0, 32, 'engineroom/diesel_generator1.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:40:56', 'admin', '2020-12-28 10:58:33', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2055, '视频监控', 0, 33, 'show2.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:41:34', 'admin', '2020-12-28 15:21:17', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2056, '实时轨迹', 0, 34, 'mapgis.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:41:59', 'admin', '2020-12-28 10:52:02', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2057, 'SBE21', 0, 35, 'sbe21.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:42:30', 'admin', '2020-12-28 10:52:08', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2058, 'AWS', 0, 36, 'aws.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:42:52', 'admin', '2020-12-28 14:58:16', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2059, 'GO8050', 0, 37, 'pco2.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:43:12', 'admin', '2020-12-28 15:16:16', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2060, '1号发电机', 2054, 1, 'engineroom/diesel_generator1.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:44:34', 'admin', '2020-12-28 15:25:36', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2061, '2号发电机', 2054, 2, 'engineroom/diesel_generator2.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 10:44:52', 'admin', '2020-12-28 15:08:31', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2062, '3号发电机', 2054, 3, 'engineroom/diesel_generator3.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-25 19:02:07', 'admin', '2020-12-25 19:02:51', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2063, '应急发电机', 2054, 4, 'engineroom/emergencyGenerator.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:35:52', 'admin', '2020-12-28 14:42:03', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2064, '压缩空气系统', 2054, 5, 'engineroom/compressedAir.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:36:36', 'admin', '2020-12-28 14:41:07', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2065, '锅炉给水系统', 2054, 6, 'engineroom/boilerFeedWater.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:39:00', 'admin', '2020-12-28 14:42:58', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2067, '泵控制系统', 2054, 7, 'engineroom/pumpControl.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:49:14', 'admin', '2020-12-28 15:17:49', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2068, '温度', 2057, 1, 'sbe21_subScreenl.html?para=0', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:50:24', 'admin', '2020-12-28 15:45:58', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2069, '盐度', 2057, 2, 'sbe21_subScreenl.html?para=1', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:50:56', 'admin', '2020-12-28 15:46:04', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2070, '溶解氧', 2057, 3, 'sbe21_subScreenl.html?para=2', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 14:51:23', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2071, '叶绿素', 2057, 4, 'sbe21_subScreenl.html?para=3', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 14:52:04', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2072, '浊度', 2057, 5, 'sbe21_subScreenl.html?para=4', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 14:52:28', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2073, 'AWS展示', 2058, 1, 'awsWork.html', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 14:57:49', 'admin', '2020-12-28 14:58:24', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2074, '大气温度', 2058, 2, 'aws_subScreen.html?para=0', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 14:59:03', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2075, '露点温度', 2058, 3, 'aws_subScreen.html?para=1', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 14:59:28', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2076, '大气压力', 2058, 4, 'aws_subScreen.html?para=2', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 14:59:50', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2077, '相对湿度', 2058, 5, 'aws_subScreen.html?para=3', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:00:19', 'admin', '2020-12-28 15:01:00', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2078, '相对风向', 2058, 6, 'aws_subScreen.html?para=4', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 15:00:53', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2079, '真实风向', 2058, 7, 'aws_subScreen.html?para=6', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 15:01:44', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2080, '相对风速', 2058, 8, 'aws_subScreen.html?para=5', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:02:26', 'admin', '2020-12-28 15:03:03', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2081, '真实风速', 2058, 9, 'aws_subScreen.html?para=7', NULL, 1, 'M', '0', NULL, 'clipboard', 1, 'admin', '2020-12-28 15:02:53', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2082, '叶绿素', 2059, 1, 'pco2_subScreen.html?para=0', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:03:46', 'admin', '2020-12-28 15:21:44', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2083, 'CO2', 2059, 2, 'pco2_subScreen.html?para=1', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:04:09', 'admin', '2020-12-28 15:21:49', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2084, '平衡器温度', 2059, 3, 'pco2_subScreen.html?para=2', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:04:44', 'admin', '2020-12-28 15:22:14', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2085, '原位温度', 2059, 4, 'pco2_subScreen.html?para=3', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:05:09', 'admin', '2020-12-28 15:22:12', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2086, '真风速', 2059, 5, 'pco2_subScreen.html?para=4', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:05:33', 'admin', '2020-12-28 15:22:10', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2087, '溶氧饱和度', 2059, 6, 'pco2_subScreen.html?para=5', NULL, 1, 'M', '0', '', 'clipboard', 1, 'admin', '2020-12-28 15:06:07', 'admin', '2020-12-28 15:22:04', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2088, '站位管理', 2011, 3, 'cruiseStation', 'ship/cruiseStation', 1, 'C', '0', 'cruiseStation:info:query', 'component', 0, 'admin', '2020-12-29 10:59:16', 'jiageng', '2021-04-13 10:32:50', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2089, '列表', 2088, 1, '', NULL, 1, 'F', '0', 'cruiseStation:info:list', '#', 0, 'admin', '2020-12-29 10:59:55', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2090, '新增', 2088, 2, '', NULL, 1, 'F', '0', 'cruiseStation:info:add', '#', 0, 'admin', '2020-12-29 11:00:18', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2091, '修改', 2088, 3, '', NULL, 1, 'F', '0', 'cruiseStation:info:edit', '#', 0, 'admin', '2020-12-29 11:00:42', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2092, '删除', 2088, 4, '', NULL, 1, 'F', '0', 'cruiseStation:info:remove', '#', 0, 'admin', '2020-12-29 11:01:00', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2093, '导出', 2088, 5, '', NULL, 1, 'F', '0', 'cruiseStation:info:export', '#', 0, 'admin', '2020-12-29 11:01:20', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2098, '服务监控', 2, 6, 'server', 'monitor/server/index', 1, 'C', '0', 'monitor:server:list', 'qq', 0, 'admin', '2021-02-03 15:07:06', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2100, '快照管理', 0, 4, 'snapshot', NULL, 1, 'M', '0', NULL, 'icon', 0, 'admin', '2021-03-29 11:05:03', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2101, '快照通道', 2100, 1, 'SnapshotChannel', 'snapshot/channel/index', 1, 'C', '0', 'snapshot:channel:query', 'color', 0, 'admin', '2021-03-29 11:05:36', 'jiageng', '2021-04-13 11:09:09', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2102, '截屏日志', 2100, 2, 'SnapshotLog', 'snapshot/log/index', 1, 'C', '0', 'snapshot:log:query', 'excel', 0, 'admin', '2021-03-29 11:06:13', 'jiageng', '2021-04-13 10:33:19', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2103, '列表', 2101, 1, '', NULL, 1, 'F', '0', 'snapshot:channel:list', '#', 0, 'admin', '2021-03-29 11:06:37', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2104, '添加', 2101, 2, '', NULL, 1, 'F', '0', 'snapshot:channel:add', '#', 0, 'admin', '2021-03-29 11:06:54', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2105, '修改', 2101, 3, '', NULL, 1, 'F', '0', 'snapshot:channel:edit', '#', 0, 'admin', '2021-03-29 11:07:07', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2106, '删除', 2101, 4, '', NULL, 1, 'F', '0', 'snapshot:channel:remove', '#', 0, 'admin', '2021-03-29 11:07:21', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2107, '导出', 2101, 5, '', NULL, 1, 'F', '0', 'snapshot:channel:export', '#', 0, 'admin', '2021-03-29 11:07:34', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2108, '列表', 2102, 1, '', NULL, 1, 'F', '0', 'snapshot:log:list', '#', 0, 'admin', '2021-03-29 11:07:52', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2109, '导出', 2102, 2, '', NULL, 1, 'F', '0', 'snapshot:log:export', '#', 0, 'admin', '2021-03-29 11:08:05', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2110, '传输管理', 0, 5, 'transfer', NULL, 1, 'M', '0', NULL, 'dict', 0, 'admin', '2021-03-29 14:37:16', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2111, '设备传输', 2110, 1, 'TransferAttribute', 'device/transfer/index', 1, 'C', '0', 'transfer:attribute:query', 'build', 0, 'admin', '2021-03-29 14:39:23', 'jiageng', '2021-04-13 10:33:29', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2112, '列表', 2111, 1, '', NULL, 1, 'F', '0', 'transfer:attribute:list', '#', 0, 'admin', '2021-03-29 14:39:46', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2113, '修改', 2111, 2, '', NULL, 1, 'F', '0', 'transfer:attribute:edit', '#', 0, 'admin', '2021-03-29 14:40:02', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2114, '快照传输', 2110, 2, 'SnapshotTransfer', 'snapshot/transfer/index', 1, 'C', '0', 'snapshot:transfer:query', 'color', 0, 'admin', '2021-03-29 14:40:32', 'jiageng', '2021-04-13 10:33:36', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2115, '列表', 2114, 1, '', NULL, 1, 'F', '0', 'snapshot:transfer:list', '#', 0, 'admin', '2021-03-29 14:40:52', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2116, '添加', 2114, 2, '', NULL, 1, 'F', '0', 'snapshot:transfer:add', '#', 0, 'admin', '2021-03-29 14:41:05', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2117, '修改', 2114, 3, '', NULL, 1, 'F', '0', 'snapshot:transfer:edit', '#', 0, 'admin', '2021-03-29 14:41:19', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2118, '删除', 2114, 4, '', NULL, 1, 'F', '0', 'snapshot:transfer:remove', '#', 0, 'admin', '2021-03-29 14:41:32', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2119, '导出', 2114, 5, '', NULL, 1, 'F', '0', 'snapshot:transfer:export', '#', 0, 'admin', '2021-03-29 14:41:46', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2120, '模版管理', 2024, 1, 'Template', 'view/template/index', 1, 'C', '0', 'view:template:list', 'documentation', 0, 'admin', '2021-04-11 18:59:10', 'admin', '2021-04-11 19:00:11', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2121, '列表', 2120, 1, '', NULL, 1, 'F', '0', 'view:template:list', '#', 0, 'admin', '2021-04-11 19:00:37', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2122, '添加', 2120, 2, '', NULL, 1, 'F', '0', 'view:template:add', '#', 0, 'admin', '2021-04-11 19:00:50', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2123, '修改', 2120, 3, '', NULL, 1, 'F', '0', 'view:template:edit', '#', 0, 'admin', '2021-04-11 19:01:07', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2124, '删除', 2120, 4, '', NULL, 1, 'F', '0', 'view:template:remove', '#', 0, 'admin', '2021-04-11 19:01:23', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2125, '导出', 2120, 5, '', NULL, 1, 'F', '0', 'view:template:export', '#', 0, 'admin', '2021-04-11 19:01:38', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2126, '船舶页面', 0, 29, 'ship.html', NULL, 1, 'M', '0', NULL, 'example', 1, 'admin', '2021-04-16 17:12:40', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2127, '设备属性', 2111, 3, '', NULL, 1, 'F', '0', 'device:attribute:list', '#', 0, 'admin', '2021-05-13 15:38:54', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2128, '采集终端', 2035, 3, 'acquisitionMiddleware', 'device/acquisitionMiddleware/index', 1, 'C', '0', 'device:info:list', 'build', 0, 'admin', '2021-11-08 17:40:22', 'admin', '2021-11-08 17:44:21', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2129, '列表', 2128, 1, '', NULL, 1, 'F', '0', 'device:info:list', '#', 0, 'admin', '2021-11-08 17:45:30', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2130, '实时查看', 2100, 6, 'SnapshotReal', 'snapshot/real/index', 1, 'C', '0', 'snapshot:real:query', 'international', 0, 'admin', '2021-11-18 10:38:09', 'master009', '2021-12-01 16:16:09', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2131, '列表', 2130, 1, '', NULL, 1, 'F', '0', 'snapshot:real:list', '#', 0, 'admin', '2021-11-18 10:41:47', 'admin', '2021-11-18 10:42:06', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2132, '视频回放', 2100, 7, 'SnapshotVideo', 'snapshot/video/index', 1, 'C', '0', 'snapshot:video:query', 'icon', 0, 'admin', '2021-11-18 10:44:27', 'master009', '2021-12-01 16:16:13', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2133, '列表', 2132, 1, '', NULL, 1, 'F', '0', 'snapshot:video:list', '#', 0, 'admin', '2021-11-18 10:46:21', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2134, '添加', 2130, 2, '', NULL, 1, 'F', '0', 'snapshot:real:add', '#', 0, 'admin', '2021-11-26 10:33:34', 'admin', '2021-11-26 10:34:26', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2135, '添加', 2132, 2, '', NULL, 1, 'F', '0', 'snapshot:video:add', '#', 0, 'admin', '2021-11-26 10:43:46', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2136, '模板查询', 112, 1, '', NULL, 1, 'F', '0', 'system:module:query', '#', 0, 'master009', '2021-11-30 11:18:13', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2137, '模板新增', 112, 2, '', NULL, 1, 'F', '0', 'system:module:add', '#', 0, 'master009', '2021-11-30 11:18:47', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2138, '模板修改', 112, 3, '', NULL, 1, 'F', '0', 'system:module:edit', '#', 0, 'master009', '2021-11-30 11:19:16', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2139, '模板删除', 112, 4, '', NULL, 1, 'F', '0', 'system:module:remove', '#', 0, 'master009', '2021-11-30 11:19:32', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2140, '模板导出', 112, 5, '', NULL, 1, 'F', '0', 'system:module:export', '#', 0, 'master009', '2021-11-30 14:43:27', 'master009', '2021-11-30 15:21:43', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2141, '船舶终端管理', 2035, 4, 'shipTerminalManage', 'device/shipTerminalManage/index', 1, 'C', '0', 'device:shipTerminalManage:list', 'table', 0, 'master009', '2021-12-01 10:32:06', 'master009', '2021-12-01 15:10:47', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2142, '列表', 2141, 1, '', NULL, 1, 'F', '0', 'device:shipTerminalManage:list', '#', 0, 'master009', '2021-12-01 11:04:48', 'master009', '2021-12-01 15:10:57', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2154, '图片删除', 2149, 4, '', NULL, 1, 'F', '0', 'system:backgroundpic:remove', '#', 0, 'master009', '2021-12-20 15:21:19', 'master009', '2021-12-28 09:54:36', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2153, '图片修改', 2149, 3, '', NULL, 1, 'F', '0', 'system:backgroundpic:edit', '#', 0, 'master009', '2021-12-20 15:20:44', 'master009', '2021-12-27 15:34:43', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2152, '添加图片', 2149, 2, '', NULL, 1, 'F', '0', 'system:backgroundpic:add', '#', 0, 'master009', '2021-12-20 15:20:03', '', NULL, '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2151, '图片查询', 2149, 1, '', NULL, 1, 'F', '0', 'system:backgroundpic:query', '#', 0, 'master009', '2021-12-20 15:19:12', 'master009', '2021-12-27 15:34:35', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2149, '图片管理', 1, 10, 'BackgroundPic', 'system/backgroundpic/index', 1, 'C', '0', 'system:backgroundpic:list', 'chart', 0, 'admin', '2021-12-20 14:49:53', 'master009', '2021-12-20 15:02:42', '');
INSERT INTO `dct`.`sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `menu_type`, `visible`, `perms`, `icon`, `target_system`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2169, '船端操作管理', 1, 11, 'sheetacontrol', 'system/sheetacontrol/index', 1, 'C', '0', '', 'edit', 0, 'master009', '2022-04-26 20:23:48', 'master009', '2022-04-27 09:57:50', '船端操作管理菜单');

-- ----------------------------
-- 初始化-角色和菜单关联表数据
-- ----------------------------
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2135);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2134);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2133);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2132);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2131);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2130);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2129);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2128);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2127);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2126);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2119);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2118);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2117);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2116);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2115);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2114);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2113);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2112);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2111);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2110);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2109);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2108);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2107);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2106);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2105);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2104);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2103);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2102);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2101);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2100);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2056);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2055);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2053);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2052);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2048);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2044);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2043);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2042);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2041);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2040);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2039);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2038);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2037);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2035);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2023);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2022);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2021);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2020);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2019);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2018);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2017);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2016);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2015);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2014);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2013);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2012);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (3, 2011);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2154);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2153);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2152);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2151);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2149);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2142);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2141);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2140);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2139);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2138);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2137);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2136);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2135);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2134);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2133);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2132);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2131);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2130);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2129);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2128);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2127);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2125);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2124);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2123);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2122);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2121);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2120);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2119);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2118);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2117);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2116);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2115);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2114);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2113);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2112);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2111);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2110);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2109);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2108);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2107);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2106);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2105);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2104);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2103);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2102);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2101);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2100);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2093);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2092);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2091);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2090);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2089);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2088);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2052);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2048);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2044);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2043);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2042);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2041);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2040);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2039);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2038);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2037);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2035);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2030);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2029);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2028);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2027);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2026);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2025);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2024);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2023);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2022);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2021);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2020);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2019);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2018);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2017);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2016);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2015);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2014);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2013);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2012);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2011);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1054);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1053);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1052);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1051);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1050);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1049);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1048);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1047);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1046);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1045);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1044);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1043);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1042);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1041);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1040);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1039);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1038);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1037);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1036);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1035);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1034);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1033);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1032);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1031);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1030);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1029);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1028);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1027);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1026);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1025);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1024);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1023);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1022);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1021);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1020);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1019);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1018);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1017);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1016);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1015);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1014);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1013);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1012);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1011);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1010);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1009);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1008);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1007);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1006);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1005);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1004);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1003);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1002);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1001);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 501);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 500);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 112);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 111);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 110);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 109);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 108);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 107);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 106);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 105);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 104);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 103);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 102);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 101);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 100);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 2);
INSERT INTO `dct`.`sys_role_menu`(`role_id`, `menu_id`) VALUES (2, 1);





-- ----------------------------
-- 初始化-角色和部门关联表数据
-- ----------------------------
insert into sys_role_dept values ('2', '100');
insert into sys_role_dept values ('2', '101');
insert into sys_role_dept values ('2', '105');


-- ----------------------------
-- 初始化-用户与岗位关联表数据
-- ----------------------------
insert into sys_user_post values ('1', '1');
insert into sys_user_post values ('2', '2');



INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '用户性别列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '菜单状态列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '系统开关列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '任务状态列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '任务分组列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '系统是否列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '通知类型列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '通知状态列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '操作类型列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '登录状态列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (11, '连接方式', 'connect_type', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '连接方式列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, '设备类型', 'device_type', '0', 'admin', '2020-04-26 18:54:57', 'system', '2020-05-09 09:37:24', '设备类型列表');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, '页面类型', 'page_type', '0', 'admin', '2021-04-12 20:03:46', '', NULL, '视图模版中页面类型，对应大屏展示系统页面');
INSERT INTO `dct`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, '模板类型', 'module_type', '0', 'admin', '2021-11-12 12:05:12', 'admin', '2021-11-12 12:05:34', '模板管理');


INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '性别男');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '性别女');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '性别未知');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '显示菜单');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '隐藏菜单');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '正常状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '停用状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '正常状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '停用状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '默认分组');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '系统分组');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '系统默认是');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '系统默认否');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '通知');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '公告');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '正常状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '关闭状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (18, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '新增操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (19, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '修改操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (20, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '删除操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (21, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '授权操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (22, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '导出操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (23, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '导入操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (24, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '强退操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (25, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '生成操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (26, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '清空操作');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (27, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '正常状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (28, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '停用状态');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (29, 1, '采集终端', '0', 'connect_type', '', '', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '采集终端');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (30, 2, '串口连接', '1', 'connect_type', '', '', 'Y', '0', 'admin', '2018-03-16 11:33:00', 'system', '2018-03-16 11:33:00', '串口连接');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, 0, 'Sf-3050', '32', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:56:01', 'admin', '2020-04-28 14:35:50', '星站差分GPS ，优先传输通道');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, 1, 'ATTITUDE', '33', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:56:21', 'admin', '2020-04-28 14:35:55', '姿态仪');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, 2, 'SEAPATH', '34', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:56:42', 'admin', '2020-04-28 14:35:59', '声学GPS');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, 4, 'EA600', '35', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:04', 'admin', '2020-04-28 14:36:03', '水深');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (104, 3, 'WINCH', '36', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:22', 'admin', '2020-04-28 14:36:07', '绞车');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (105, 5, 'SBE21', '37', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:42', 'admin', '2020-04-28 14:36:12', '走航SBE21');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (106, 6, 'AWS', '38', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:57:57', 'admin', '2020-04-28 14:36:17', '气象站');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (107, 7, 'GO8050', '39', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:58:19', 'admin', '2020-04-28 14:36:20', '走航CO2');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (108, 8, 'LOG', '40', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:58:34', 'admin', '2020-04-28 14:36:23', '计程仪');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (109, 9, 'ECHO', '41', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:58:47', 'admin', '2020-04-28 14:36:27', '测深（浅水）');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (110, 10, 'MAG', '42', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:03', 'admin', '2020-04-28 14:36:31', '电罗经');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (111, 11, 'DGPS1', '43', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:17', 'admin', '2020-04-28 14:36:34', '船载GPS1');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (112, 12, 'DGPS2', '44', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:32', 'admin', '2020-04-28 14:36:37', '船载GPS2');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (113, 13, 'AIS', '45', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:45', 'admin', '2020-04-28 14:36:46', '船载AIS');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (114, 14, 'WIND', '46', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 18:59:56', 'admin', '2020-04-28 14:36:49', '船载风速风向');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (115, 15, 'RA-CDU1', '47', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:09', 'admin', '2020-04-28 14:36:53', '手动舵1');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (116, 16, 'RA-CDU2', '48', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:22', 'admin', '2020-04-28 14:36:56', '手动舵2');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (117, 17, 'AUTO PIOLT', '49', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:34', 'admin', '2020-04-28 14:37:00', '自动舵');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (118, 18, 'GYRO', '50', 'device_type', '', '', 'N', '0', 'admin', '2020-04-26 19:00:46', 'admin', '2020-04-28 14:37:03', '磁罗经');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (119, 1, 'IP认证', '0', 'sys_user_authentication', NULL, NULL, 'Y', '0', 'admin', '2020-12-29 10:57:46', 'admin', '2020-12-29 10:58:03', 'IP认证');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (120, 2, '账号认证', '1', 'sys_user_authentication', NULL, NULL, 'N', '0', 'admin', '2020-12-29 10:59:16', 'admin', '2020-12-29 10:59:28', '账号认证');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (121, 3, '双重认证', '2', 'sys_user_authentication', NULL, NULL, 'N', '0', 'admin', '2020-12-29 11:00:12', 'admin', '2020-12-29 11:00:19', '双重认证');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (122, 1, '首页面', 'INDEX', 'page_type', NULL, NULL, 'N', '0', 'admin', '2021-04-12 20:07:10', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (123, 2, 'webConning', 'WEB_CONNING', 'page_type', NULL, NULL, 'N', '0', 'admin', '2021-04-12 20:08:03', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (124, 3, '视频监控', 'SNAPSHOT', 'page_type', NULL, NULL, 'N', '0', 'admin', '2021-04-12 20:08:50', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (125, 4, '实时轨迹', 'MAPGIS', 'page_type', NULL, NULL, 'N', '0', 'admin', '2021-04-12 20:09:34', '', NULL, NULL);
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (126, 1, '船只管理', '0', 'module_type', NULL, NULL, 'N', '0', 'admin', '2021-11-12 12:06:03', 'admin', '2021-11-12 12:07:42', '船只管理');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (127, 2, '航次管理', '1', 'module_type', NULL, NULL, 'N', '0', 'admin', '2021-11-12 12:31:29', '', NULL, '航次管理');
INSERT INTO `dct`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (128, 3, '站位管理', '2', 'module_type', NULL, NULL, 'N', '0', 'admin', '2021-11-12 12:31:41', '', NULL, '站位管理');


insert into sys_config values(1, '主框架页-默认皮肤样式名称', 'sys.index.skinName',     'skin-blue',     'Y', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow' );
insert into sys_config values(2, '用户管理-账号初始密码',     'sys.user.initPassword',  '123456',        'Y', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '初始化密码 123456' );
insert into sys_config values(3, '主框架页-侧边栏主题',       'sys.index.sideTheme',    'theme-dark',    'Y', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '深色主题theme-dark，浅色主题theme-light' );



insert into sys_job values(1, '系统默认（无参）', 'DEFAULT', 'scheduledTask.noParams',        '0/10 * * * * ?', '3', '1', '1', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_job values(2, '系统默认（有参）', 'DEFAULT', 'scheduledTask.haveParams(\'xhjt\')',  '0/15 * * * * ?', '3', '1', '1', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');
insert into sys_job values(3, '系统默认（多参）', 'DEFAULT', 'scheduledTask.multipleParams(\'xhjt\', true, 2000L, 316.50D, 100)',  '0/20 * * * * ?', '3', '1', '1', 'admin', '2018-03-16 11-33-00', 'system', '2018-03-16 11-33-00', '');

-- ----------------------------
-- 初始化-船只、航次、站位固定数据
-- ----------------------------
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (1, 'sn号', 'sn', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 1, 0, 0, 2, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (2, '船只名称', 'name', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 1, 1, 0, 1, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (3, 'MMSI', 'mmsi', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 1, 1, 0, 3, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (4, '呼号', 'callSign', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 1, 1, 0, 4, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (5, 'IMO', 'imo', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 1, 1, 0, 5, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (6, '状态', 'status', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 1, 0, 2, 6, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (7, '创建者', 'createBy', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 0, 0, 0, 0, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (8, '创建时间', 'createTime', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 0, 0, 1, 0, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (9, '更新者', 'updateBy', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 0, 0, 0, 0, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (10, '更新时间', 'updateTime', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 0, 0, 1, 0, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (11, '部门编号', 'deptId', 'admin', '2021-11-02 16:28:45', 'admin', '2021-11-02 16:28:51', 0, 0, 0, 0, 0, 0, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (13, '船名', 'shipName', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 1, 0, 1, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (14, '航次编号', 'code', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 1, 0, 2, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (15, '航次名称', 'cruiseName', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 1, 0, 3, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (16, '船长', 'captain', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 0, 0, 0, 4, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (17, '开始时间', 'startTime', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 1, 1, 5, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (18, '结束时间', 'endTime', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 1, 1, 6, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (19, '计划开始时间', 'planStartTime', 'admin', '2021-11-16 10:46:55', '', NULL, 1, 0, 1, 1, 1, 7, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (20, '计划结束时间', 'planEndTime', 'admin', '2021-11-16 10:46:55', '', NULL, 1, 0, 1, 1, 1, 8, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (21, '目的海域', 'seaArea', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 1, 0, 9, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (22, '编辑时间', 'editTime', 'admin', '2021-11-11 15:36:05', '', NULL, 1, 0, 1, 0, 1, 10, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (23, '部门编号', 'deptId', 'admin', '2021-11-15 14:34:57', 'admin', '2021-11-15 14:36:28', 1, 0, 0, 0, 0, 11, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (24, 'sn', 'sn', 'admin', '2021-11-15 14:34:57', 'admin', '2021-11-15 14:36:28', 1, 0, 0, 0, 0, 12, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (25, '创建者', 'createBy', 'admin', '2021-11-15 14:34:57', 'admin', '2021-11-15 14:36:28', 1, 0, 0, 0, 0, 13, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (26, '创建时间', 'createTime', 'admin', '2021-11-15 17:31:29', '', NULL, 1, 0, 0, 0, 1, 14, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (27, '更新者', 'updateBy', 'admin', '2021-11-15 14:34:57', 'admin', '2021-11-15 14:36:28', 1, 0, 0, 0, 0, 15, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (28, '更新时间', 'updateTime', 'admin', '2021-11-15 17:29:37', '', NULL, 1, 0, 0, 0, 1, 16, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (29, '站位名称', 'stationName', 'admin', '2021-11-15 14:30:06', '', NULL, 2, 0, 1, 1, 0, 1, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (30, '站位编码', 'stationCode', 'admin', '2021-11-17 10:58:03', '', NULL, 2, 0, 0, 1, 0, 2, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (31, '船只名称', 'shipName', 'admin', '2021-11-16 11:46:19', '', NULL, 2, 0, 1, 1, 0, 3, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (32, '航次名称', 'cruiseName', 'admin', '2021-11-16 15:10:52', '', NULL, 2, 0, 1, 1, 0, 4, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (33, '航次编码', 'cruiseCode', 'admin', '2021-11-16 11:46:19', '', NULL, 2, 0, 1, 0, 0, 5, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (34, '经度', 'longitude', 'admin', '2021-11-16 11:46:19', '', NULL, 2, 0, 1, 1, 0, 6, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (35, '纬度', 'latitude', 'admin', '2021-11-16 11:46:19', '', NULL, 2, 0, 1, 1, 0, 7, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (36, '计划到站时间', 'planArrivalTime', 'admin', '2021-11-17 12:46:51', 'admin', '2021-11-17 12:47:21', 2, 0, 1, 1, 1, 8, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (37, '计划离站时间', 'planDepartureTime', 'admin', '2021-11-17 12:46:51', 'admin', '2021-11-17 12:47:21', 2, 0, 1, 1, 1, 9, 1, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (38, '实际到站时间', 'arrivalTime', 'admin', '2021-11-16 11:46:19', '', NULL, 2, 0, 1, 0, 1, 10, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (39, '实际离站时间', 'departureTime', 'admin', '2021-11-16 11:46:19', '', NULL, 2, 0, 1, 0, 1, 11, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (40, '创建者', 'createBy', 'admin', '2021-11-15 14:34:57', 'admin', '2021-11-15 14:36:28', 2, 0, 0, 0, 0, 12, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (41, '创建时间', 'createTime', 'admin', '2021-11-15 17:31:29', '', NULL, 2, 0, 0, 0, 1, 13, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (42, '更新者', 'updateBy', 'admin', '2021-11-15 14:34:57', 'admin', '2021-11-15 14:36:28', 2, 0, 0, 0, 0, 14, 0, 100, 100);
INSERT INTO `dct`.`template_config`(`id`, `label`, `property`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `field_type`, `table_show`, `form_show`, `character_type`, `sort_num`, `is_required`, `com_id`, `dept_id`) VALUES (43, '更新时间', 'updateTime', 'admin', '2021-11-15 17:29:37', '', NULL, 2, 0, 0, 0, 1, 15, 0, 100, 100);

-- ----------------------------
-- 15、内置GPS 罗经
-- ----------------------------
INSERT INTO `dct`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '0', '内置GPS', '53', 'device_type', NULL, NULL, 'N', '0', 'admin', '2022-07-02 14:53:22', '', NULL, '内置GPS');
INSERT INTO `dct`.`sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '10', '内置COMPASS', '54', 'device_type', NULL, NULL, 'N', '0', 'admin', '2022-07-02 14:53:22', '', NULL, '内置COMPASS');




