-- ----------------------------
-- 1、部门表
-- ----------------------------
drop table if exists sys_dept;
create table sys_dept (
  dept_id           bigint(20)      not null auto_increment    comment '部门id',
  parent_id         bigint(20)      default 0                  comment '父部门id',
  ancestors         varchar(50)     default ''                 comment '祖级列表',
  dept_name         varchar(30)     default ''                 comment '部门名称',
  order_num         int(4)          default 0                  comment '显示顺序',
  leader            varchar(20)     default null               comment '负责人',
  phone             varchar(11)     default null               comment '联系电话',
  email             varchar(50)     default null               comment '邮箱',
  status            char(1)         default '0'                comment '部门状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time 	    datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (dept_id)
) engine=innodb auto_increment=200 comment = '部门表';


-- ----------------------------
-- 2、用户信息表
-- ----------------------------
drop table if exists sys_user;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '手机号码',
  `authentication` varchar(20) DEFAULT NULL,
  `sex` char(1) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '最后登陆IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登陆时间',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '备注',
  `binding_ip` varchar(200) DEFAULT NULL COMMENT '绑定IP',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8mb3 COMMENT='用户信息表';


-- ----------------------------
-- 3、岗位信息表
-- ----------------------------
drop table if exists sys_post;
create table sys_post
(
  post_id       bigint(20)      not null auto_increment    comment '岗位ID',
  post_code     varchar(64)     not null                   comment '岗位编码',
  post_name     varchar(50)     not null                   comment '岗位名称',
  post_sort     int(4)          not null                   comment '显示顺序',
  status        char(1)         not null                   comment '状态（0正常 1停用）',
  create_by     varchar(64)     default ''                 comment '创建者',
  create_time   datetime                                   comment '创建时间',
  update_by     varchar(64)     default ''			       comment '更新者',
  update_time   datetime                                   comment '更新时间',
  remark        varchar(500)    default null               comment '备注',
  primary key (post_id)
) engine=innodb comment = '岗位信息表';

-- ----------------------------
-- 4、角色信息表
-- ----------------------------
drop table if exists sys_role;
create table sys_role (
  role_id           bigint(20)      not null auto_increment    comment '角色ID',
  dept_id      bigint(20)     default null                   comment '部门ID',
  role_name         varchar(30)     not null                   comment '角色名称',
  role_key          varchar(100)    not null                   comment '角色权限字符串',
  role_sort         int(4)          not null                   comment '显示顺序',
  data_scope        char(1)         default '1'                comment '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  status            char(1)         not null                   comment '角色状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (role_id)
) engine=innodb auto_increment=100 comment = '角色信息表';

-- ----------------------------
-- 5、菜单权限表
-- ----------------------------
drop table if exists sys_menu;
create table sys_menu (
  menu_id           bigint(20)      not null auto_increment    comment '菜单ID',
  menu_name         varchar(50)     not null                   comment '菜单名称',
  parent_id         bigint(20)      default 0                  comment '父菜单ID',
  order_num         int(4)          default 0                  comment '显示顺序',
  path              varchar(200)    default ''                 comment '路由地址',
  component         varchar(255)    default null               comment '组件路径',
  is_frame          int(1)          default 1                  comment '是否为外链（0是 1否）',
  menu_type         char(1)         default ''                 comment '菜单类型（M目录 C菜单 F按钮）',
  visible           char(1)         default 0                  comment '菜单状态（0显示 1隐藏）',
  perms             varchar(100)    default null               comment '权限标识',
  icon              varchar(100)    default '#'                comment '菜单图标',
  target_system     int(1)          DEFAULT 0                  COMMENT '菜单目标系统',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default ''                 comment '备注',
  primary key (menu_id)
) engine=innodb auto_increment=2000 comment = '菜单权限表';

-- ----------------------------
-- 6、用户和角色关联表  用户N-1角色
-- ----------------------------
drop table if exists sys_user_role;
create table sys_user_role (
  user_id   bigint(20) not null comment '用户ID',
  role_id   bigint(20) not null comment '角色ID',
  primary key(user_id, role_id)
) engine=innodb comment = '用户和角色关联表';

-- ----------------------------
-- 7、角色和菜单关联表  角色1-N菜单
-- ----------------------------
drop table if exists sys_role_menu;
create table sys_role_menu (
  role_id   bigint(20) not null comment '角色ID',
  menu_id   bigint(20) not null comment '菜单ID',
  primary key(role_id, menu_id)
) engine=innodb comment = '角色和菜单关联表';

-- ----------------------------
-- 8、角色和部门关联表  角色1-N部门
-- ----------------------------
drop table if exists sys_role_dept;
create table sys_role_dept (
  role_id   bigint(20) not null comment '角色ID',
  dept_id   bigint(20) not null comment '部门ID',
  primary key(role_id, dept_id)
) engine=innodb comment = '角色和部门关联表';

-- ----------------------------
-- 9、用户与岗位关联表  用户1-N岗位
-- ----------------------------
drop table if exists sys_user_post;
create table sys_user_post
(
  user_id   bigint(20) not null comment '用户ID',
  post_id   bigint(20) not null comment '岗位ID',
  primary key (user_id, post_id)
) engine=innodb comment = '用户与岗位关联表';

-- ----------------------------
-- 10、操作日志记录
-- ----------------------------
drop table if exists sys_oper_log;
create table sys_oper_log (
  oper_id           bigint(20)      not null auto_increment    comment '日志主键',
  title             varchar(50)     default ''                 comment '模块标题',
  business_type     int(2)          default 0                  comment '业务类型（0其它 1新增 2修改 3删除）',
  method            varchar(100)    default ''                 comment '方法名称',
  request_method    varchar(10)     default ''                 comment '请求方式',
  operator_type     int(1)          default 0                  comment '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name         varchar(50)     default ''                 comment '操作人员',
  dept_name         varchar(50)     default ''                 comment '部门名称',
  oper_url          varchar(255)    default ''                 comment '请求URL',
  oper_ip           varchar(50)     default ''                 comment '主机地址',
  oper_location     varchar(255)    default ''                 comment '操作地点',
  oper_param        varchar(2000)   default ''                 comment '请求参数',
  json_result       varchar(2000)   default ''                 comment '返回参数',
  status            int(1)          default 0                  comment '操作状态（0正常 1异常）',
  error_msg         varchar(2000)   default ''                 comment '错误消息',
  oper_time         datetime                                   comment '操作时间',
  primary key (oper_id)
) engine=innodb auto_increment=100 comment = '操作日志记录';

-- ----------------------------
-- 11、字典类型表
-- ----------------------------
drop table if exists sys_dict_type;
create table sys_dict_type
(
  dict_id          bigint(20)      not null auto_increment    comment '字典主键',
  dict_name        varchar(100)    default ''                 comment '字典名称',
  dict_type        varchar(100)    default ''                 comment '字典类型',
  status           char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by        varchar(64)     default ''                 comment '创建者',
  create_time      datetime                                   comment '创建时间',
  update_by        varchar(64)     default ''                 comment '更新者',
  update_time      datetime                                   comment '更新时间',
  remark           varchar(500)    default null               comment '备注',
  primary key (dict_id),
  unique (dict_type)
) engine=innodb auto_increment=100 comment = '字典类型表';

-- ----------------------------
-- 12、字典数据表
-- ----------------------------
drop table if exists sys_dict_data;
create table sys_dict_data
(
  dict_code        bigint(20)      not null auto_increment    comment '字典编码',
  dict_sort        int(4)          default 0                  comment '字典排序',
  dict_label       varchar(100)    default ''                 comment '字典标签',
  dict_value       varchar(100)    default ''                 comment '字典键值',
  dict_type        varchar(100)    default ''                 comment '字典类型',
  css_class        varchar(100)    default null               comment '样式属性（其他样式扩展）',
  list_class       varchar(100)    default null               comment '表格回显样式',
  is_default       char(1)         default 'N'                comment '是否默认（Y是 N否）',
  status           char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by        varchar(64)     default ''                 comment '创建者',
  create_time      datetime                                   comment '创建时间',
  update_by        varchar(64)     default ''                 comment '更新者',
  update_time      datetime                                   comment '更新时间',
  remark           varchar(500)    default null               comment '备注',
  primary key (dict_code)
) engine=innodb auto_increment=100 comment = '字典数据表';

-- ----------------------------
-- 13、参数配置表
-- ----------------------------
drop table if exists sys_config;
create table sys_config (
  config_id         int(5)          not null auto_increment    comment '参数主键',
  config_name       varchar(100)    default ''                 comment '参数名称',
  config_key        varchar(100)    default ''                 comment '参数键名',
  config_value      varchar(500)    default ''                 comment '参数键值',
  config_type       char(1)         default 'N'                comment '系统内置（Y是 N否）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (config_id)
) engine=innodb auto_increment=100 comment = '参数配置表';

-- ----------------------------
-- 14、系统访问记录
-- ----------------------------
drop table if exists sys_logininfor;
create table sys_logininfor (
  info_id        bigint(20)     not null auto_increment   comment '访问ID',
  user_name      varchar(50)    default ''                comment '用户账号',
  ipaddr         varchar(50)    default ''                comment '登录IP地址',
  login_location varchar(255)   default ''                comment '登录地点',
  browser        varchar(50)    default ''                comment '浏览器类型',
  os             varchar(50)    default ''                comment '操作系统',
  status         char(1)        default '0'               comment '登录状态（0成功 1失败）',
  msg            varchar(255)   default ''                comment '提示消息',
  login_time     datetime                                 comment '访问时间',
  primary key (info_id)
) engine=innodb auto_increment=100 comment = '系统访问记录';

-- ----------------------------
-- 15、定时任务调度表
-- ----------------------------
drop table if exists sys_job;
create table sys_job (
  job_id              bigint(20)    not null auto_increment    comment '任务ID',
  job_name            varchar(64)   default ''                 comment '任务名称',
  job_group           varchar(64)   default 'DEFAULT'          comment '任务组名',
  invoke_target       varchar(500)  not null                   comment '调用目标字符串',
  cron_expression     varchar(255)  default ''                 comment 'cron执行表达式',
  misfire_policy      varchar(20)   default '3'                comment '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  concurrent          char(1)       default '1'                comment '是否并发执行（0允许 1禁止）',
  status              char(1)       default '0'                comment '状态（0正常 1暂停）',
  create_by           varchar(64)   default ''                 comment '创建者',
  create_time         datetime                                 comment '创建时间',
  update_by           varchar(64)   default ''                 comment '更新者',
  update_time         datetime                                 comment '更新时间',
  remark              varchar(500)  default ''                 comment '备注信息',
  primary key (job_id, job_name, job_group)
) engine=innodb auto_increment=100 comment = '定时任务调度表';

-- ----------------------------
-- 16、定时任务调度日志表
-- ----------------------------
drop table if exists sys_job_log;
create table sys_job_log (
  job_log_id          bigint(20)     not null auto_increment    comment '任务日志ID',
  job_name            varchar(64)    not null                   comment '任务名称',
  job_group           varchar(64)    not null                   comment '任务组名',
  invoke_target       varchar(500)   not null                   comment '调用目标字符串',
  job_message         varchar(500)                              comment '日志信息',
  status              char(1)        default '0'                comment '执行状态（0正常 1失败）',
  exception_info      varchar(2000)  default ''                 comment '异常信息',
  create_time         datetime                                  comment '创建时间',
  primary key (job_log_id)
) engine=innodb comment = '定时任务调度日志表';

-- ----------------------------
-- 17、通知公告表
-- ----------------------------
drop table if exists sys_notice;
create table sys_notice (
  notice_id         int(4)          not null auto_increment    comment '公告ID',
  notice_title      varchar(50)     not null                   comment '公告标题',
  notice_type       char(1)         not null                   comment '公告类型（1通知 2公告）',
  notice_content    varchar(2000)   default null               comment '公告内容',
  status            char(1)         default '0'                comment '公告状态（0正常 1关闭）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(255)    default null               comment '备注',
  primary key (notice_id)
) engine=innodb auto_increment=10 comment = '通知公告表';

-- ----------------------------
-- 21、航次表
-- ----------------------------
drop table if exists cruise;
CREATE TABLE `cruise` (
  `cruise_id` bigint NOT NULL AUTO_INCREMENT COMMENT '航次ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `sn` varchar(20) NOT NULL COMMENT '船只sn号',
  `captain` varchar(20) DEFAULT '' COMMENT '船长',
  `code` varchar(20) NOT NULL COMMENT '航次编号',
  `start_time` bigint DEFAULT NULL COMMENT '航次开始时间',
  `finish_time` bigint DEFAULT NULL COMMENT '航次结束时间',
  `total_days` int DEFAULT NULL COMMENT '总天数',
  `history_mileage` double DEFAULT NULL COMMENT '历史里程',
  `start_port` varchar(100) DEFAULT '' COMMENT '起始地',
  `end_port` varchar(100) DEFAULT '' COMMENT '目的地',
  `sea_area` varchar(100) DEFAULT '' COMMENT '目标海域',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `module_json` text CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT '属性json',
  PRIMARY KEY (`cruise_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci COMMENT='航次表';

-- ----------------------------
-- 25、数据接收记录
-- ----------------------------
drop table if exists receive_log;
create table receive_log (
  id          bigint(20)         not null auto_increment    comment 'ID',
  dept_id      bigint(20)     default null                   comment '部门ID',
  sn          varchar(20)     not null                   comment '服务器sn号',
  record_time      bigint(20)     default null                  comment '记录时间',
  total_length      int(4)       default null                   comment '总长度',
  total_lines            int(4)    default null               comment '总条数',
  repair_length            int(4)    default null             comment '补数据长度',
  pic_length            int(4)    default null               comment '图片数据长度',

  create_time       datetime                                   comment '创建时间',
  primary key (id)
) engine=innodb auto_increment=10 comment = '数据接收记录表';

-- ----------------------------
-- 33、机舱数据结构
-- ----------------------------
drop table if exists engine_room_config;
create table engine_room_config (
  id          bigint(20)         not null auto_increment    comment 'ID',
  sn          varchar(20)     not null                   comment '服务器sn号',
  code         int(4)         not null                 comment '编号',
  code_str     varchar(50)    default null             comment '编号字符串',
  name         varchar(50)    default ''               comment '名称',
  symbol       varchar(20)    default ''               comment '单位',

  create_time       datetime                                   comment '创建时间',
  update_time       datetime        default null               comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=50 comment = '机舱数据结构表';

-- ----------------------------
-- 37、航次站位表
-- ----------------------------
drop table if exists cruise_station;
CREATE TABLE `cruise_station` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '航次站位ID',
  `sn` varchar(20) NOT NULL COMMENT '船只sn号',
  `cruise_id` bigint NOT NULL COMMENT '航次ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `station_code` varchar(50) DEFAULT '' COMMENT '站位编号',
  `longitude` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '经度',
  `latitude` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '纬度',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `module_json` text CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT '属性json',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci COMMENT='航次站位表';

-- ----------------------------
-- 38、行程动作节点表
-- ----------------------------
drop table if exists travel_action_node;
create table travel_action_node (
  id          bigint(20)         not null auto_increment    comment 'ID',
  sn          varchar(20)     not null                   comment '服务器sn号',
  cruise_id      bigint(20)     not null                   comment '航次ID',
  time      bigint(20)     default null            comment '时间',
  longitude       varchar(50)        default null                   comment '经度',
  latitude      varchar(50)     default null                   comment '纬度',
  ground_rate       double        default null                   comment '船速',
  mt4_mtr_speed       double        default null                   comment '主推4马达转速',
  mt5_mtr_speed       double        default null                   comment '主推5马达转速',
  relation_station      varchar(50)     default ''                   comment '关联站位',
  relation_station_distance     double        default null                   comment '和关联站位的距离',
  action_type      int     default null                   comment '行为类型',

  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime        default null               comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=10 comment = '行程动作节点表';

-- ----------------------------
-- 40、船只表
-- ----------------------------
drop table if exists ship;
CREATE TABLE `ship` (
  `ship_id` bigint NOT NULL AUTO_INCREMENT COMMENT '船只ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  `sn` varchar(10) NOT NULL COMMENT 'sn',
  `name` varchar(50) NOT NULL COMMENT '船只名称',
  `mmsi` varchar(20) DEFAULT NULL COMMENT 'MMSI',
  `call_sign` varchar(20) DEFAULT NULL COMMENT '呼号',
  `imo` varchar(20) DEFAULT NULL COMMENT 'IMO',
  `status` int DEFAULT '0' COMMENT '状态',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `module_json` text CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT '属性json',
  PRIMARY KEY (`ship_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci COMMENT='船只表';

-- ----------------------------
-- 41、设备表
-- ----------------------------
drop table if exists device;
create table device (
  id          bigint(20)         not null auto_increment    comment '设备ID',
  dept_id      bigint(20)     not null                   comment '部门ID',
  sn      varchar(10)     not null                   comment '船sn',
  name      varchar(50)     not null                   comment '设备名称',
  type       int(4)        not null                   comment '设备类型',
  code       varchar(20)        not null                   comment '设备编号',
  enable     int(4)            default 0              COMMENT '启用状态',
  mac        varchar(20)         default null        comment '机器MAC地址',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime        default null               comment '更新时间',
  remark            varchar(255)    default null               comment '备注',
`transfer_status` int DEFAULT '0' COMMENT '传输状态',
  `compartment` int DEFAULT '300' COMMENT '传输间隔(单位：秒)',
  `connect_type` int NOT NULL COMMENT '连接方式',
  `baud_rate` int DEFAULT NULL COMMENT '波特率',
  `data_bits` int DEFAULT NULL COMMENT '数据位',
  `stop_bits` int DEFAULT NULL COMMENT '停止位',
  `parity` int DEFAULT NULL COMMENT '校验位',
  `serial_port` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '串口号',
`connect_status` int DEFAULT NULL COMMENT '设备连接状态',
  primary key (id)
) engine=innodb auto_increment=10 comment = '设备表';

-- ----------------------------
-- 42、设备属性
-- ----------------------------
drop table if exists device_attribute;
create table device_attribute (
  id          bigint(20)         not null auto_increment    comment 'ID',
  type       int(4)        default null                   comment '类型',
  name      varchar(50)     default null                   comment '属性名称',
  label       varchar(50)        default null                comment '属性',
  remark            varchar(255)    default null               comment '备注',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=100 comment = '设备属性';

-- ----------------------------
-- 43、传输属性
-- ----------------------------
drop table if exists transfer_attribute;
create table transfer_attribute (
  id           bigint(20)         not null auto_increment    comment 'ID',
  dept_id      bigint(20)     not null                   comment '部门ID',
  sn      varchar(10)     not null                   comment '船sn',
  device_code  varchar(20)        default null          comment '设备编码',
  name         varchar(50)     default null                   comment '属性名称',
  label       varchar(50)        default null                comment '属性',
  order_num       int(4)        default null                   comment '排序',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=100 comment = '传输属性';

-- ----------------------------
-- 44、快照通道管理表
-- ----------------------------
DROP TABLE IF EXISTS snapshot_channel;
CREATE TABLE snapshot_channel (
  id          bigint(20) NOT NULL AUTO_INCREMENT,
  dept_id      bigint(20)     not null                   comment '部门ID',
  sn      varchar(10)     not null                   comment '船sn',
  code        varchar(10) NOT NULL COMMENT '通道编号',
  name        varchar(64) default '' COMMENT '通道名称',
  address     varchar(255) default '' COMMENT '通道地址',
  storage_time int(10) DEFAULT '30' COMMENT '历史数据保存时长（单位天）',
  create_by varchar(64) COLLATE utf8_unicode_ci DEFAULT '',
  create_time datetime DEFAULT NULL,
  update_by varchar(64) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '更新者',
  update_time datetime DEFAULT NULL,
  PRIMARY KEY (id)
) engine=innodb auto_increment=500 comment = '快照通道';

-- ----------------------------
-- 45、快照传输
-- ----------------------------
drop table if exists snapshot_transfer;
create table snapshot_transfer (
  id           bigint(20)         not null auto_increment    comment 'ID',
  dept_id      bigint(20)     not null                   comment '部门ID',
  sn      varchar(10)     not null                   comment '船sn',
  channel_code  varchar(10)        NOT NULL          comment '通道编号',
  resolving_power    varchar(50)     default '640*360'         comment '分辨率(640*360)',
  compartment       int(4)       default 300                comment '传输间隔(单位：秒)',
  cost       int(4)        default 40                   comment '优先级',
  status       int(4)        default 0                   comment '状态',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
`transfer_status` int DEFAULT '0' COMMENT '传输状态',
  `connect_status` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '0' COMMENT '连接状态',
  primary key (id)
) engine=innodb auto_increment=500 comment = '快照传输';

-- ----------------------------
-- 46、快照记录
-- ----------------------------
drop table if exists snapshot_log;
create table snapshot_log (
  id           bigint(20)         not null auto_increment    comment 'ID',
  dept_id      bigint(20)     not null                   comment '部门ID',
  sn      varchar(10)     not null                   comment '船sn',
  channel_code  varchar(20)        not null          comment '通道编号',
  operate_time   bigint(20)     not null         comment '截屏时间',
  file_name       varchar(64)       default ''     comment '文件名称',
  directory       varchar(255)       default ''     comment '目录',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=500 comment = '快照记录';


-- ----------------------------
-- 47、模版
-- ----------------------------
drop table if exists view_template;
create table view_template (
  id           bigint(20)         not null auto_increment    comment 'ID',
  page_type      varchar(20)        not null                  comment '页面类型',
  name      varchar(50)     not null                   comment '模版名称',
  code      varchar(20)        not null          comment '模版编码',
  status       int(4)        default 0                   comment '状态',
  module_json    text CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT '模块',

  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=500 comment = '模版';

-- ----------------------------
-- 22、视图方案
-- ----------------------------
drop table if exists view_scheme;
create table view_scheme (
  id          bigint(20)         not null auto_increment    comment 'ID',
  dept_id      bigint(20)     default null                   comment '部门ID',
  sn      varchar(10)     not null                   comment '船sn',
  template_code     varchar(20)    not null               comment '模版',
  page_type      varchar(20)        not null                  comment '页面类型',
  name      varchar(50)     not null                   comment '名称',
  status        int(4)    default null               comment '状态',
  site_title      varchar(200)    default null               comment '站点标题',
  copyright      varchar(200)    default null               comment '版权信息',

  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime        default null               comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=10 comment = '视图方案表';

-- ----------------------------
-- 23、模块配置
-- ----------------------------
drop table if exists module_config;
create table module_config (
  id          bigint(20)         not null auto_increment    comment 'ID',
  dept_id      bigint(20)     default null                   comment '部门ID',
  scheme_id      bigint(20)     not null                   comment '方案ID',
  module_code    varchar(20)        not null                   comment '模块编码',
  module_name    varchar(50)        not null                   comment '模块名称',
  mode        int(4)    default null               comment '展示模式',
  device_code    varchar(20)        not null                   comment '设备编码',
  attribute      varchar(20)    default null               comment '属性',
  grading        int(4)    default null               comment '粒度',
  time_frame        int(4)    default null               comment '时间范围',

  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime        default null               comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=10 comment = '模块配置表';

-- ----------------------------
-- 24、采集中间件
-- ----------------------------
drop table if exists acquisition_middleware;
CREATE TABLE `acquisition_middleware` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `sn` varchar(10) NOT NULL COMMENT 'sn',
  `type` int DEFAULT NULL COMMENT '类型',
  `name` varchar(20) DEFAULT NULL COMMENT '设备名称',
  `mac` varchar(20) DEFAULT NULL COMMENT '机器MAC地址',
  `ip` varchar(200) DEFAULT NULL COMMENT '设备IP',
  `port` int DEFAULT NULL COMMENT '设备端口',
  `connect_status` int DEFAULT NULL COMMENT '设备连接状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `module_model` varchar(255) DEFAULT NULL COMMENT '模块型号',
  `code` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '采集编码',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `device_status` int DEFAULT NULL COMMENT '绑定状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='采集中间件';

-- ----------------------------
-- 25、船舶终端管理
-- ----------------------------
drop table if exists ship_terminal_manage;
CREATE TABLE `ship_terminal_manage` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `sn` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'sn',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `module_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模块型号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `serial_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '序列号',
  `soft_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '软件版本',
  `author_information` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '授权信息',
  `connect_status` int DEFAULT NULL COMMENT '连接状态(0:断开 1：连接)',
  `cpu` decimal(19,2) DEFAULT NULL COMMENT 'cpu',
  `memory` decimal(19,2) DEFAULT NULL COMMENT '内存',
  `hard_disk` decimal(19,2) DEFAULT NULL COMMENT '硬盘',
  `wan_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'WANIP',
  `lan_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'LANIP',
  `run_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '运行时长',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci COMMENT='船舶终端管理';

-- ----------------------------
-- 26、船舶模板管理
-- ----------------------------
drop table if exists template_config;
CREATE TABLE `template_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `label` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '列名称',
  `property` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '列编码',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `type` int NOT NULL DEFAULT '0' COMMENT '模板名称 0:船只 1：航次 2：站位',
  `field_type` int NOT NULL DEFAULT '0' COMMENT '字段存储方式 0:固定 1:可变',
  `table_show` int NOT NULL DEFAULT '0' COMMENT '列表是否显示 0:不显示 1：显示',
  `form_show` int NOT NULL DEFAULT '0' COMMENT '表单是否显示 0:不显示 1：显示',
  `character_type` int NOT NULL DEFAULT '0' COMMENT '字符类型 0：字符串 1：时间 2：数值 3：',
  `sort_num` bigint NOT NULL DEFAULT '0' COMMENT '排序',
  `is_required` int NOT NULL DEFAULT '0' COMMENT '是否必填 0:非必填 1：必填',
  `com_id` bigint NOT NULL COMMENT '公司ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=255 DEFAULT CHARSET=utf8;

-- ----------------------------
-- 27、实时查看
-- ----------------------------
drop table if exists snapshot_real;
CREATE TABLE `snapshot_real` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sn` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '船只sn',
  `channel_code` varchar(20) NOT NULL COMMENT '通道编号',
  `dept_id` bigint NOT NULL COMMENT '部门id',
  `type` bigint NOT NULL DEFAULT '0' COMMENT '分屏类型:0单屏,1四分屏,2九分屏,3十六分屏',
  `mark` bigint NOT NULL COMMENT '下标',
  `status` bigint DEFAULT '0' COMMENT '状态:0关闭，1开启',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- ----------------------------
-- 28、视频回放
-- ----------------------------
drop table if exists snapshot_video;
CREATE TABLE `snapshot_video` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sn` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '船只sn',
  `channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '通道编号',
  `dept_id` bigint NOT NULL COMMENT '部门id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- ----------------------------
-- 29、图片管理
-- ----------------------------
drop table if exists background_pic;
CREATE TABLE `background_pic` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` int DEFAULT NULL COMMENT '类型(备用字段，暂时不适用)',
  `name` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图片名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `bg_status` int DEFAULT NULL COMMENT '图片状态(0:不启用 1：启用)',
  `sort_num` bigint DEFAULT '0' COMMENT '排序',
  `file_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '文件名称',
  `directory` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '目录',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- ----------------------------
-- 30、redis-保存数据
-- ----------------------------
drop table if exists redis_obj;
CREATE TABLE `redis_obj` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `sn` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '服务器sn号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `redis_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'redis的key',
  `value_length` bigint DEFAULT NULL COMMENT '数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB auto_increment=10 comment = 'redis-保存数据';