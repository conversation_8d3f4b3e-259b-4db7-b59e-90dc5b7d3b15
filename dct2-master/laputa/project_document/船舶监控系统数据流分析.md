# 船舶监控系统数据流分析

## 1. 系统概述

该系统是一个船舶监控系统（DCT2），主要用于收集、传输、存储和展示船舶各种设备的数据。系统通过各种传感器和设备采集船舶数据，通过网络传输到岸基系统，并在系统中进行分析、存储和展示。系统使用了Kafka作为消息队列中间件，Redis作为缓存，HBase作为历史数据存储。

## 2. 核心业务流程

系统核心业务是船舶设备数据的采集、传输、存储和展示。具体包括：

1. 设备数据采集：从各种船舶设备（GPS、风速仪、罗经仪等）采集数据
2. 数据传输：通过网络将数据从船舶传输到岸基系统
3. 数据处理：对数据进行解析、处理和存储
4. 数据展示：通过WebSocket等方式将数据实时展示给用户
5. 快照功能：对重要数据进行快照保存
6. 系统同步：岸基系统与船端系统之间的配置同步

## 3. 数据流程图

### 3.1 总体数据流程

```mermaid
flowchart TD
    A[船舶设备] -->|采集数据| B[采集中间件]
    B -->|RAW_DATA_TOPIC| C[Kafka]
    B -->|DEVICE_DATA_TOPIC| C
    B -->|SNAPSHOT_LATEST_TOPIC| C
    C -->|消费消息| D[ConsumerService]
    D -->|设备数据| E[StoreService]
    D -->|快照数据| F[SnapshotLogService]
    D -->|原始数据| G[Redis缓存]
    E -->|处理后数据| H[HBase存储]
    E -->|最新数据| G
    D -->|WebSocket| I[前端实时显示]
    J[岸基系统] -->|SHORE_SYNC_DATA_TOPIC| C
    C -->|同步消息| K[SyncService]
    K -->|处理同步| L[各业务Service]
```

### 3.2 数据消费流程

```mermaid
flowchart LR
    A[Kafka消息] -->|DEVICE_DATA_TOPIC| B[sourceDataConsumer]
    A -->|SNAPSHOT_LATEST_TOPIC| C[snapshotConsumer]
    A -->|RAW_DATA_TOPIC| D[rawDataConsumer]
    B -->|TransferPackage| E[handleMessage]
    B -->|WebSocket| F[实时显示]
    C -->|TransferPackage| G[addLogByTransfer]
    D -->|TransferPackage| H[save2Redis]
    D -->|WebSocket| I[实时显示]
    E -->|处理后数据| J[save2Hbase]
    E -->|最新数据| K[save2Redis]
```

## 4. 时序图

### 4.1 设备数据处理时序

```mermaid
sequenceDiagram
    participant 船舶设备
    participant Kafka
    participant ConsumerService
    participant StoreService
    participant HBase
    participant Redis
    participant WebSocket
    
    船舶设备->>Kafka: 发送设备数据(DEVICE_DATA_TOPIC)
    Kafka->>ConsumerService: sourceDataConsumer消费消息
    ConsumerService->>WebSocket: websocketSendCacheData(实时预览)
    ConsumerService->>StoreService: handleMessage(异步处理)
    StoreService->>StoreService: analysisMessage(解析数据)
    StoreService->>HBase: save2Hbase(存储历史数据)
    StoreService->>Redis: save2Redis(缓存最新数据)
```

### 4.2 原始数据处理时序

```mermaid
sequenceDiagram
    participant 船舶设备
    participant Kafka
    participant ConsumerService
    participant StoreService
    participant Redis
    participant WebSocket
    
    船舶设备->>Kafka: 发送原始数据(RAW_DATA_TOPIC)
    Kafka->>ConsumerService: rawDataConsumer消费消息
    ConsumerService->>StoreService: save2Redis(异步处理)
    ConsumerService->>WebSocket: websocketSendRawCacheData(实时预览)
    StoreService->>Redis: 存储原始数据
```

### 4.3 同步处理时序

```mermaid
sequenceDiagram
    participant 岸基系统
    participant Kafka
    participant SyncService
    participant 业务Services
    
    岸基系统->>Kafka: 发送同步数据(SHORE_SYNC_DATA_TOPIC)
    Kafka->>SyncService: handleMessage消费消息
    SyncService->>SyncService: 解析SyncEntity
    alt transferDevice模块
        SyncService->>业务Services: deviceService.handleBySync
        SyncService->>业务Services: transferAttributeService.updateBySync
    else transferSnapshot模块
        SyncService->>业务Services: snapshotTransferService.handleBySync
        SyncService->>业务Services: snapshotChannelService.handleBySync
    else acquisitionMiddleware模块
        SyncService->>业务Services: acquisitionMiddlewareService.handleBySync
    else syncAll模块
        SyncService->>业务Services: deviceService.handleStatusBySync
        SyncService->>业务Services: snapshotChannelService.handleStatusBySync
    else syncManage模块
        SyncService->>业务Services: shipTerminalManageService.handleBySync
    end
```

## 5. 关键组件与数据走向

### 5.1 Kafka主题说明

系统中使用的Kafka主题包括：

1. **DEVICE_DATA_TOPIC**: 设备数据主题，用于传输船舶设备的处理后数据
2. **RAW_DATA_TOPIC**: 原始数据主题，用于传输设备的原始未处理数据
3. **SNAPSHOT_LATEST_TOPIC**: 快照数据主题，用于传输设备的快照数据
4. **SHORE_SYNC_DATA_TOPIC**: 同步数据主题，用于岸基系统与船端系统间的配置同步

### 5.2 Redis键结构

系统中使用的主要Redis键包括：

1. **LATEST_DATA_{sn}_{deviceCode}**: 存储设备最新数据
2. **LATEST_DATE_{sn}_{deviceCode}**: 存储设备最新数据的时间戳
3. **ALL_ENABLE_SHIP_SN**: 所有启用的船只SN号
4. **ALL_ENABLE_DEVICE_CODE**: 所有启用的设备编码
5. **SHIP_PAZU_IP_{sn}**: 记录不同船的IP地址
6. **SHIP_PAZU_PORT_{sn}**: 记录不同船的端口号

### 5.3 关键类和方法

#### 5.3.1 ConsumerService类 (src/main/java/com/xhjt/project/netty/service/ConsumerService.java)

负责消费Kafka消息并处理数据。主要方法：

- `sourceDataConsumer`: 消费DEVICE_DATA_TOPIC主题的设备数据
- `snapshotConsumer`: 消费SNAPSHOT_LATEST_TOPIC主题的快照数据
- `rawDataConsumer`: 消费RAW_DATA_TOPIC主题的原始数据
- `websocketSendCacheData`: 通过WebSocket发送设备实时数据
- `websocketSendRawCacheData`: 通过WebSocket发送原始实时数据

#### 5.3.2 StoreService类 (src/main/java/com/xhjt/project/netty/service/StoreService.java)

负责数据的处理和存储。主要方法：

- `handleMessage`: 处理设备数据，将数据解析并存储到HBase和Redis
- `analysisMessage`: 解析设备消息数据
- `save2Hbase`: 将数据保存到HBase，支持数据抽稀
- `save2Redis`: 将数据保存到Redis

#### 5.3.3 SyncService类 (src/main/java/com/xhjt/project/netty/service/SyncService.java)

负责处理同步消息。主要方法：

- `handleMessage`: 处理同步消息，根据不同模块类型调用不同的业务服务
- `sendSync2Kafka`: 发送同步数据到Kafka

### 5.4 数据实体类

#### 5.4.1 TransferPackage (com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage)

传输包装类，用于在Kafka中传输数据。主要字段：

- `sn`: 船舶序列号
- `deviceCode`: 设备代码
- `deviceType`: 设备类型
- `time`: 数据时间戳
- `message`: 消息内容
- `isRepair`: 是否为修复数据
- `packageType`: 包类型

#### 5.4.2 DeviceEntity (src/main/java/com/xhjt/project/device/domain/DeviceEntity.java)

设备实体类，表示船舶上的设备。主要字段：

- `id`: 设备ID
- `sn`: 船舶序列号
- `name`: 设备名称
- `type`: 设备类型
- `code`: 设备编码
- `enable`: 启用状态
- `connectStatus`: 连接状态
- `transferStatus`: 传输状态
- `compartment`: 传输间隔

#### 5.4.3 ShipEntity (src/main/java/com/xhjt/project/ship/domain/ShipEntity.java)

船舶实体类。主要字段：

- `shipId`: 船舶ID
- `sn`: 船舶序列号
- `name`: 船名
- `status`: 状态
- `mmsi`: MMSI号
- `callSign`: 呼号
- `imo`: IMO号

## 6. 完整数据流分析

1. **数据采集阶段**：
   - 船舶上的各种设备(GPS、风速仪等)采集数据
   - 数据通过采集中间件采集，由AcquisitionMiddleware管理

2. **数据传输阶段**：
   - 采集到的数据被封装成TransferPackage对象
   - 通过Kafka发送到不同主题：
     - 原始数据 -> RAW_DATA_TOPIC
     - 设备数据 -> DEVICE_DATA_TOPIC
     - 快照数据 -> SNAPSHOT_LATEST_TOPIC

3. **数据消费阶段**：
   - ConsumerService消费不同主题的消息
   - sourceDataConsumer处理设备数据
   - snapshotConsumer处理快照数据
   - rawDataConsumer处理原始数据

4. **数据存储阶段**：
   - 设备数据通过StoreService.handleMessage方法处理
     - 数据解析：根据设备类型和属性解析数据
     - HBase存储：将数据按不同时间粒度存储到HBase
     - Redis缓存：将最新数据缓存到Redis
   - 原始数据直接存储到Redis
   - 快照数据存储到系统数据库

5. **数据展示阶段**：
   - 通过WebSocket将Redis中的实时数据推送到前端
   - 通过API接口查询历史数据

6. **系统同步阶段**：
   - 岸基系统通过SHORE_SYNC_DATA_TOPIC发送同步消息
   - SyncService处理同步消息，更新设备配置、快照配置等

## 7. 总结

该船舶监控系统是一个典型的物联网数据采集和监控系统，通过Kafka、Redis和HBase等中间件实现了数据的高效采集、传输、存储和展示。系统采用了微服务架构，各个模块职责明确，通过消息队列实现了系统的解耦和高可扩展性。

数据流向清晰：从船舶设备采集数据，通过Kafka传输，ConsumerService消费处理，StoreService存储到HBase和Redis，最后通过WebSocket实时展示给用户。同时，系统还支持岸基与船端的配置同步，保证了数据的一致性。 